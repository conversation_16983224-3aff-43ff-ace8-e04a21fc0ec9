2025-10-29 15:05:33,926 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:06:33,939 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:07:34,011 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:08:34,066 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:09:34,236 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:10:34,243 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:11:34,336 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:12:34,421 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:13:34,507 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:14:34,607 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:15:34,757 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:16:34,807 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:17:34,811 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:18:34,855 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:19:34,941 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:20:35,012 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:21:35,135 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:22:35,233 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:23:35,319 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:24:35,327 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:25:35,488 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:26:35,563 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:27:35,608 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:28:35,613 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:29:35,693 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:30:35,897 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:31:35,905 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:32:36,078 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:33:36,084 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:34:36,164 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
2025-10-29 15:35:36,251 ERROR scheduler Exception in Enqueue Events for Site local.westside
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1698, "Access denied for user '_07fdbf6ba2f0be13'@'localhost'")
