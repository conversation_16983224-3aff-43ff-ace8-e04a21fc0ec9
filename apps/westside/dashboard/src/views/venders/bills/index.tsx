"use client";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  ChevronRight,
  ChevronLeft,
  Plus,
  RefreshCcw,
  Eye,
  Search,
} from "lucide-react";
import { format } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { useNavigate } from "react-router-dom";
import { listBills, getVendors } from "@/services/admin/billGenerate";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { useSearchParams } from "react-router-dom";
import { Input } from "@/components/ui/input";

// Dummy Data

const statusList = ["Paid", "Pending", "Overdue"];

const uniqueStatuses = statusList.map((status) => ({
  label: status,
  value: status,
}));

const getStatusBadgeStyle = (status: string) => {
  switch (status.toLowerCase()) {
    case "paid":
      return { backgroundColor: "#D7FDE5", color: "#339D59" };
    case "pending":
      return { backgroundColor: "#FFF6D6", color: "#D39E10" };
    case "overdue":
      return { backgroundColor: "#FFE1E1", color: "#D33232" };
    default:
      return { backgroundColor: "#E5E8EF", color: "#4B5563" };
  }
};

export default function BillsListView() {
  const [searchParams, setSearchParams] = useSearchParams();

  const initialDate = searchParams.get("date")
    ? new Date(searchParams.get("date")!)
    : null;

  const [date, setDate] = useState<Date | null>(initialDate);
  const [statusFilter, setStatusFilter] = useState(
    searchParams.get("status") || ""
  );
  const [vendorFilter, setVendorFilter] = useState(
    searchParams.get("vendor") || ""
  );
  const [search, setSearch] = useState(searchParams.get("search") || "");
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const navigate = useNavigate();

  const rowsPerPage = 10;
  const { data: billDetails } = useQuery({
    queryKey: [
      "fetchListBillsBillDetails",
      vendorFilter,
      statusFilter,
      date,
      search,
    ],
    queryFn: () =>
      listBills({
        ...(vendorFilter && { vendor: vendorFilter }),
        ...(statusFilter && { status: statusFilter }),
        ...(date && { bill_date: format(date, "yyyy-MM-dd") }),
        ...(search && { search: search }),
      }),
  });

  const handleOpenDrawer = (id: number) => {
    setSelectedItemId(id);
    setOpenDrawer(true);
  };

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setSelectedItemId(null);
  };

  const totalCount = billDetails?.data?.length || 0;
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const paginatedData =
    billDetails?.data?.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    ) || [];
  const { data: vendorsData } = useQuery({
    queryKey: ["getVendors"],
    queryFn: () => getVendors(),
  });

  useEffect(() => {
    const newParams = new URLSearchParams();

    if (statusFilter) newParams.set("status", statusFilter);
    if (vendorFilter) newParams.set("vendor", vendorFilter);
    if (search) newParams.set("search", search);
    if (date) newParams.set("date", format(date, "yyyy-MM-dd"));
    newParams.set("page", currentPage.toString());

    setSearchParams(newParams);
  }, [statusFilter, vendorFilter, search, date, currentPage]);
  useEffect(() => {
    const pageParam = parseInt(searchParams.get("page") || "1", 10);
    setCurrentPage(isNaN(pageParam) ? 1 : pageParam);
  }, []);
  // if (!billDetails) {
  //   return <div className="p-6">Loading bill list...</div>;
  // }
  return (
    <div className="">
      {/* Filters */}
      <div className="flex items-center justify-between flex-wrap gap-4 mb-3">
        {/* Left: Filters in one row */}
        <div className="flex flex-wrap items-center gap-3 flex-1">
          <p className="text-sm sm:text-base mt-5">
            {totalCount}{" "}
            <span className="text-gray-500">
              Result{totalCount > 1 ? "s" : ""} Found
            </span>
          </p>
        </div>

        {/* Right: Create Bill Button */}
        <div className="flex gap-4 items-center ml-auto">
          {/* <div className="relative"> */}
          <Input
            className="pr-10"
            placeholder="Search here"
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              setCurrentPage(1);
              setSearchParams((prev) => ({
                ...prev,
                search: e.target.value,
                page: "1",
              }));
            }}
          />
          <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          {/* </div> */}
          <select
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value);
              setCurrentPage(1);
              setSearchParams((prev) => ({
                ...prev,
                status: e.target.value,
                page: "1",
              }));
            }}
            className="h-10 min-w-[150px] border bg-gray-100 text-sm text-gray-700 px-3 rounded-md"
          >
            <option value="">All Statuses</option>
            {uniqueStatuses.map((status, i) => (
              <option key={i} value={status?.value}>
                {status?.label}
              </option>
            ))}
          </select>

          {/* Date Picker */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-10 bg-gray-100 text-sm text-gray-700 px-3 rounded-md"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? (
                  format(date, "dd-MM-yyyy")
                ) : (
                  <span className="text-gray-400">DD-MM-YYYY</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date || undefined}
                onSelect={(day) => {
                  setDate(day || null);
                  setCurrentPage(1);
                  setSearchParams((prev) => ({
                    ...prev,
                    date: day ? format(day, "yyyy-MM-dd") : "",
                    page: "1",
                  }));
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          {/* Clear Filters Button */}
          {(date || search || vendorFilter || statusFilter) && (
            <Button
              variant="outline"
              className="h-10 text-sm text-gray-700"
              onClick={() => {
                setDate(null);
                setSearch("");
                setStatusFilter("");
                setVendorFilter("");
                setSelectedIds([]);
                setSearchParams({});
              }}
            >
              <RefreshCcw className="mr-2 h-4 w-4" /> Clear
            </Button>
          )}
        </div>
      </div>
      {/* Table */}
      <div className="pt-6 overflow-y-auto">
        <ScrollArea className="whitespace-nowrap">
          <Table className="table-auto w-full border-separate border-spacing-y-2">
            <TableHeader>
              <TableRow className="bg-[#D3DAE7] h-[55px]">
                {[
                  "BILL #",
                  "VENDOR BILL #",
                  "BOOKING #",
                  "JOB #",
                  "BILL DATE",
                  "ORIGIN",
                  // "VENDOR",
                  <div className="leading-tight">
                    TOTAL <br />
                    AMOUNT $
                  </div>,
                  <div className="leading-tight">
                    QUICKBOOKS <br />
                    VENDOR NAME
                  </div>,
                  "STATUS",
                  "CREATED DATE",
                  "ACTION",
                ].map((head, idx) => (
                  <TableHead
                    key={idx}
                    className="p-3 text-left font-bold text-[#191C36] pl-6"
                  >
                    {head}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.length > 0 ? (
                paginatedData.map((item: any) => (
                  <TableRow
                    key={item.id}
                    className="  hover:bg-gray-300 transition-all duration-300 transform shadow-md"
                  >
                    {[
                      item.document_number,
                      item?.vendor_bill_no ?? "--",
                      item.booking_id,
                      item.job_id,
                      item.bill_date
                        ? dayjs(item.bill_date).format("MMM-DD-YYYY")
                        : "--",
                      item?.origin ?? "--",
                      // item.vendor,
                      item.total_amount !== undefined &&
                      item.total_amount !== null
                        ? new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD",
                            minimumFractionDigits: 2,
                          }).format(Number(item.total_amount))
                        : "N/A",
                      item.quickbooks_vendor,
                    ].map((val, i) => (
                      <TableCell
                        key={i}
                        className={`p-3 pl-6 ${
                          i === 7
                            ? "break-words whitespace-normal max-w-[160px]"
                            : ""
                        }`}
                      >
                        {val ?? "--"}
                      </TableCell>
                    ))}
                    <TableCell className="p-3 text-bold pl-6">
                      <span
                        className="px-3 py-1 text-sm font-medium rounded-full capitalize whitespace-nowrap"
                        style={getStatusBadgeStyle(item.status)}
                      >
                        {item.status}
                      </span>
                    </TableCell>
                    <TableCell className="p-3  pl-6 max-w-[150px]">
                      {item.creation
                        ? dayjs(item.creation).format("MMM-DD-YYYY")
                        : "--"}
                    </TableCell>
                    <TableCell className="p-3 text-bold  pl-6">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="bg-primary text-white rounded-sm px-3 py-2"
                          onClick={() =>
                            navigate(
                              `/dashboard/bill-view/${item.name}${
                                item?.job_id ? `?jobId=${item.job_id}` : ""
                              }`
                            )
                          }
                        >
                          <Eye />
                        </Button>
                        {/* <Button
                  size="icon"
                  className="bg-primary text-white rounded-sm px-3 py-2"
                >
                  <img src={IconDownload} alt="icon" />
                </Button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="p-3 text-center text-gray-500"
                  >
                    No bills found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => setCurrentPage(totalPages)}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
