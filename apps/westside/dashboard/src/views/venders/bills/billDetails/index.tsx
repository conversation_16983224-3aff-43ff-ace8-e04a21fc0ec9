// This is the VIEW version of the bill form
// All inputs, dropdowns, buttons are replaced with plain text or layout elements

import React from "react";
import { Card } from "@/components/ui/card";
import {
  getBillDetails,
  generateBillPDF,
  handleMakeBillPayment,
} from "@/services/admin/billGenerate";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { Image, Save, FileText, Eye } from "lucide-react";
import { ViewAttachedFiles } from "./viewAttachedFile";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import dayjs from "dayjs";

const ViewBillPageVendor = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const jobId = params.get("jobId");
  const {
    data: billData,
    isFetching: isBillFetching,
    refetch: billDetailRefetch,
  } = useQuery({
    queryKey: ["fetchBillDetails", { billId: id }],
    queryFn: () => getBillDetails(id as string),
    enabled: !!id,
  });

  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{
    name: string;
    license: string;
  } | null>(null);
  const handleFileClick = (file) => {
    setSelectedFile({
      name: file.file,
      license: file.file_url,
    });
    setPreviewOpen(true);
  };
  const { mutate: triggerGeneratePDF, isPending: isGeneratingPDF } =
    useMutation({
      mutationFn: (docname: string) => generateBillPDF(docname),
      onSuccess: (res) => {
        if (res?.status_code === "200") {
          toast.success(res?.message || "PDF generated successfully");
          const fileUrl = res?.file_url;
          if (fileUrl) {
            // Open in new tab
            const fullUrl = `${`${
              import.meta.env.VITE_BACKEND_URL
            }/${fileUrl}`}`;
            window.open(fullUrl, "_blank");
          }
          // refetchInvoice();
        } else {
          toast.error(res?.message?.error || "Failed to generate PDF");
        }
      },
      onError: (error) => {
        toast.error("An error occurred while generating the PDF");
        console.error(error);
      },
    });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPaymentType, setSelectedPaymentType] = useState("Cash");
  const [selectedBillId, setSelectedBillId] = useState<string | null>(null);
  const [notes, setNotes] = useState("");

  const handleChangeStatusBill = (billId: string, payment_type: string) => {
    if (billId) {
      triggerChangeStatus({ billId, payment_type });
    }
  };

  const { mutate: triggerChangeStatus, isPending: isChangeBillStatus } =
    useMutation({
      mutationFn: ({
        billId,
        payment_type,
      }: {
        billId: string;
        payment_type: string;
        notes?: string;
      }) => handleMakeBillPayment(billId, payment_type, notes),

      onSuccess: (res) => {
        if (res?.message?.status_code === "200") {
          toast.success(
            res?.message?.message ||
              "Bill marked as paid in QuickBooks successfully."
          );
          billDetailRefetch();
        } else {
          toast.error(res?.message?.error || "Failed to change status");
        }
      },

      onError: (error) => {
        toast.error("An error occurred while changing bill status");
        console.error(error);
      },
    });

  const handleGeneratePDF = (docname: string) => {
    if (docname) triggerGeneratePDF(docname);
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Left Section */}
        <Card className="md:col-span-3 p-6 space-y-6">
          <h2 className="text-lg font-semibold text-orange-600">
            Bill Details
          </h2>
          <hr className="border-gray-300 mt-0" />
          {/* <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Vendor Name
            </label>
            <p>{billData?.data.vendor || "-"}</p>
          </div> */}

          {/* <div className="grid md:grid-cols-3 gap-4">
            <ViewField label="Bill Date" value={billData?.data.bill_date} />
            <ViewField label="Booking #" value={billData?.data.bookingNo} />
            <ViewField label="HS Code" value={billData?.data.hsCode} />
            <ViewField
              label="Origin Port"
              value={billData?.data.originPort?.location}
            />
            <ViewField
              label="Destination Port"
              value={billData?.data.destinationPort?.location}
            />
            <ViewField label="BOL #" value={billData?.data.bol_number} />
            <ViewField
              label="Shipping Date"
              value={billData?.data.shippingDate}
            />
            <ViewField label="Due Date" value={billData?.data.dueDate} />
          </div> */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Vendor Name:</strong> {billData?.data.vendor}
            </div>
            
            <div>
              <strong>Bill Number:</strong> {billData?.data.document_number}
            </div>
            <div>
              <strong>Bill Date:</strong> {billData?.data.bill_date ? dayjs(billData?.data.bill_date).format("MMM-DD-YYYY") : "--"}
            </div>
            <div>
              <strong>Vendor Bill Number:</strong> {billData?.data.vendor_bill_no}
            </div>
            <div>
              <strong>Booking #:</strong> {billData?.data.booking_id}
            </div>
            <div>
              <strong>HS Code:</strong> {billData?.data.hs_code}
            </div>
            <div>
              <strong>Origin Port:</strong> {billData?.data.origin_port}
            </div>
            <div>
              <strong>Destination Port:</strong>{" "}
              {billData?.data.destination_port}
            </div>
            <div>
              <strong>BOL #:</strong> {billData?.data.bol_number}
            </div>
            <div>
              <strong>Shipping Date:</strong> {billData?.data.shipping_date ? dayjs(billData?.data.shipping_date).format("MMM-DD-YYYY") : "--"}
            </div>
            <div>
              <strong>Due Date:</strong> {billData?.data.due_date ? dayjs(billData?.data.due_date).format("MMM-DD-YYYY") : "--"}
            </div>
          </div>
          <h3 className="text-lg font-medium mt-6">Category Details</h3>
          <div className="w-full overflow-x-auto">
            <table className="min-w-[500px] text-sm w-full border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="text-left px-3 py-2">Category</th>
                  <th className="text-left px-3 py-2">Description</th>
                  <th className="text-left px-3 py-2">Amount</th>
                </tr>
              </thead>
              <tbody>
                {billData?.data.category_details?.map((cat, idx) => (
                  <tr key={idx} className="border-t">
                    <td className="px-3 py-2">{cat.category}</td>
                    <td className="px-3 py-2">{cat.description}</td>
                    <td className="px-3 py-2">{cat.amount}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <h3 className="text-lg font-medium mt-6">Items Details</h3>
          <div className="w-full overflow-x-auto">
            <table className="min-w-[900px] text-sm w-full border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="text-left px-3 py-2">Product/Services</th>
                  <th className="text-left px-3 py-2">Description</th>
                  <th className="text-left px-3 py-2">Qty</th>
                  <th className="text-left px-3 py-2">UOM</th>
                  <th className="text-left px-3 py-2">Rate</th>
                  <th className="text-left px-3 py-2">Amount</th>
                </tr>
              </thead>
              <tbody>
                {billData?.data.item_details?.map((item, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-3 py-2">{item.product_service ?? "-"}</td>
                    <td className="px-3 py-2">{item.description ?? "-"}</td>
                    <td className="px-3 py-2">{item.quantity ?? "-"}</td>
                    <td className="px-3 py-2">{item.uom ?? "-"}</td>
                    <td className="px-3 py-2">{item.rate ?? "-"}</td>
                    <td className="px-3 py-2">{item.amount ?? "-"}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="mt-4">
            <strong>Comments:</strong>
            <p className="text-sm text-gray-800 whitespace-pre-line mt-1">
              {billData?.data?.memo || "-"}
            </p>
          </div>
          {/* <div className="space-y-2">
            <label className="block text-sm font-medium">Attached Files</label>
            {billData?.data.attachments?.length > 0 ? (
              billData.data.attachments.map((file, index) => (
                <div key={index} className="text-sm text-blue-600 underline">
                  {file.name}
                </div>
              ))
            ) : (
              <p>-</p>
            )}
          </div> */}

          <div className="space-y-2 mt-4">
            <strong className="block text-sm font-medium text-gray-700">
              Attached Files
            </strong>
            {billData?.data.attachments?.length > 0 ? (
              billData.data.attachments.map((file, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <Image className="w-5 h-5 text-gray-600" />
                  <span
                    onClick={() => handleFileClick(file)}
                    className="text-blue-600 hover:underline cursor-pointer"
                  >
                    {file.file}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No attachments</p>
            )}
          </div>
        </Card>

        {/* Right Section */}
        <Card className="p-6 space-y-4">
          <h2 className="text-lg font-semibold text-orange-600">
            Vendor Details
          </h2>
          <hr className="border-gray-300 mt-1" />
          <div className="text-sm space-y-2">
            <div>
              <strong>Vendor in QuickBooks:</strong>{" "}
              {billData?.data.quickbooks_vendor_display_name || "-"}
            </div>
            <div>
              <strong>Bill To:</strong>{" "}
              <pre className="whitespace-pre-wrap inline">
                {billData?.data.qb_bill_to || "-"}
              </pre>
            </div>
            <div>
              <strong>Email:</strong> {billData?.data.qb_vendor_email || "-"}
            </div>
            <div>
              <strong>Contact:</strong>{" "}
              {billData?.data.qb_vendor_contact || "-"}
            </div>
          </div>

          <div className="space-y-2">
            {/* <div className="flex justify-between">
              <span className="font-semibold">Sub Total</span>
              <span>{billData.subTotal || "0.00"}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-semibold">Tax (18%)</span>
              <span>{billData.tax || "0.00"}</span>
            </div> */}
            <hr />
            <div className="flex justify-between">
              <span className="font-semibold">Total Amount</span>
              <span>{billData?.data.total_amount || "0.00"}</span>
            </div>
          </div>

          <Button
            type="button"
            className="w-full cursor-pointer"
            onClick={() => handleGeneratePDF(id)}
            disabled={isGeneratingPDF}
          >
            <FileText size={20} className="mr-2" />
            {isGeneratingPDF ? "Loading..." : "View Bill PDF"}
          </Button>
          {billData?.data?.status && billData?.data?.status !== "Paid" && (
            <Button
              type="button"
              className="w-full cursor-pointer"
              onClick={() => {
                setSelectedBillId(billData?.data?.qb_bill_id as string); // Save selected bill
                setSelectedPaymentType("Cash"); // Reset default
                setIsDialogOpen(true); // Open dialog
              }}
              disabled={isChangeBillStatus}
            >
              Mark Bill as Paid
            </Button>
          )}
          {billData?.data?.status === "Paid" && (
            <Button
              type="button"
              className="px-4 py-1 bg-orange-500 text-white text-sm font-semibold border border-orange-700 cursor-default"
              disabled
            >
              Paid
            </Button>
          )}

          {jobId && (
            <Button
              type="button"
              className="w-full mt-1 cursor-pointer"
              onClick={() => navigate(`/dashboard/vendors/job-view/${jobId}`)}
            >
              <Eye size={20} className="mr-2" />
              Back To Job
            </Button>
          )}

          <button
            onClick={() => navigate(-1)}
            className="mb-2 px-4 py-2 bg-gray-200 text-sm rounded hover:bg-gray-300 transition"
          >
            ← Back
          </button>
        </Card>

        <ViewAttachedFiles
          open={previewOpen}
          selectedFile={selectedFile}
          onOpenChange={setPreviewOpen}
          setSelectedFile={setSelectedFile}
        />
        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Bill Payment</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to mark this bill as paid?
              </AlertDialogDescription>
            </AlertDialogHeader>

            <fieldset className="my-4">
              <legend className="mb-2 font-medium">Payment Type</legend>
              <div className="space-y-2">
                {["Check", "Cash", "CreditCard"].map((type) => (
                  <label key={type} className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="paymentType"
                      value={type}
                      checked={selectedPaymentType === type}
                      onChange={() => setSelectedPaymentType(type)}
                    />
                    {type === "CreditCard" ? "Credit Card" : type}
                  </label>
                ))}
              </div>
            </fieldset>
            <fieldset className="my-4">
              <legend className="mb-2 font-medium">Notes:</legend>
              <div><textarea className="w-full border border-gray-300 rounded-md p-2" placeholder="Enter notes" name="notes" onChange={(e) => setNotes(e.target.value)}></textarea></div>
            </fieldset>

            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (selectedBillId) {
                    handleChangeStatusBill(selectedBillId, selectedPaymentType);
                    setIsDialogOpen(false);
                  }
                }}
              >
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default ViewBillPageVendor;
