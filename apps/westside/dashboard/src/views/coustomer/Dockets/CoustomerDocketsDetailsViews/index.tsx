import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, Package, MessageSquareCode, Undo2 } from "lucide-react";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import PDF from "../../../../assets/img/pdf.svg";
import ImageFile from "../../../../assets/img/imageFile.svg";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import CommentDrawerDocket from "./commentDrawerDocket";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getCoustomerDocketsDetails,
  getCoustomerDocketsRevitionDetails,
} from "@/services/customer/getDocketDeatilsByDocketId";
import { useParams } from "react-router-dom";
import Loader from "@/components/Loader";
import { docketStatusChange } from "@/services/customer/docketStatus";
import { toast } from "sonner";
import { format } from "date-fns";

interface DocketRevision {
  revision_name: string;
  revision_number: string;
  creation: string;
  status: string;
  show_to_customer: number;
  bill_of_ladding?: string;
  certificate_of_origin?: string;
  form6?: string;
  form9?: string;
  packing_list?: string;
  invoice?: string;
  isccplus_self_declaration?: string;
  iscc_self_declaration?: string;
  additional_attachments?: Array<{ file_name: string; url: string }>;
}

interface DocketDetails {
  docket_id?: string;
  carrier_booking_number?: string;
  revision_number?: string;
  blno?: string;
  origin?: string;
  status?: string;
  revisions?: DocketRevision[];
  revision_id?: string;
  revisionNumber?: string;
}

const CustomerDocketsDetailsViews = () => {
  const [selectedRevision, setSelectedRevision] = useState("");
  const [selectedRevisionId, setSelectedRevisionId] = useState("");
  const [selectedRevisionNumber, setSelectedRevisionNumber] = useState("");
  const [commentDrawerOpen, setCommentDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("attachments");
  const [actionType, setActionType] = useState<"reject" | "reopen">("reject");
  const queryClient = useQueryClient();

  const { id } = useParams<{ id: string }>();

  const { data, isLoading, isError } = useQuery<{
    message: { data: DocketDetails };
  }>({
    queryKey: ["docketCustomerDetails", id],
    queryFn: () => getCoustomerDocketsDetails(String(id)),
    enabled: !!id,
  });

  const datas = data?.message?.data || {};

  const {
    data: revisionsData,
    isLoading: revisionsLoading,
    isError: revisionsError,
  } = useQuery<{ message: { revisions: DocketRevision[] } }>({
    queryKey: ["docketCustomerRevisions", id],
    queryFn: () => getCoustomerDocketsRevitionDetails(String(id)),
    enabled: !!id,
  });

  // Filter revisions to only show those with show_to_customer === 1
  const revisions = (revisionsData?.message?.revisions || []).filter(
    (revision) => revision.show_to_customer === 1
  );

  const hasMultipleRevisions = parseInt(datas?.revision_number || "0") > 1;

  const handleStatusChange = async (
    status: "Accepted" | "Rejected" | "Reopen",
    revisionNumber?: string
  ) => {
    if (!id) return;

    try {
      await docketStatusChange({
        docketId: id,
        status: status,
        revision_id: revisionNumber ?? ""
      });

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ["docketCustomerDetails", id],
        }),
        queryClient.invalidateQueries({
          queryKey: ["docketCustomerRevisions", id],
        }),
      ]);

      toast.success(`Docket ${status} successfully!`);
      
    } catch (error) {
      console.error("Error changing docket status:", error);
      toast.error("Failed to change docket status");
    }
  };

  const handleApproveConfirmation = (revisionNumber: string) => {
    toast("Are you sure you want to approve this revision?", {
      action: {
        label: "Yes",
        onClick: () => handleStatusChange("Accepted", revisionNumber),
      },
      cancel: {
        label: "No",
        onClick: () => {},
      },
      duration: 10000,
    });
  };

  const handleRejectClick = (revisionNumber: string, revisionId: string) => {
    setActionType("reject");
    setSelectedRevisionId(revisionId);
    setSelectedRevisionNumber(revisionNumber);
    setCommentDrawerOpen(true);
  };

  const handleReopenClick = (revisionNumber: string, revisionId: string) => {
    setActionType("reopen");
    setSelectedRevisionId(revisionId);
    setSelectedRevisionNumber(revisionNumber);
    setCommentDrawerOpen(true);
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  const getStatusColorGradient = (status: string) => {
  switch (status) {
    case "New":
      return "#DBEAFE"; 
    case "Rejected":
      return "#FEE2E2"; 
    case "Accepted":
      return "#DCFCE7"; 
    case "Acknowledged":
      return "#FFEDD5"; 
    case "Reopen":
      return "#FEF9C3"; 
    case "Open":
      return "#EDE9FE"; 
    case "Sent":
      return "#E9E294"; 
    case "Revised":
      return "#E0F7FA";
    default:
      return "#F3F4F6"; 
  }
};

const getStatusTextColor = (status: string) => {
  switch (status) {
    case "New":
      return "#1D4ED8"; 
    case "Rejected":
      return "#B91C1C"; 
    case "Accepted":
      return "#15803D"; 
    case "Acknowledged":
      return "#C2410C"; 
    case "Reopen":
      return "#A16207"; 
    case "Open":
      return "#6D28D9"; 
    case "Sent":
      return "#2F855A"; 
    case "Revised":
      return "#17A2B8"; 
    default:
      return "#374151"; 
  }
};

const getStatusBorderColor = (status: string) => {
  switch (status) {
    case "New":
      return "#3B82F6"; 
    case "Rejected":
      return "#EF4444"; 
    case "Accepted":
      return "#22C55E"; 
    case "Acknowledged":
      return "#F97316"; 
    case "Reopen":
      return "#EAB308"; 
    case "Open":
      return "#8B5CF6"; 
    case "Sent":
      return "#2F855A"; 
    case "Revised":
      return "#17A2B8"; 
    default:
      return "#9CA3AF"; 
  }
};


  const getFileIcon = (fileName: string) => {
    return fileName?.toLowerCase().endsWith(".pdf") ? PDF : ImageFile;
  };

  if (isLoading || revisionsLoading) {
    return <Loader />;
  }

  if (isError || revisionsError) {
    return <Loader />;
  }

  const Status = datas?.status || "Open";
  const displayStatus = hasMultipleRevisions
    ? "Revised"
    : Status === "Sent"
    ? "Open"
    : Status || "N/A";

  return (
    <div className="w-full pb-6 md:pb-20 px-2 sm:px-4 md:px-6 lg:px-8 max-w-9xl mx-auto">
      <Card className="w-full">
        <CardContent className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 p-2 sm:p-4 text-sm">
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <p className="text-xs sm:text-sm">Dockets ID</p>
            <p className="font-semibold truncate text-xs sm:text-sm">
              {datas?.docket_id || "N/A"}
            </p>
          </div>
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <p className="text-xs sm:text-sm">Booking ID</p>
            <p className="font-semibold truncate text-xs sm:text-sm">
              {datas?.carrier_booking_number || "N/A"}
            </p>
          </div>
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <p className="text-xs sm:text-sm">No Of Revision</p>
            <p className="font-semibold truncate text-xs sm:text-sm">
              {datas?.revision_number || "0"}
            </p>
          </div>
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <p className="text-xs sm:text-sm">B/L NO</p>
            <p className="font-semibold truncate text-xs sm:text-sm">
              {datas?.blno || "N/A"}
            </p>
          </div>
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <p className="text-xs sm:text-sm">Port of Origin</p>
            <p className="font-semibold truncate text-xs sm:text-sm">
              {datas?.origin || "N/A"}
            </p>
          </div>
          <div className="min-w-0 px-1 sm:px-2 text-center">
            <div
              className="inline-flex items-center justify-center px-2 py-1 sm:px-3 rounded-full text-xs font-medium truncate"
              style={{
                backgroundColor: getStatusColorGradient(displayStatus),
                color: getStatusTextColor(displayStatus),
                border: `1px solid ${getStatusBorderColor(displayStatus)}`,
                minWidth: "5rem",
                maxWidth: "10rem",
              }}
            >
              {displayStatus === "Reopen" ? "Reopened" : displayStatus}
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="pt-4">
        <h2 className="text-xl sm:text-2xl md:text-3xl font-bold">
          Attachment
        </h2>
        <TabsContent
          value="attachments"
          className="pt-4 space-y-4 sm:space-y-6"
        >
          <div className="space-y-3 sm:space-y-4">
            {revisions.length > 0 ? (
              revisions.map((docket, index) => {
                const createdDate = formatDate(docket.creation);

                const standardFiles = [
                  { name: "Bill of Lading", file: docket.bill_of_ladding },
                  {
                    name: "Certificate of Origin",
                    file: docket.certificate_of_origin,
                  },
                  { name: "Form 6", file: docket.form6 },
                  { name: "Form 9", file: docket.form9 },
                  { name: "Packing List", file: docket.packing_list },
                  { name: "Invoice", file: docket.invoice },
                  {
                    name: "ISCC PLUS Self-Declaration",
                    file: docket.isccplus_self_declaration,
                  },
                  {
                    name: "ISCC Self-Declaration",
                    file: docket.iscc_self_declaration,
                  },
                ].filter((file) => file.file);

                const additionalFiles =
                  docket.additional_attachments?.map((attachment: any) => ({
                    name: attachment.file_name || "Additional File",
                    file: attachment.url,
                  })) || [];

                const files = [...standardFiles, ...additionalFiles];

                return (
                  <div key={index} className="border-2 rounded-lg p-3 sm:p-4">
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value={`item-${index}`}>
                        <div className="flex flex-col sm:flex-row justify-between w-full text-left gap-2 sm:gap-0">
                          <div>
                            <div className="flex items-center gap-2 sm:gap-3 font-medium text-gray-800">
                              <Package className="text-primary w-4 h-4 sm:w-5 sm:h-5" />
                              <span className="text-sm sm:text-base">
                                Revision {docket.revision_number}
                              </span>
                            </div>
                            <div className="text-xs text-gray-500 pl-6 sm:pl-8">
                              {`Created on ${createdDate}`}
                            </div>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end gap-1 sm:gap-2">
                            {docket.status !== "Accepted" &&
                            docket.status !== "Rejected" &&
                            docket.status !== "Reopen" ? (
                              <div className="flex gap-1 sm:gap-3">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-green-400 border border-green-300 hover:text-green-500 hover:border-green-300 transition-colors rounded-full text-xs sm:text-sm px-2 sm:px-4"
                                  onClick={() =>
                                    handleApproveConfirmation(
                                      docket.revision_number
                                    )
                                  }
                                >
                                  Approve
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-orange-300 border border-orange-300 hover:text-orange-500 hover:border-orange-300 transition-colors rounded-full text-xs sm:text-sm px-2 sm:px-4"
                                  onClick={() =>
                                    handleRejectClick(
                                      docket.revision_number,
                                      docket.revision_name
                                    )
                                  }
                                >
                                  Reject
                                </Button>
                              </div>
                            ) : docket.status === "Accepted" ? (
                              <Button
                                variant="default"
                                size="sm"
                                className="hover:text-white-500 hover:border-orange-300 transition-colors rounded-meduim text-xs sm:text-sm px-2 sm:px-4 cursor-pointer"
                                onClick={() =>
                                  handleReopenClick(
                                    docket.revision_number,
                                    docket.revision_name
                                  )
                                }
                              >
                                 <Undo2 className="w-4 h-4" />
                                Reopen
                              </Button>
                            ) : (
                              <div
                                className={`inline-flex items-center justify-center px-2 py-1 sm:px-3 rounded-full text-xs font-medium ${
                                  docket.status === "Accepted"
                                    ? "bg-green-100 text-green-800 border border-green-300 w-20 sm:w-24"
                                    : docket.status === "Rejected"
                                    ? "bg-red-100 text-red-800 border border-red-300 w-20 sm:w-24"
                                    : "bg-green-100 text-green-800 border border-green-300 w-32 sm:w-40"
                                }`}
                              >
                                {docket.status === "Reopen"
                                  ? "Reopend"
                                  : docket.status}
                              </div>
                            )}

                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-400 hover:text-gray-600 p-1 sm:p-2"
                              onClick={() => {
                                setSelectedRevisionId(docket.revision_name);
                                setSelectedRevisionNumber(
                                  docket.revision_number
                                );
                                setCommentDrawerOpen(true);
                              }}
                            >
                              <MessageSquareCode className="w-3 h-3 sm:w-4 sm:h-4" />
                            </Button>
                            <AccordionTrigger className="hover:no-underline p-1 sm:p-2" />
                          </div>
                        </div>

                        <AccordionContent className="space-y-2 sm:space-y-3 pt-3 sm:pt-4">
                          {files.length > 0 ? (
                            files.map((file, i) => (
                              <div
                                key={i}
                                className="flex items-center justify-between border-t pt-2 sm:pt-3 text-xs sm:text-sm"
                              >
                                <div className="flex items-center gap-2 sm:gap-3 text-gray-700">
                                  <img
                                    src={getFileIcon(file.file)}
                                    alt={file.name}
                                    className="w-3 h-3 sm:w-4 sm:h-4"
                                  />
                                  <div className="flex flex-col">
                                    <span className="text-xs sm:text-sm">
                                      {file.name}
                                    </span>
                                    <span className="text-xs text-gray-400 truncate max-w-[120px] sm:max-w-[200px]">
                                      {file.file?.split("/").pop()}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1 sm:gap-2 mr-1 sm:mr-5">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-gray-400 border border-gray-300 rounded-md hover:text-orange-500 hover:border-orange-500 p-1 sm:p-2"
                                    onClick={() =>
                                      window.open(file.file, "_blank")
                                    }
                                  >
                                    <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                                  </Button>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-xs sm:text-sm text-gray-500 italic">
                              No files available in this revision
                            </div>
                          )}
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>
                );
              })
            ) : (
              <div className="flex flex-col items-center justify-center p-4 sm:p-8 border-2 rounded-lg">
                <Package className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400 mb-2 sm:mb-4" />
                <p className="text-gray-500 text-sm sm:text-lg">
                  No revisions available
                </p>
                <p className="text-gray-400 text-xs sm:text-sm text-center">
                  There are no document revisions to display
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      <CommentDrawerDocket
        open={commentDrawerOpen}
        onOpenChange={setCommentDrawerOpen}
        revisionName={selectedRevision}
        revisionNumber={selectedRevisionNumber}
        revisionId={selectedRevisionId}
        onReject={handleStatusChange}
        onReopen={handleStatusChange}
        actionType={actionType}
      />
    </div>
  );
};

export default CustomerDocketsDetailsViews;