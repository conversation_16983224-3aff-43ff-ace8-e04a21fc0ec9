import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { ChevronLeft, ChevronRight, FileSearch, Search,RefreshCcw } from "lucide-react";

import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Loader from "@/components/Loader";
import { fetchAllCustomerDockets } from "@/services/customer/getAllDockets";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat.js";


type Docket = {
  id: string;
  booking_id?: string;
  port_of_origin?: { location_name?: string } | string;
  place_of_receipt?: { location_name?: string } | string;
  status?: string;
  // Add other fields as needed
};

type DocketsResponse = {
  message?: {
    docket?: Docket[];
    total_count?: number;
    // Add other fields as needed
  };
};

const CoustomerDocketsList = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const initialStatus = searchParams.get("status") || "all";
  const initialSearch = searchParams.get("search") || "";
  const initialPage = Number(searchParams.get("page")) || 1;

  const [page, setPage] = useState(initialPage);
  const pageSize = 10;
  const [searchTerm, setSearchTerm] = useState(initialSearch);
  const [statusFilter, setStatusFilter] = useState(initialStatus);
  dayjs.extend(customParseFormat);

  const { data, isLoading, isError, error } = useQuery<DocketsResponse, Error>({
    queryKey: [
      "dockets",
      {
        status: statusFilter === "all" ? "" : statusFilter,
        search: searchTerm,
        page,
        page_size: pageSize,
      },
    ],
    queryFn: () =>
      fetchAllCustomerDockets({
        status: statusFilter === "all" ? "" : statusFilter,
        search: searchTerm,
        page: page,
        page_size: pageSize.toString(),
      }) as Promise<DocketsResponse>,
    keepPreviousData: true,
  });

  const Docket_list = data?.message?.docket || [];

  const totalCount = data?.message?.total_count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
  };

const getStatusColorGradient = (status: string) => {
  const displayStatus = getDisplayStatus(status);
  
  switch (displayStatus) {
    case "New":
        return "#DBEAFE";
      case "Rejected":
        return "#FEE2E2";
      case "Accepted":
        return "#DCFCE7";
      case "Acknowledged":
        return "#FFEDD5";
      case "Reopen":
        return "#FEF9C3";
      case "Open":
        return "#EDE9FE";
      case "Sent":
        return "#E9E294";
      case "Revised":
        return "#b6d4e3";
      default:
        return "#F3F4F6";
    }
};

const getStatusTextColor = (status: string) => {
  const displayStatus = getDisplayStatus(status);
  
  switch (displayStatus) {
     case "New":
        return "#1D4ED8";
      case "Rejected":
        return "#B91C1C";
      case "Accepted":
        return "#15803D";
      case "Acknowledged":
        return "#C2410C";
      case "Reopen":
        return "#A16207";
      case "Open":
        return "#6D28D9";
      case "Sent":
        return "#cceded";
      case "Revised":
        return "#1782b8";
      default:
        return "#374151";
    }
};

const getStatusBorderColor = (status: string) => {
  const displayStatus = getDisplayStatus(status);
  
  switch (displayStatus) {
    case "New":
        return "#3B82F6";
      case "Rejected":
        return "#EF4444";
      case "Accepted":
        return "#22C55E";
      case "Acknowledged":
        return "#F97316";
      case "Reopen":
        return "#EAB308";
      case "Open":
        return "#8B5CF6";
      case "Sent":
        return "#2f9696";
      case "Revised":
        return "#1782b8";
      default:
        return "#9CA3AF";
    }
};

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === "N/A") return "N/A";
    try {
      const date = new Date(dateString);
      const month = date.toLocaleString("default", { month: "short" });
      const day = date.getDate().toString().padStart(2, "0");
      const year = date.getFullYear();
      return `${month}-${day}-${year}`;
    } catch (e) {
      return dateString.split(" ")[0];
    }
  };

  const getDisplayStatus = (status: string) => {
    if (status === "Open") return "New";
    if (status === "Sent") return "Open";
    return status || "N/A";
  };

  // if (isLoading) {
  //   return <div>Loading...</div>;
  // }

  
  // Pagination logic
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const half = Math.floor(maxVisiblePages / 2);
      let start = Math.max(1, page - half);
      let end = Math.min(totalPages, start + maxVisiblePages - 1);

      if (end - start + 1 < maxVisiblePages) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (start > 1) {
        pages.unshift("...");
        pages.unshift(1);
      }

      if (end < totalPages) {
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  useEffect(() => {
      const params: Record<string, string> = {};
  
      if (statusFilter && statusFilter !== "All") params.status = statusFilter;
      if (searchTerm) params.search = searchTerm;
      if (page > 1) params.page = page.toString();
  
      setSearchParams(params, { replace: true });
    }, [statusFilter, searchTerm, page, setSearchParams]);
    if (isError) {
    return (
      <div>
        Error: {error instanceof Error ? error.message : "Unknown error"}
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex gap-4 items-center ml-auto">
          <div className="relative">
            <Input
              className="pr-10"
              placeholder="Search here"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          </div>

          <div className="relative flex">
            <Select onValueChange={handleStatusChange} value={statusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Select status</SelectLabel>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="Open">New</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                  <SelectItem value="Acknowledged">Acknowledged</SelectItem>
                  <SelectItem value="Sent">Open</SelectItem>
                  {/* <SelectItem value="Sent">Sent</SelectItem> */}
                  <SelectItem value="Accepted">Accepted</SelectItem>
                  <SelectItem value="Reopen">Reopened</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="relative flex">
            {(searchTerm || statusFilter !== 'all') && (
              <Button
                variant="outline"
                className="h-11"
                onClick={() => {
                  setSearchTerm("");
                  // setSearchParams({});
                  // setCurrentPage(1);
                  setStatusFilter("all");
                }}
              >
                <RefreshCcw /> Clear Filters
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="pt-6 overflow-y-auto">
        
          <ScrollArea className="min-w-full">
            <Table className="table-auto border-1 border-[#D3DAE7] w-full">
              <TableHeader>
                <TableRow className="bg-[#D3DAE7] h-[55px]">
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Dockets #
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Booking #
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6 w-[8%] ">
                    Port Of Origin
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6 w-[8%] ">
                    Port Of Discharge
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Container Qty
                  </TableHead>
                  {/* <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Sail Date
                  </TableHead> */}
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Carrier
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    BOL #
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    ETA
                  </TableHead>
                   <TableHead className="p-2 font-bold text-[#191C36]">
                    Invoice #
                  </TableHead>
                  <TableHead className="p-2 font-bold text-[#191C36]">
                    <div className="flex flex-col leading-tight text-sm">
                      <span>Invoice</span>
                      <span>Total</span>
                    </div>
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Revision #
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Weight
                  </TableHead>
                  <TableHead className="p-2 font-bold text-[#191C36] min-w-[90px]">
                    Send Doc Date
                  </TableHead>
                  <TableHead className="p-2 font-bold text-[#191C36] min-w-[90px]">
                    Created At
                  </TableHead>
                  <TableHead className="p-2 font-bold text-[#191C36] min-w-[90px]">
                    Modified At
                  </TableHead>
                  <TableHead className="p-2 font-bold text-[#191C36] min-w-[90px]">
                    Modified By
                  </TableHead>
                  <TableHead className="p-3 text-left font-bold text-[#191C36] pl-6">
                    Status
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="border-b border-gray-300 h-[75px]">
                {Docket_list && Docket_list.length > 0 ? (
                  Docket_list.map((item: any) => (
                    <TableRow
                      key={item.id}
                      className="border border-[#D3DAE7] hover:bg-gray-300 transition-all duration-300 transform shadow-md cursor-pointer"
                      style={{ border: `2px solid #D3DAE7` }}
                      onClick={() =>
                        navigate(
                          `/dashboard/customer/dockets-Details-View/${item.id}`
                        )
                      }
                    >
                      {/* <TableCell className="pl-6 p-3 border-t-2 border-l-2 border-b-2"></TableCell> */}
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.id || "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.carrier_booking_number || "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6 break-words whitespace-normal">
                        <span className="break-words whitespace-normal">
                        {item.port_of_origin?.location_name ||
                          item.port_of_origin ||
                          "N/A"}
                          </span>
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6 break-words whitespace-normal">
                        <span className="break-words whitespace-normal">
                        {item.place_of_receipt?.location_name ||
                          item.place_of_receipt ||
                          "N/A"}
                          </span>
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.container_qty || "N/A"}
                      </TableCell>
                      {/* <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {formatDate(item.shipping_date)}
                      </TableCell> */}
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.carrier || "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.bol_number || "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {formatDate(item.ETA) || "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.invoice_no|| "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.invoice_total !== undefined &&
                        item.invoice_total !== null
                          ? new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "USD",
                              minimumFractionDigits: 2,
                            }).format(Number(item.invoice_total))
                          : "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.revision_number|| "N/A"}
                      </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                        {item.weight|| "--"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                                              {item.send_doc_date ? (
                                                <>
                                                  <span>
                                                    {dayjs(item.send_doc_date.replace(" ", "T")).format(
                                                      "MMM-DD-YYYY"
                                                    )}{" "}
                                                  </span>
                                                  {/* <br />
                                                  <span className="text-gray-500 text-xs">
                                                    {dayjs(item.send_doc_date.replace(" ", "T")).format(
                                                      "hh:mm A"
                                                    )}{" "}
                                                  </span> */}
                                                </>
                                              ) : (
                                                "N/A"
                                              )}
                                            </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                                              {item.creation ? (
                                                <>
                                                  <span>
                                                    {dayjs(item.creation.replace(" ", "T")).format(
                                                      "MMM-DD-YYYY"
                                                    )}{" "}
                                                  </span>
                                                  {/* <br />
                                                  <span className="text-gray-500 text-xs">
                                                    {dayjs(item.creation.replace(" ", "T")).format(
                                                      "hh:mm A"
                                                    )}{" "}
                                                  </span> */}
                                                </>
                                              ) : (
                                                "N/A"
                                              )}
                                            </TableCell>
                                            <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                                              {item.last_updated ? (
                                                <>
                                                  <span>
                                                    {dayjs(item.last_updated.replace(" ", "T")).format(
                                                      "MMM-DD-YYYY"
                                                    )}{" "}
                                                  </span>
                                                  {/* <br />
                                                  <span className="text-gray-500 text-xs">
                                                    {dayjs(item.last_updated.replace(" ", "T")).format(
                                                      "hh:mm A"
                                                    )}{" "}
                                                  </span> */}
                                                </>
                                              ) : (
                                                "N/A"
                                              )}
                                            </TableCell>
                                            <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                                              {item.modified_by || "N/A"}
                                            </TableCell>
                      <TableCell className="p-3 text-bold border-t-2 border-b-2 border-r-2 pl-6">
                        <span
                          className={`px-3 py-1 rounded-sm text-sm font-normal`}
                          style={{
                            color: getStatusTextColor(item.status),
                            borderColor: getStatusBorderColor(item.status),
                            backgroundColor: getStatusColorGradient(
                              item.status
                            ),
                            borderWidth: "2px",
                            borderStyle: "solid",
                          }}
                        >
                         
                          {item.status === "Reopen"
                            ? "Reopened"
                            : getDisplayStatus(item.status)}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FileSearch className="w-10 h-10 text-gray-400" />
                        <span className="text-gray-500 font-medium">
                          No data found
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

      </div>
      <div className="flex items-center justify-between sm:justify-between w-full">
        <div className="flex items-center gap-2 mt-6 ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            <ChevronLeft className="text-black" />
          </Button>
          <div className="flex gap-1">
            {getPageNumbers().map((pageNumber, index) =>
              pageNumber === "..." ? (
                <span key={`ellipsis-${index}`} className="px-2 text-gray-500">
                  ...
                </span>
              ) : (
                <Button
                  key={pageNumber}
                  className={`rounded-lg px-3 py-2 ${
                    page === pageNumber
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  onClick={() => setPage(pageNumber as number)}
                >
                  {pageNumber}
                </Button>
              )
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
            disabled={page >= totalPages}
          >
            <ChevronRight className="text-black" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CoustomerDocketsList;
