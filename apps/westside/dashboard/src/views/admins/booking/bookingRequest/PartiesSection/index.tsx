import { FC } from "react";
// React hook form & Shadcn.
import { useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
// Custom component.
import Lookup from "@/components/booking/Lookup";
import MyPartnerModal from "./MyPartnerModal";

// Types.
import { BookingRequestGeneralType } from "@/types/booking";
import PartiesDetailModal from "@/components/booking/PartiesDetailModal";
import { useQuery } from "@tanstack/react-query";
import { fetchContractPartyList } from "@/services/admin/common";

interface PartiesSectionProps {
  isAmend?: boolean;
  generalData?: BookingRequestGeneralType["data"];
}

const PartiesSection: FC<PartiesSectionProps> = ({ generalData, isAmend }) => {
  const { control, watch } = useFormContext();
  // fetching Contract Party List.
  const {
    data: contractPartyData,
    error: contractPartyError,
    isFetching: contractPartyFetching,
  } = useQuery({
    queryKey: ["fetchContractPartyList"],
    queryFn: fetchContractPartyList,
    refetchOnWindowFocus: false,
  });

  return (
    <div className="grid grid-cols-3 gap-6 ">
      <div className="">
        <FormField
          control={control}
          name="shipper"
          render={({ field }) => (
            <FormItem
            // className={`${field.value?.name || isAmend ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                {" "}
                <div className="flex items-end justify-between w-full">
                  <p>
                    Shipper <span className="text-red-600">*</span>
                  </p>
                  {/* {!isAmend ? ( */}
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <Lookup
                      field={field}
                      shippers={generalData?.shippers}
                      type="shippers"
                    />
                  </div>
                  {/* ) : (
                    ""
                   )} */}
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  {...field}
                  value={field?.value?.data}
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  placeholder="Enter Name"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="">
        <FormField
          control={control}
          name="forwarder"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Forwarder</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <Lookup
                      field={field}
                      shippers={generalData?.shippers}
                      type="shippers"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter Name"
                  {...field}
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  value={field?.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="">
        <FormField
          control={control}
          name="consignee"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Consignee</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <Lookup
                      field={field}
                      customers={generalData?.customers}
                      type="customers"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  {...field}
                  placeholder="Enter Name"
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  value={field?.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* TODO : Complete Parties modal */}
        {watch("consignee")?.name ? (
          <div className="flex justify-end">
            <PartiesDetailModal
              customerId={watch("consignee")?.name}
              customers={generalData?.customers}
            />
          </div>
        ) : (
          ""
        )}
      </div>
      <div className="">
        <FormField
          control={control}
          name="contractParty"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Contract Party</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <MyPartnerModal
                      field={field}
                      contractParty={contractPartyData?.message?.data}
                      inputName="contractParty"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  {...field}
                  placeholder="Enter Name"
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  value={field?.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="">
        <FormField
          control={control}
          name="notifyParty"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Notify Party</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <MyPartnerModal
                      field={field}
                      customers={generalData?.customers}
                      inputName="notifyParty"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter Name"
                  {...field}
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  value={field.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {watch("notifyParty")?.name ? (
          <div className="flex justify-end">
            <PartiesDetailModal
              customerId={watch("notifyParty")?.name}
              customers={generalData?.customers}
            />
          </div>
        ) : (
          ""
        )}
      </div>
      <div>
        <FormField
          control={control}
          name="notifyParty1"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Additional Notify Party 1</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <MyPartnerModal
                      field={field}
                      customers={generalData?.customers}
                      inputName="additionalNotifyParty1"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter Name"
                  {...field}
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  value={field.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {watch("notifyParty1")?.name ? (
          <div className="flex justify-end">
            <PartiesDetailModal
              customerId={watch("notifyParty1")?.name}
              customers={generalData?.customers}
            />
          </div>
        ) : (
          ""
        )}
      </div>
      <div>
        <FormField
          control={control}
          name="notifyParty2"
          render={({ field }) => (
            <FormItem
              className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
            >
              <FormLabel>
                <div className="flex items-end justify-between w-full">
                  <p>Additional Notify Party 2</p>
                  <div className="flex gap-1">
                    {field?.value?.data ? (
                      <Button
                        onClick={() =>
                          field.onChange({
                            name: "",
                            data: "",
                          })
                        }
                        type="button"
                        variant={"secondary"}
                        className="text-white h-7"
                      >
                        <X />
                      </Button>
                    ) : (
                      ""
                    )}
                    <MyPartnerModal
                      field={field}
                      customers={generalData?.customers}
                      inputName="additionalNotifyParty2"
                    />
                  </div>
                </div>
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter Name"
                  {...field}
                  onChange={(e) =>
                    field.onChange({
                      name: "",
                      data: e.target.value,
                    })
                  }
                  disabled={Boolean(field?.value?.name)}
                  className=" disabled:opacity-100 disabled:cursor-not-allowed"
                  value={field.value?.data}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {watch("notifyParty2")?.name ? (
          <div className="flex justify-end">
            <PartiesDetailModal
              customerId={watch("notifyParty2")?.name}
              customers={generalData?.customers}
            />
          </div>
        ) : (
          ""
        )}
      </div>
      <FormField
        control={control}
        name="customsBroker"
        render={({ field }) => (
          <FormItem
            className={`${field.value?.name ? " cursor-not-allowed " : ""}`}
          >
            <FormLabel>
              <div className="flex items-end justify-between w-full">
                <p>Customs Broker</p>
                <div className="flex gap-1">
                  {field?.value?.data ? (
                    <Button
                      onClick={() =>
                        field.onChange({
                          name: "",
                          data: "",
                        })
                      }
                      variant={"secondary"}
                      type="button"
                      className="text-white h-7"
                    >
                      <X />
                    </Button>
                  ) : (
                    ""
                  )}
                  <MyPartnerModal
                    field={field}
                    customers={[]}
                    inputName="notifyParty"
                  />
                </div>
              </div>
            </FormLabel>
            <FormControl>
              <Input
                type="text"
                placeholder="Enter Name"
                {...field}
                onChange={(e) =>
                  field.onChange({
                    name: "",
                    data: e.target.value,
                  })
                }
                disabled={Boolean(field?.value?.name)}
                value={field.value?.data}
                className=" disabled:opacity-100 disabled:cursor-not-allowed"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default PartiesSection;
