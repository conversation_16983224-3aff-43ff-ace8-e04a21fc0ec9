import { FC, useEffect, useState } from "react";
// react hook form
import { useFieldArray, useFormContext } from "react-hook-form";
// shadcn
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
// custom components
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Check, ChevronsUpDown } from "lucide-react";
import CarrierScheduleSection from "./CarrierScheduleSection";
import MainCarriageSection from "./MainCarriageSection";
import OnCarriageSection from "./OnCarriageSection";
import PreCarriageSection from "./PreCarriageSection";
// types
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import {
  fetchBookingLocations,
  fetchBookingOceanSchedule,
} from "@/services/admin/booking";
import { BookingRequestGeneralType } from "@/types/booking";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { Typography } from "@/components/typography";

interface TransportSectionProps {
  // oceanScheduleData?: BookingOceanSheduleDataTye[];
  carrier?: {
    name: string;
    partyalias: string;
    partyname1: string;
  };
  generalData?: BookingRequestGeneralType["data"];
}

const TransportSection: FC<TransportSectionProps> = ({
  generalData,
  carrier,
}) => {
  const { control, watch, setValue, getValues } = useFormContext();
  const [addPreCarriage, setAddPreCarriage] = useState<boolean>(
    watch("preCarriage") ? true : false
  );
  const [addOnCarriage, setAddOnCarriage] = useState<boolean>(
    watch("onCarriage") ? true : false
  );

  const mainCarriageArray = useFieldArray({
    control: control,
    name: "mainCarriage",
  });
  const preCarriageArray = useFieldArray({
    control: control,
    name: "preCarriage",
  });
  const onCarriageArray = useFieldArray({
    control: control,
    name: "onCarriage",
  });

  const selectedMoveType = watch("moveType");
  const placeCarrierRecieve = watch("placeCarrierReceipt");
  const placeCarrierDeliver = watch("placeCarrierDelivary");
  const earliestDepartureDate = watch("earliestDepartureDate");

  // fetching ocean schedule data.
  const {
    data: oceanScheduleData,
    error: oceanScheduleError,
    isFetching: oceanScheduleFetching,
  } = useQuery({
    queryKey: [
      "fetchBookingOceanSchedule",
      {
        originPort: placeCarrierRecieve?.locode,
        destinationPort: placeCarrierDeliver?.locode,
        carrier: carrier?.partyalias || "",
        searchDate: earliestDepartureDate || dayjs().format("YYYY-MM-DD"),
      },
    ],
    queryFn: fetchBookingOceanSchedule,
    enabled:
      Boolean(placeCarrierRecieve?.locode) &&
      Boolean(placeCarrierDeliver?.locode) &&
      Boolean(carrier?.partyalias) &&
      Boolean(earliestDepartureDate),
  });

  return (
    <div className="py-2 flex flex-col gap-6">
      <Card className="bg-[#F8F9FA]">
        <CardContent>
          <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3 ">
            <FormField
              control={control}
              name="moveType"
              render={({ field }) => (
                <FormItem className="">
                  <FormLabel>
                    Move Type <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl className="">
                    <Select
                      onValueChange={(data) => {
                        field.onChange(data);
                        switch (data) {
                          case "Port, Ramp, or CY to Port, Ramp, or CY":
                            setValue("preCarriage", null, {
                              shouldDirty: true,
                              shouldValidate: false,
                            });
                            setValue("onCarriage", null, {
                              shouldDirty: true,
                              shouldValidate: false,
                            });
                            setAddPreCarriage(false);
                            setAddOnCarriage(false);
                            return;

                          case "Door to Port, Ramp, or CY":
                            setValue("onCarriage", null, {
                              shouldDirty: true,
                              shouldValidate: false,
                            });
                            setAddOnCarriage(false);
                            return;
                          case "Port, Ramp, or CY to Door":
                            setValue("preCarriage", null, {
                              shouldDirty: true,
                              shouldValidate: false,
                            });
                            setAddPreCarriage(false);

                            return;
                        }
                      }}
                      defaultValue={field.value}
                    >
                      <SelectTrigger className="w-full bg-white overflow-hidden">
                        <SelectValue placeholder="Choose One" />
                      </SelectTrigger>
                      <SelectContent className="">
                        <SelectItem
                          value={"Port, Ramp, or CY to Port, Ramp, or CY"}
                        >
                          {`Port, Ramp, or CY to Port, Ramp, or CY`}
                        </SelectItem>
                        <SelectItem
                          value={"Door to Port, Ramp, or CY"}
                        >{`Door to Port, Ramp, or CY`}</SelectItem>
                        <SelectItem
                          value={"Door to Door"}
                        >{`Door to Door`}</SelectItem>
                        <SelectItem
                          value={"Port, Ramp, or CY to Door"}
                        >{`Port,Ramp, or CY to Door`}</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="placeCarrierReceipt"
              render={({ field }) => {
                const { value, onChange } = field;
                const [query, setQuery] = useState<string>("");
                const [open, setOpen] = useState(false);
                const [shownValue, setShownValue] = useState<string>(
                  value?.location || ""
                );
                useEffect(() => {
                  setShownValue(value?.location);
                }, []);

                const {
                  data: locationData,
                  error: locationError,
                  isFetching: locationFetching,
                } = useQuery({
                  queryKey: [
                    "fetchBookingLocation",
                    {
                      search: query,
                    },
                  ],
                  queryFn: fetchBookingLocations,
                });

                const handleSearch = (val: string) => {
                  setQuery(val);
                };

                return (
                  <FormItem>
                    <FormLabel className="flex items-center justify-between">
                      <span className="">Place of Carrier Receipt <span className="text-red-600">*</span></span>
                      {value?.name || value?.location || value?.locode ? (
                        <Typography
                          onClick={() =>
                            setValue("placeCarrierReceipt", {
                              name: "",
                              location: "",
                              locode: "",
                            })
                          }
                          variant={"muted"}
                          className="underline cursor-pointer"
                        >
                          Clear
                        </Typography>
                      ) : (
                        ""
                      )}
                    </FormLabel>
                    <FormControl>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={open}
                            className="justify-between w-full overflow-hidden h-11"
                          >
                            {value?.name ? shownValue : "Select Location..."}
                            <ChevronsUpDown className="opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput
                              placeholder="Search Loction..."
                              className="h-9"
                              value={query}
                              onValueChange={(val) => handleSearch(val)}
                            />
                            <CommandList>
                              <CommandEmpty>
                                {locationFetching ? (
                                  <div className="flex justify-center w-full">
                                    <SpinnerLoader />
                                  </div>
                                ) : (
                                  "  No Location Found."
                                )}
                              </CommandEmpty>
                              <CommandGroup>
                                {locationData?.message?.results?.length
                                  ? locationData?.message?.results?.map(
                                      (location) => (
                                        <CommandItem
                                          key={location.name}
                                          value={`${location?.location_name}, ${
                                            location?.sub_division
                                              ? `${location?.sub_division},`
                                              : ""
                                          } ${location?.country} (${
                                            location?.locode
                                          })`}
                                          onSelect={() => {
                                            onChange({
                                              name: String(location.name),
                                              location: `${
                                                location?.location_name
                                              }, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`,
                                              locode: location?.locode,
                                            });
                                            setShownValue(
                                              `${location?.location_name}, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`
                                            );

                                            if (
                                              selectedMoveType ===
                                                "Port, Ramp, or CY to Port, Ramp, or CY" ||
                                              selectedMoveType ===
                                                "Port,Ramp, or CY to Door"
                                            ) {
                                              //   Check if mainCarriage[0] exists and has portOfLoad data
                                              const mainCarriage =
                                                getValues("mainCarriage");
                                              const portOfLoad =
                                                mainCarriage?.[0]?.portOfLoad;

                                              const isEmptyPortOfLoad =
                                                !portOfLoad?.location &&
                                                !portOfLoad?.name;

                                              if (isEmptyPortOfLoad) {
                                                setValue(
                                                  "mainCarriage.0.portOfLoad",
                                                  {
                                                    name: location.name,
                                                    location: `${
                                                      location?.location_name
                                                    }, ${
                                                      location?.sub_division
                                                        ? `${location?.sub_division},`
                                                        : ""
                                                    } ${location?.country} (${
                                                      location?.locode
                                                    })`,
                                                  },
                                                  {
                                                    shouldDirty: true,
                                                    shouldTouch: true,
                                                    shouldValidate: true,
                                                  }
                                                );
                                              }
                                            }

                                            setOpen(false);
                                          }}
                                        >
                                          {`${location?.location_name}, ${
                                            location?.sub_division
                                              ? `${location?.sub_division},`
                                              : ""
                                          } ${location?.country} (${
                                            location?.locode
                                          })`}
                                          <Check
                                            className={
                                              value?.name ===
                                              String(location.name)
                                                ? "ml-auto opacity-100"
                                                : "ml-auto opacity-0"
                                            }
                                          />
                                        </CommandItem>
                                      )
                                    )
                                  : ""}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
            <FormField
              control={control}
              name="earliestDepartureDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Earliest Departure Date<span className="text-red-600">*</span></FormLabel>
                  <FormControl>
                    <Input type="date" {...field} className="bg-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="placeCarrierDelivary"
              render={({ field }) => {
                const { value, onChange } = field;
                const [query, setQuery] = useState<string>("");
                const [open, setOpen] = useState(false);
                const [shownValue, setShownValue] = useState<string>(
                  value?.location || ""
                );
                useEffect(() => {
                  setShownValue(value?.location);
                }, []);

                const {
                  data: locationData,
                  error: locationError,
                  isFetching: locationFetching,
                } = useQuery({
                  queryKey: [
                    "fetchBookingLocation",
                    {
                      search: query,
                    },
                  ],
                  queryFn: fetchBookingLocations,
                });

                const handleSearch = (val: string) => {
                  setQuery(val);
                };

                return (
                  <FormItem>
                    <FormLabel className="flex items-center justify-between">
                      <span className="">Place of Carrier Delivery <span className="text-red-600">*</span></span>
                      {value?.name || value?.location || value?.locode ? (
                        <Typography
                          onClick={() =>
                            setValue("placeCarrierDelivary", {
                              name: "",
                              location: "",
                              locode: "",
                            })
                          }
                          variant={"muted"}
                          className="underline cursor-pointer"
                        >
                          Clear
                        </Typography>
                      ) : (
                        ""
                      )}
                    </FormLabel>
                    <FormControl>
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={open}
                            className="justify-between w-full overflow-hidden h-11"
                          >
                            {value?.name ? shownValue : "Select Location..."}
                            <ChevronsUpDown className="opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput
                              placeholder="Search Loction..."
                              className="h-9"
                              value={query}
                              onValueChange={(val) => handleSearch(val)}
                            />
                            <CommandList>
                              <CommandEmpty>
                                {locationFetching ? (
                                  <div className="flex justify-center w-full">
                                    <SpinnerLoader />
                                  </div>
                                ) : (
                                  "No Location Found."
                                )}
                              </CommandEmpty>
                              <CommandGroup>
                                {locationData?.message?.results?.length
                                  ? locationData?.message?.results?.map(
                                      (location) => (
                                        <CommandItem
                                          key={location.name}
                                          value={`${location?.location_name}, ${
                                            location?.sub_division
                                              ? `${location?.sub_division},`
                                              : ""
                                          } ${location?.country} (${
                                            location?.locode
                                          })`}
                                          onSelect={() => {
                                            onChange({
                                              name: String(location.name),
                                              location: `${
                                                location?.location_name
                                              }, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`,
                                              locode: location?.locode,
                                            });
                                            setShownValue(
                                              `${location?.location_name}, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`
                                            );

                                            if (
                                              selectedMoveType ===
                                                "Port, Ramp, or CY to Port, Ramp, or CY" ||
                                              selectedMoveType ===
                                                "Door to Port, Ramp, or CY"
                                            ) {
                                              //   Check if mainCarriage[0] exists and has portOfLoad data
                                              const mainCarriage =
                                                getValues("mainCarriage");
                                              const portOfLoad =
                                                mainCarriage?.[0]
                                                  ?.portOfDischarge;

                                              const isEmptyPortOfLoad =
                                                !portOfLoad?.location &&
                                                !portOfLoad?.name;

                                              if (isEmptyPortOfLoad) {
                                                setValue(
                                                  "mainCarriage.0.portOfDischarge",
                                                  {
                                                    name: location.name,
                                                    location: `${
                                                      location?.location_name
                                                    }, ${
                                                      location?.sub_division
                                                        ? `${location?.sub_division},`
                                                        : ""
                                                    } ${location?.country} (${
                                                      location?.locode
                                                    })`,
                                                  },
                                                  {
                                                    shouldDirty: true,
                                                    shouldTouch: true,
                                                    shouldValidate: true,
                                                  }
                                                );
                                              }
                                            }

                                            setOpen(false);
                                          }}
                                        >
                                          {`${location?.location_name}, ${
                                            location?.sub_division
                                              ? `${location?.sub_division},`
                                              : ""
                                          } ${location?.country} (${
                                            location?.locode
                                          })`}
                                          <Check
                                            className={
                                              value?.name ===
                                              String(location.name)
                                                ? "ml-auto opacity-100"
                                                : "ml-auto opacity-0"
                                            }
                                          />
                                        </CommandItem>
                                      )
                                    )
                                  : ""}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
            <FormField
              control={control}
              name="latestDeliveryDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Latest Delivery Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} className="bg-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
      {selectedMoveType === "Port, Ramp, or CY to Port, Ramp, or CY" &&
      oceanScheduleData?.message?.length ? (
        <CarrierScheduleSection
          oceanScheduleData={oceanScheduleData?.message}
          carrier={carrier?.partyname1 || "N/A"}
          destinationPort={placeCarrierDeliver}
          originPort={placeCarrierRecieve}
          searchDate={earliestDepartureDate || dayjs().format("YYYY-MM-DD")}
        />
      ) : (
        ""
      )}
      <PreCarriageSection
        fields={preCarriageArray.fields}
        append={() =>
          preCarriageArray.append({
            start: {
              name: "",
              location: "",
            },
            end: {
              name: "",
              location: "",
            },
            eta: "",
            etd: "",
            mode: "",
          })
        }
        remove={preCarriageArray.remove}
        addPreCarriage={addPreCarriage}
        setAddPreCarriage={setAddPreCarriage}
      />
      <MainCarriageSection
        fields={mainCarriageArray.fields}
        append={() =>
          mainCarriageArray.append({
            portOfLoad: {
              name: "",
              location: "",
            },
            portOfDischarge: {
              name: "",
              location: "",
            },
            etd: "",
            eta: "",
            vessel: "",
            voyage: "",
          })
        }
        remove={mainCarriageArray.remove}
      />
      <OnCarriageSection
        fields={onCarriageArray.fields}
        append={() =>
          onCarriageArray.append({
            start: {
              name: "",
              location: "",
            },
            end: {
              name: "",
              location: "",
            },
            eta: "",
            etd: "",
            mode: "",
          })
        }
        remove={onCarriageArray.remove}
        addOnCarriage={addOnCarriage}
        setAddOnCarriage={setAddOnCarriage}
      />
    </div>
  );
};

export default TransportSection;
