import { FC, Fragment, useMemo, useState } from "react";
// react hook form
import { useFieldArray, useFormContext } from "react-hook-form";
// shadcn
import { Button } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Check, ChevronsUpDown, Minus, Plus, SquareArrowOutUpRight } from "lucide-react";
// custom component
import FumigationDetailModal from "./FumigationDetailModal";
import HaulageDetailModal from "./haulageDetailModal";

// types
import { BookingContainerTypeGeneralType } from "@/types/booking";

interface ContainerSectionProps {
  containerTypes?: BookingContainerTypeGeneralType[];
}

const ContainerSection: FC<ContainerSectionProps> = ({ containerTypes }) => {
  const { control } = useFormContext();

  const containersArray = useFieldArray({
    control: control,
    name: "containers",
  });

  return (
    <div>
      {containersArray?.fields?.map((_, index) => (
        <Fragment key={index}>
          <div className="grid grid-cols-3  gap-6 py-4 ">
            <div>
              <Label className="py-3">Container Quantity/Type <span className="text-red-600">*</span></Label>
              <div className="grid grid-cols-12 gap-4">
                <div className="col-span-2">
                  <FormField
                    control={control}
                    name={`containers.${index}.containerQuantity`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="00"
                            {...field}
                            onChange={(e) => {
                              const value = e.target.value.replace(/\D/g, "");
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="col-span-10 w-full">
                  <FormField
                    control={control}
                    name={`containers.${index}.containerType`}
                    render={({ field }) => {
                      const { value, onChange } = field;
                      const [query, setQuery] = useState<string>("");
                      const [open, setOpen] = useState(false);

                      const filteredContainers = useMemo(() => {
                        if (!query) return containerTypes?.slice(0, 50);
                        return containerTypes
                          ?.filter((container) =>
                            container.shortdescription?.toLowerCase()?.includes(query.toLowerCase())
                          )
                          ?.slice(0, 50);
                      }, [query, containerTypes]);

                      const handleSearch = (val: string) => {
                        setQuery(val);
                      };
                      return (
                        <FormItem>
                          <FormControl>
                            <Popover open={open} onOpenChange={setOpen}>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between h-11"
                                >
                                  {value
                                    ? containerTypes?.find((container) => String(container.name) === value?.name)
                                        ?.shortdescription
                                    : "Select container..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Container Type..."
                                    className="h-9"
                                    value={query}
                                    onValueChange={(val) => handleSearch(val)}
                                  />
                                  <CommandList>
                                    <CommandEmpty>No Container Type Found.</CommandEmpty>
                                    <CommandGroup>
                                      {filteredContainers?.map((container) => (
                                        <CommandItem
                                          key={container.name}
                                          value={String(container.shortdescription)}
                                          onSelect={() => {
                                            onChange({
                                              name: String(container.name),
                                              type: container?.shortdescription,
                                            });
                                            setOpen(false);
                                          }}
                                        >
                                          {container.shortdescription}
                                          <Check
                                            className={
                                              value?.name === String(container.name)
                                                ? "ml-auto opacity-100"
                                                : "ml-auto opacity-0"
                                            }
                                          />
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-between gap-4 pt-4 ">
              <div className="w-full ">
                {" "}
                <FormField
                  control={control}
                  name={`containers.${index}.containerComments`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Container Comments</FormLabel>
                      <FormControl>
                        <Input type="text" placeholder="Enter Comments" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="size-12 pt-5">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant={"outline"} className="w-full size-11 ">
                      <SquareArrowOutUpRight />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-screen-sm">
                    <DialogHeader>
                      <DialogTitle>Container Comments </DialogTitle>
                    </DialogHeader>
                    <hr />
                    <div className="">
                      <FormField
                        control={control}
                        name={`containers.${index}.containerComments`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Container Comments *</FormLabel>

                            <FormControl>
                              <Textarea placeholder="Cargo Description" {...field} className="min-h-44 mt-2" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
            <div className=" flex justify-between pt-6 ">
              <FormField
                control={control}
                name={`containers.${index}.isShipperOwned`}
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox checked={field?.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Shipper Owned</FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end items-center gap-2">
                <Button
                  type="button"
                  className="bg-secondary hover:bg-secondary/90"
                  onClick={() =>
                    containersArray.append({
                      containerQuantity: "",
                      containerType: {
                        name: "",
                        type: "",
                      },
                      containerComments: "",
                      isShipperOwned: false,
                    })
                  }
                >
                  <Plus />
                </Button>
                {containersArray?.fields?.length > 1 && (
                  <Button onClick={() => containersArray?.remove(index)} type="button" variant="outline" size="icon">
                    <Minus />
                  </Button>
                )}
              </div>
            </div>
            <div className="flex gap-4 items-center">
              <HaulageDetailModal containerIndex={index} />
              {/* <FumigationDetailModal /> */}
            </div>
          </div>
          {index === containersArray?.fields?.length - 1 ? "" : <hr />}
        </Fragment>
      ))}
    </div>
  );
};

export default ContainerSection;
