import { FC, useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Check, ChevronsUpDown, Info } from "lucide-react";
import { Typography } from "@/components/typography";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
// types
import { BookingRequestGeneralType } from "@/types/booking";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { fetchBookingLocations } from "@/services/admin/booking";
import { useQuery } from "@tanstack/react-query";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { useFormContext } from "react-hook-form";

interface GeneralDetailSectionProps {
  isAmend?: boolean;
  generalData?: BookingRequestGeneralType["data"];
}

const GeneralDetailSection: FC<GeneralDetailSectionProps> = ({
  generalData,
  isAmend,
}) => {
  const { control, setValue, watch } = useFormContext();
  return (
    <div className="grid grid-cols-3 gap-6 ">
      <FormField
        control={control}
        name="carrier"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Carrier / NVOCC / Booking Agent{" "}
              <span className="text-red-600">*</span>
            </FormLabel>
            <FormControl>
              <Select
                onValueChange={(data) => {
                  const datas = generalData?.carriers?.find(
                    (item) => item?.name === data
                  );
                  field.onChange({
                    name: datas?.name,
                    partyalias: datas?.partyalias,
                    partyname1: datas?.partyname1,
                  });
                }}
                defaultValue={field?.value?.name}
              >
                <SelectTrigger className="w-full overflow-hidden">
                  <SelectValue placeholder="Choose One" />
                </SelectTrigger>
                <SelectContent>
                  {generalData?.carriers?.map((carrier) => (
                    <SelectItem key={carrier?.name} value={carrier?.name}>
                      {carrier?.partyname1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="contractNumber"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Contract Number</FormLabel>
            <FormControl>
              <Input type="text" placeholder="Enter Number" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="bookingOfficeLocation"
        render={({ field }) => {
          const { value, onChange } = field;
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const [query, setQuery] = useState<string>("");
          const [open, setOpen] = useState(false);
          const [shownValue, setShownValue] = useState<string>(
            value?.location || ""
          );
          useEffect(() => {
            setShownValue(value?.location);
          }, []);

          const {
            data: locationData,
            error: locationError,
            isFetching: locationFetching,
          } = useQuery({
            queryKey: [
              "fetchBookingLocation",
              {
                search: query,
              },
            ],
            queryFn: fetchBookingLocations,
          });

          const handleSearch = (val: string) => {
            setQuery(val);
          };

          return (
            <FormItem>
              <FormLabel className="w-full flex justify-between">
                <div className="flex items-center gap-1 ">
                  <span className="">Booking Office </span>
                  <span className="">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info size={15} />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-64">
                          <Typography variant={"small"} className="text-white">
                            This indicates the carrier location that will handle
                            your shipment. If the preferred carrier location is
                            other than the port of load, indicate that location
                            here. Otherwise, enter port of load location.
                          </Typography>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </span>
                </div>
                {value?.name || value?.location ? (
                  <Typography
                    onClick={() =>
                      setValue("bookingOfficeLocation", {
                        name: "",
                        location: "",
                      })
                    }
                    variant={"muted"}
                    className="underline cursor-pointer"
                  >
                    Clear
                  </Typography>
                ) : (
                  ""
                )}
              </FormLabel>
              <FormControl>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className="justify-between w-full overflow-hidden h-11"
                    >
                      {value?.location ? shownValue : "Select Location..."}
                      <ChevronsUpDown className="opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput
                        placeholder="Search Loction..."
                        className="h-9"
                        value={query}
                        onValueChange={(val) => handleSearch(val)}
                      />
                      <CommandList>
                        <CommandEmpty>
                          {locationFetching ? (
                            <div className="flex justify-center w-full">
                              <SpinnerLoader />
                            </div>
                          ) : (
                            "No Location Found."
                          )}
                        </CommandEmpty>
                        <CommandGroup>
                          {locationData?.message?.results?.length
                            ? locationData?.message?.results?.map(
                                (location) => (
                                  <CommandItem
                                    key={location.name}
                                    value={`${location?.location_name}, ${
                                      location?.sub_division
                                        ? `${location?.sub_division},`
                                        : ""
                                    } ${location?.country} (${
                                      location?.locode
                                    })`}
                                    onSelect={() => {
                                      onChange({
                                        name: String(location.name),
                                        location: `${
                                          location?.location_name
                                        }, ${
                                          location?.sub_division
                                            ? `${location?.sub_division},`
                                            : ""
                                        } ${location?.country} (${
                                          location?.locode
                                        })`,
                                      });
                                      setShownValue(
                                        `${location?.location_name}, ${
                                          location?.sub_division
                                            ? `${location?.sub_division},`
                                            : ""
                                        } ${location?.country} (${
                                          location?.locode
                                        })`
                                      );
                                      setOpen(false);
                                    }}
                                  >
                                    {`${location?.location_name}, ${
                                      location?.sub_division
                                        ? `${location?.sub_division},`
                                        : ""
                                    } ${location?.country} (${
                                      location?.locode
                                    })`}
                                    <Check
                                      className={
                                        value?.name === String(location.name)
                                          ? "ml-auto opacity-100"
                                          : "ml-auto opacity-0"
                                      }
                                    />
                                  </CommandItem>
                                )
                              )
                            : ""}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
      <FormField
        disabled={true}
        control={control}
        name="bookingNumber"
        render={({ field }) => (
          <FormItem className="cursor-not-allowed">
            <FormLabel> Carrier Booking Number</FormLabel>
            <FormControl>
              <Input
                disabled={true}
                type="text"
                placeholder="Enter Number"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div>
        <FormField
          control={control}
          name="carrierDoesNotFile"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="flex items-center gap-1 pb-0.5">
                  <span className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(bool) => {
                          if (!bool) {
                            setValue("filerId", "");
                          }
                          field.onChange(bool);
                        }}
                      />
                    </FormControl>
                    <Label htmlFor="terms">Carrier Does NOT File</Label>
                  </span>
                  <span className="">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info size={15} />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-64">
                          <Typography variant={"small"} className="text-white">
                            Check this box if the Carrier will NOT be filing the
                            Cargo Manifest with Customs.
                          </Typography>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </span>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="filerId"
          disabled={!watch("carrierDoesNotFile")}
          render={({ field }) => (
            <FormItem
              className={
                !watch("carrierDoesNotFile") ? "cursor-not-allowed" : ""
              }
            >
              <FormControl>
                <span className="relative">
                  <Label
                    htmlFor="picture"
                    className="min-w-10 px-3 absolute inset-y-0 left-0 bg-[#F9FAFC] border"
                  >
                    ID/SCAC
                  </Label>
                  <Input
                    className={`pl-22 `}
                    id="picture"
                    placeholder="Enter ID"
                    {...field}
                  />
                </span>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={control}
        name="isDistinctReleaseNumber"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center gap-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <Label className="text-sm">
                Distinct Release Number Per Container
              </Label>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default GeneralDetailSection;
