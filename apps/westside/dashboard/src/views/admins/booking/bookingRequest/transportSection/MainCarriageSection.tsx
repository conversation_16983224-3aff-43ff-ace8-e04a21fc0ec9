import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { Typography } from "@/components/typography";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { fetchBookingLocations } from "@/services/admin/booking";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { Check, ChevronsUpDown, Minus, Plus } from "lucide-react";
import { FC, Fragment, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

interface MainCarriageSectionProps {
  fields: any[];
  append: () => void;
  remove: (index: number) => void;
}

const MainCarriage: FC<MainCarriageSectionProps> = ({
  fields,
  append,
  remove,
}) => {
  const { control, setValue, watch } = useFormContext();
  return (
    <div className="">
      <Typography variant={"p"}>Main Carriage</Typography>
      <Card className="bg-[#F8F9FA]">
        <CardContent>
          {fields?.map((field, index) => (
            <Fragment key={index}>
              <div className="grid grid-cols-4 gap-6 py-4">
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.portOfLoad`}
                  render={({ field }) => {
                    const { value, onChange } = field;
                    const [query, setQuery] = useState<string>("");
                    const [open, setOpen] = useState(false);
                    const [shownValue, setShownValue] = useState<string>(
                      value?.location || ""
                    );
                    useEffect(() => {
                      setShownValue(value?.location);
                    }, [value]);

                    const {
                      data: locationData,
                      error: locationError,
                      isFetching: locationFetching,
                    } = useQuery({
                      queryKey: [
                        "fetchBookingLocation",
                        {
                          search: query,
                        },
                      ],
                      queryFn: fetchBookingLocations,
                    });

                    const handleSearch = (val: string) => {
                      setQuery(val);
                    };

                    return (
                      <FormItem>
                        <FormLabel className="flex items-center justify-between">
                          <span className="">
                            Port of Load <span className="text-red-600">*</span>
                          </span>
                          {value?.name || value?.location ? (
                            <Typography
                              onClick={() =>
                                setValue(`mainCarriage.${index}.portOfLoad`, {
                                  name: "",
                                  location: "",
                                })
                              }
                              variant={"muted"}
                              className="underline cursor-pointer"
                            >
                              Clear
                            </Typography>
                          ) : (
                            ""
                          )}
                        </FormLabel>
                        <FormControl>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={open}
                                className="justify-between w-full overflow-hidden h-11"
                              >
                                {value?.name
                                  ? shownValue
                                  : "Select Location..."}
                                <ChevronsUpDown className="opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput
                                  placeholder="Search Loction..."
                                  className="h-9"
                                  value={query}
                                  onValueChange={(val) => handleSearch(val)}
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {locationFetching ? (
                                      <div className="flex justify-center w-full ">
                                        <SpinnerLoader />
                                      </div>
                                    ) : (
                                      "No Location Found."
                                    )}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {locationData?.message?.results?.length
                                      ? locationData?.message?.results?.map(
                                          (location) => (
                                            <CommandItem
                                              key={location.name}
                                              value={`${
                                                location?.location_name
                                              }, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`}
                                              onSelect={() => {
                                                onChange({
                                                  name: String(location.name),
                                                  location: `${
                                                    location?.location_name
                                                  }, ${
                                                    location?.sub_division
                                                      ? `${location?.sub_division},`
                                                      : ""
                                                  } ${location?.country} (${
                                                    location?.locode
                                                  })`,
                                                });
                                                setShownValue(
                                                  `${
                                                    location?.location_name
                                                  }, ${
                                                    location?.sub_division
                                                      ? `${location?.sub_division},`
                                                      : ""
                                                  } ${location?.country} (${
                                                    location?.locode
                                                  })`
                                                );
                                                setOpen(false);
                                              }}
                                            >
                                              {`${location?.location_name}, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`}
                                              <Check
                                                className={
                                                  value?.name ===
                                                  String(location.name)
                                                    ? "ml-auto opacity-100"
                                                    : "ml-auto opacity-0"
                                                }
                                              />
                                            </CommandItem>
                                          )
                                        )
                                      : ""}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.etd`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ETD</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          min={dayjs().format("YYYY-MM-DD")}
                          className="bg-white"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div></div>
                <div className="flex items-center justify-end gap-2">
                  <Button
                    type="button"
                    className="bg-secondary hover:bg-secondary/90"
                    onClick={() => append()}
                  >
                    <Plus />
                  </Button>
                  {fields.length > 1 && (
                    <Button
                      onClick={() => remove(index)}
                      type="button"
                      variant="outline"
                      size="icon"
                    >
                      <Minus />
                    </Button>
                  )}
                </div>
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.portOfDischarge`}
                  render={({ field }) => {
                    const { value, onChange } = field;
                    const [query, setQuery] = useState<string>("");
                    const [open, setOpen] = useState(false);
                    const [shownValue, setShownValue] = useState<string>(
                      value?.location || ""
                    );
                    useEffect(() => {
                      setShownValue(value?.location);
                    }, [value]);

                    const {
                      data: locationData,
                      error: locationError,
                      isFetching: locationFetching,
                    } = useQuery({
                      queryKey: [
                        "fetchBookingLocation",
                        {
                          search: query,
                        },
                      ],
                      queryFn: fetchBookingLocations,
                    });

                    const handleSearch = (val: string) => {
                      setQuery(val);
                    };

                    return (
                      <FormItem className="">
                        <FormLabel className="flex items-center justify-between">
                          <span className="">
                            Port of Discharge{" "}
                            <span className="text-red-600">*</span>
                          </span>

                          {value?.name || value?.location ? (
                            <Typography
                              onClick={() =>
                                setValue(
                                  `mainCarriage.${index}.portOfDischarge`,
                                  {
                                    name: "",
                                    location: "",
                                  }
                                )
                              }
                              variant={"muted"}
                              className="underline cursor-pointer"
                            >
                              Clear
                            </Typography>
                          ) : (
                            ""
                          )}
                        </FormLabel>
                        <FormControl>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={open}
                                className="justify-between w-full overflow-hidden h-11"
                              >
                                {value?.name
                                  ? shownValue?.length > 35
                                    ? `${shownValue?.slice(0, 35)}..`
                                    : shownValue
                                  : "Select Location..."}
                                <ChevronsUpDown className="opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput
                                  placeholder="Search Loction..."
                                  className="h-9"
                                  value={query}
                                  onValueChange={(val) => handleSearch(val)}
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {locationFetching ? (
                                      <div className="flex justify-center w-full">
                                        <SpinnerLoader />
                                      </div>
                                    ) : (
                                      "No Location Found. "
                                    )}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {locationData?.message?.results?.length
                                      ? locationData?.message?.results?.map(
                                          (location) => (
                                            <CommandItem
                                              key={location.name}
                                              value={`${
                                                location?.location_name
                                              }, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`}
                                              onSelect={() => {
                                                onChange({
                                                  name: String(location.name),
                                                  location: `${
                                                    location?.location_name
                                                  }, ${
                                                    location?.sub_division
                                                      ? `${location?.sub_division},`
                                                      : ""
                                                  } ${location?.country} (${
                                                    location?.locode
                                                  })`,
                                                });
                                                setShownValue(
                                                  `${
                                                    location?.location_name
                                                  }, ${
                                                    location?.sub_division
                                                      ? `${location?.sub_division},`
                                                      : ""
                                                  } ${location?.country} (${
                                                    location?.locode
                                                  })`
                                                );
                                                setOpen(false);
                                              }}
                                            >
                                              {`${location?.location_name}, ${
                                                location?.sub_division
                                                  ? `${location?.sub_division},`
                                                  : ""
                                              } ${location?.country} (${
                                                location?.locode
                                              })`}
                                              <Check
                                                className={
                                                  value?.name ===
                                                  String(location.name)
                                                    ? "ml-auto opacity-100"
                                                    : "ml-auto opacity-0"
                                                }
                                              />
                                            </CommandItem>
                                          )
                                        )
                                      : ""}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.eta`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ETA</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          min={
                            watch(`mainCarriage.${index}.etd`)
                              ? dayjs(
                                  watch(`mainCarriage.${index}.etd`)
                                )?.format("YYYY-MM-DD")
                              : dayjs().format("YYYY-MM-DD")
                          }
                          className="bg-white"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.vessel`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vessel</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter Vessel"
                          {...field}
                          className="bg-white"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={control}
                  name={`mainCarriage.${index}.voyage`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Voyage</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter Voyage"
                          {...field}
                          className="bg-white"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {index === fields?.length - 1 ? "" : <hr />}
            </Fragment>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};

export default MainCarriage;
