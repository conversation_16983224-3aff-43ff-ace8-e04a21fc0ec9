//shadcn
import { Typography } from "@/components/typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { BookingOceanSheduleDataTye } from "@/types/booking";
import dayjs from "dayjs";
import { Info } from "lucide-react";
import { FC } from "react";
import { useFormContext } from "react-hook-form";

interface CarrierScheduleSectionProps {
  oceanScheduleData?: BookingOceanSheduleDataTye[];
  originPort: {
    name: string;
    location: string;
    locode: string;
  };
  destinationPort: {
    name: string;
    location: string;
    locode: string;
  };
  carrier: string;
  searchDate: string;
}
const CarrierScheduleSection: FC<CarrierScheduleSectionProps> = ({
  oceanScheduleData = [],
  originPort,
  destinationPort,
  carrier,
  searchDate,
}) => {
  const endOfSearchDate = dayjs(searchDate)
    .add(4, "week")
    ?.format("DD-MMM-YYYY");
  const { watch, setValue } = useFormContext();

  return (
    <Card className="bg-[#F8F9FA]">
      <CardHeader>
        <CardTitle>
          <Typography
            variant={"small"}
            weight={"medium"}
            className="flex items-center gap-0.5 lead"
          >
            <span className="">
              {" "}
              <Info size={15} />
            </span>
            INTTRA has found {oceanScheduleData?.length} matching {carrier}{" "}
            schedules. (optional)
            <span className=""></span>
          </Typography>
          <Typography variant={"small"} weight={"medium"}>
            Showing result for {originPort?.location} to{" "}
            {destinationPort?.location} from{" "}
            {dayjs(searchDate)?.format("DD-MMM-YYYY")} to {endOfSearchDate}
          </Typography>
        </CardTitle>
      </CardHeader>
      <hr />
      <CardContent>
        <div>
          <ScrollArea className="w-full border rounded-md h-72">
            <Table>
              <TableHeader>
                <TableRow className="h-16 bg-muted-foreground hover:bg-muted-foreground/90">
                  <TableHead className="font-medium">Vessel/Voyage</TableHead>
                  <TableHead className="font-medium">Service</TableHead>
                  <TableHead className="font-medium">
                    Terminal Cut-Off
                  </TableHead>
                  <TableHead className="font-medium">Departure</TableHead>
                  <TableHead className="font-medium">Arrival</TableHead>
                  <TableHead className="font-medium ">Transit</TableHead>
                  <TableHead className="pr-5 font-medium text-right"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {oceanScheduleData?.map((schedule, invoice) => (
                  <TableRow
                    className="hover:bg-muted-foreground/20"
                    key={invoice}
                  >
                    <TableCell className="py-4">{`${schedule?.vesselName}/${schedule?.voyageNumber}`}</TableCell>
                    <TableCell className="py-4">
                      {schedule?.serviceName || "N/A"}
                    </TableCell>
                    <TableCell className="py-4">
                      {schedule?.terminalCutoff &&
                      dayjs(schedule.terminalCutoff).isValid()
                        ? dayjs(schedule?.terminalCutoff)?.format("DD-MMM-YYYY")
                        : "N/A"}
                    </TableCell>
                    <TableCell className="py-4">
                      {schedule?.originDepartureDate &&
                      dayjs(schedule.originDepartureDate).isValid()
                        ? dayjs(schedule?.originDepartureDate)?.format(
                            "DD-MMM-YYYY"
                          )
                        : "N/A"}
                    </TableCell>
                    <TableCell className="py-4">
                      {" "}
                      {schedule?.destinationArrivalDate &&
                      dayjs(schedule.destinationArrivalDate).isValid()
                        ? dayjs(schedule?.destinationArrivalDate)?.format(
                            "DD-MMM-YYYY"
                          )
                        : "N/A"}
                    </TableCell>
                    <TableCell className="py-4 ">
                      {schedule?.totalDuration
                        ? `${schedule?.totalDuration} DAYS`
                        : "N/A"}
                    </TableCell>
                    <TableCell className="py-4 pr-5 text-right">
                      {watch("mainCarriage.0.etd") ===
                        dayjs(schedule?.originDepartureDate)?.format(
                          "YYYY-MM-DD"
                        ) &&
                      watch("mainCarriage.0.eta") ===
                        dayjs(schedule?.destinationArrivalDate)?.format(
                          "YYYY-MM-DD"
                        ) &&
                      watch("mainCarriage.0.vessel") === schedule?.vesselName &&
                      watch("mainCarriage.0.voyage") ===
                        schedule?.voyageNumber ? (
                        <div className="flex items-center justify-center py-1.5 text-sidebar text-sm rounded shadow-sm h-min bg-green-300/20 border-[0.1px] border-green-500">
                          {" "}
                          Selected
                        </div>
                      ) : (
                        <div
                          className="flex items-center justify-center py-1.5 text-white rounded shadow-sm h-min bg-primary/90 hover:bg-primary cursor-pointer"
                          onClick={() => {
                            setValue("mainCarriage.0.portOfLoad", originPort, {
                              shouldDirty: true,
                              shouldTouch: true,
                              shouldValidate: true,
                            });
                            setValue(
                              "mainCarriage.0.portOfDischarge",
                              destinationPort,
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true,
                              }
                            );
                            setValue(
                              "mainCarriage.0.etd",
                              dayjs(schedule?.originDepartureDate)?.format(
                                "YYYY-MM-DD"
                              ),
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true,
                              }
                            );
                            setValue(
                              "mainCarriage.0.eta",
                              dayjs(schedule?.destinationArrivalDate)?.format(
                                "YYYY-MM-DD"
                              ),
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true,
                              }
                            );
                            setValue(
                              "mainCarriage.0.vessel",
                              schedule?.vesselName,
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true,
                              }
                            );
                            setValue(
                              "mainCarriage.0.voyage",
                              schedule?.voyageNumber,
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                                shouldValidate: true,
                              }
                            );
                          }}
                        >
                          Select
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
};

export default CarrierScheduleSection;
