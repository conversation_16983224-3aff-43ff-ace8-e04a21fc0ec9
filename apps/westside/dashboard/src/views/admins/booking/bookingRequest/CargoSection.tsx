import { FC, Fragment, useState } from "react";
// react hook form
import { useFieldArray, useFormContext } from "react-hook-form";
// shadcn
import { But<PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Check,
  ChevronsUpDown,
  Minus,
  Plus,
  SquareArrowOutUpRight,
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
// types
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useQuery } from "@tanstack/react-query";
import { fetchHsCodes } from "@/services/admin/common";

interface CargoSectionProps {}

const CargoSection: FC<CargoSectionProps> = () => {
  const { control, setValue } = useFormContext();
  const cargosArray = useFieldArray({
    control: control,
    name: "cargos",
  });

  return (
    <div>
      {cargosArray?.fields?.map((field, index) => (
        <Fragment key={index}>
          <div className="grid grid-cols-12 gap-6 py-4 ">
            <div className="col-span-1">
              <FormField
                control={control}
                name={`cargos.${index}.hsCode`}
                render={({ field }) => {
                  const { value, onChange } = field;
                  const [query, setQuery] = useState<string>("");
                  const [open, setOpen] = useState(false);

                  const {
                    data: hsCodeData,
                    error: hsCodeError,
                    isFetching: hsCodeFetching,
                  } = useQuery({
                    queryKey: [
                      "fetchCommonHsCode",
                      {
                        search: query,
                      },
                    ],
                    queryFn: fetchHsCodes,
                  });

                  const handleSearch = (val: string) => {
                    setQuery(val);
                  };

                  return (
                    <FormItem>
                      <FormLabel>
                        HS Code <span className="text-red-600">*</span>
                      </FormLabel>
                      <FormControl>
                        <Popover open={open} onOpenChange={setOpen}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={open}
                              className="justify-between w-full overflow-hidden h-11"
                            >
                              {value ? value : "HS Code"}
                              <ChevronsUpDown className="opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="HS Code"
                                className="h-9"
                                value={query}
                                onValueChange={(val) => handleSearch(val)}
                              />
                              <CommandList>
                                <CommandEmpty>No HScode Found.</CommandEmpty>
                                <CommandGroup>
                                  {hsCodeData?.message?.results?.map(
                                    (hscode) => (
                                      <CommandItem
                                        key={hscode.name}
                                        value={`${hscode?.name}  ${hscode?.hs_code_description}`}
                                        onSelect={() => {
                                          onChange(String(hscode.name));

                                          setValue(
                                            `cargos.${index}.cargoDescription`,
                                            hscode?.hs_code_description,
                                            {
                                              shouldDirty: true,
                                              shouldTouch: true,
                                              shouldValidate: true,
                                            }
                                          );

                                          setOpen(false);
                                        }}
                                      >
                                        {`${hscode?.name}  ${hscode?.hs_code_description}`}
                                        <Check
                                          className={
                                            value === String(hscode.name)
                                              ? "ml-auto opacity-100"
                                              : "ml-auto opacity-0"
                                          }
                                        />
                                      </CommandItem>
                                    )
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
            <div className="col-span-2">
              <div className="flex justify-between gap-4 ">
                <div className="w-full">
                  <FormField
                    control={control}
                    name={`cargos.${index}.cargoDescription`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="whitespace-nowrap">
                          Cargo Description{" "}
                          <span className="text-red-600">*</span>
                        </FormLabel>

                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Cargo Description"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="pt-6 size-11">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant={"outline"} className="w-full size-11 ">
                        <SquareArrowOutUpRight />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-screen-sm">
                      <DialogHeader>
                        <DialogTitle>Cargo Description</DialogTitle>
                      </DialogHeader>
                      <hr />
                      <div className="">
                        <FormField
                          control={control}
                          name={`cargos.${index}.cargoDescription`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Cargo Description{" "}
                                <span className="text-red-600">*</span>
                              </FormLabel>

                              <FormControl>
                                <Textarea
                                  placeholder="Cargo Description"
                                  {...field}
                                  className="mt-2 min-h-44"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </div>
            <div className="col-span-2">
              <FormField
                control={control}
                name={`cargos.${index}.cargoWeight`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="line-clamp-1">
                      Cargo Weight (Excl. Tare){" "}
                      <span className="text-red-600">*</span>
                    </FormLabel>

                    <FormControl>
                      <Input type="text" placeholder="00" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="col-span-3 ">
              <div className="flex justify-between gap-4 ">
                <div className="w-full">
                  <FormField
                    control={control}
                    name={`cargos.${index}.netWeight`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Net Weight</FormLabel>
                        <FormControl>
                          <Input type="text" placeholder="00" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="pt-5 ">
                  <FormField
                    control={control}
                    name={`cargos.${index}.netWeightUnit`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={"KG"}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="KG" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="KG">KG</SelectItem>
                              <SelectItem value="LBS">LBS</SelectItem>
                              <SelectItem value="TNE">TNE</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="col-span-3">
              <div className="flex justify-between gap-4 ">
                <div className="w-full">
                  <FormField
                    control={control}
                    name={`cargos.${index}.grossVolume`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gross Volume</FormLabel>
                        <FormControl>
                          <Input type="text" placeholder="00" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="pt-5 ">
                  <FormField
                    control={control}
                    name={`cargos.${index}.grossVolumeUnit`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={"CBM"}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="CBM" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CBM">CBM</SelectItem>
                              <SelectItem value="CFT">CFT</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="col-span-1">
              <div className="flex items-end justify-end h-full gap-2">
                <Button
                  type="button"
                  className="bg-secondary hover:bg-secondary/90"
                  onClick={() =>
                    cargosArray.append({
                      hsCode: "",
                      cargoDescription: "",
                      cargoWeight: "",
                      netWeight: "",
                      netWeightUnit: "KG",
                      grossVolume: "",
                      grossVolumeUnit: "CBM",
                    })
                  }
                >
                  <Plus />
                </Button>
                {cargosArray?.fields?.length > 1 && (
                  <Button
                    onClick={() => cargosArray?.remove(index)}
                    type="button"
                    variant="outline"
                    size="icon"
                  >
                    <Minus />
                  </Button>
                )}
              </div>
            </div>
          </div>
          {index === cargosArray?.fields?.length - 1 ? "" : <hr />}
        </Fragment>
      ))}
    </div>
  );
};

export default CargoSection;
