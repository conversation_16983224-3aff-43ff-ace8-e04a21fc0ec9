import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON> } from "@/components/ui/scroll-area";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Typography } from "@/components/typography";
import { FC, Fragment, useState } from "react";
import { BookingStatusEnums, MyBookingResponseType } from "@/types/booking";
import dayjs from "dayjs";
import { InfiniteData } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { Container, EllipsisVertical } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import TrackContainerPage from "../TrackContainer";

interface MyBookingListTableProps {
  myBookingData?:
    | InfiniteData<
        {
          message: MyBookingResponseType;
          page_size: number;
          total_count: number;
        },
        unknown
      >
    | undefined;
}

const MyBookingListTable: FC<MyBookingListTableProps> = ({ myBookingData }) => {
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);

  const navigate = useNavigate();
  const { role } = useAuthContext();

  return (
    <div>
      <div className="pt-6 overflow-y-auto">
        <ScrollArea className="whitespace-nowrap">
          <Table className="table-auto border-1 border-[#D3DAE7] w-full">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    BKG ID
                  </Typography>
                  <Typography variant={"small"} weight={"medium"}>
                    INTTRA #
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    CARRIER
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    SRC
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    PARENT BKG
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    CONT
                  </Typography>
                  <Typography variant={"p"} weight={"medium"}>
                    QTY
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    POL
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    POD
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    VESSEL/VOYAGE
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    SI DATE
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    CY DATE
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    B/L NO.
                  </Typography>
                </TableHead>
                {role === "Vendor" || role === "Admin" ? (
                  <TableHead className="p-3">
                    <Typography variant={"p"} weight={"medium"}>
                      JOB ID'S
                    </Typography>
                  </TableHead>
                ) : (
                  ""
                )}
                {role === "Admin" ? (
                  <TableHead className="p-3">
                    <Typography variant={"p"} weight={"medium"}>
                      SI STATUS
                    </Typography>
                  </TableHead>
                ) : (
                  ""
                )}
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    STATUS
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"p"} weight={"medium"}>
                    ACTION
                  </Typography>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {myBookingData?.pages?.map((page, i) => (
                <Fragment key={i}>
                  {page?.message?.bookings?.map((booking, index) => {
                    let color = "";
                    let bgColor = "";
                    let label = "";
                    switch (booking?.booking_status) {
                      case BookingStatusEnums.CONFIRM:
                        color = "#339D59";
                        bgColor = "#B4FFCF";
                        label = "CONFIRMED";
                        break;
                      case BookingStatusEnums.CANCEL:
                        color = "#d32535";
                        bgColor = "#FFC7C2";
                        label = "CANCELED";
                        break;
                      case BookingStatusEnums.REQUEST:
                        color = "#4371C3";
                        bgColor = "#C9DCFF";
                        label = "REQUESTED";
                        break;
                      case BookingStatusEnums.AMEND:
                        color = "#FD7E14 ";
                        bgColor = "#FFF3D2";
                        label = "AMENDED";
                        break;
                      case BookingStatusEnums.DECLINE:
                        color = "#6c757d";
                        bgColor = "#b4b9be";
                        label = "DECLINED";
                        break;
                      case BookingStatusEnums.REPLACE:
                        color = "#dfa700";
                        bgColor = "#ffe8a4";
                        label = "REPLACED";
                        break;
                      default:
                        color = "#a02632";
                        bgColor = "#dd727d";
                    }

                    let siStatusColor = "";
                    switch (booking?.si_carrier_status?.toLowerCase()) {
                      case "pending":
                        siStatusColor = "#FF7C3B";
                        break;
                      case "accepted":
                        siStatusColor = "#12BF00";
                        break;
                      case "rejected":
                        siStatusColor = "#BF0020";
                        break;
                      default:
                        siStatusColor = "#292727";
                    }

                    const jobIds = booking?.job_ids?.length
                      ? booking?.job_ids?.split(",")
                      : [];

                    return (
                      <TableRow key={index}>
                        <TableCell className="px-3 py-1 align-top">
                          <div className="">
                            <Typography
                              onClick={() =>
                                navigate(
                                  `/dashboard/booking/booking-confirmation/${booking?.booking_id}?carrierName=${booking?.party_name}`
                                )
                              }
                              className="cursor-pointer hover:underline"
                            >
                              {booking?.carrier_booking_number ?? "N/A"}
                            </Typography>
                            <Typography variant={"muted"} className="">
                              {booking?.inttra_reference
                                ? booking?.inttra_reference
                                : "-"}
                            </Typography>
                          </div>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.party_short_name
                              ? booking?.party_short_name
                              : "-"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.out_side_booking === 1 ? "C" : "I"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.carrier_source_booking_number
                              ? booking?.carrier_source_booking_number
                              : booking?.carrier_booking_number}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.total_containers ? (
                              <span className="flex items-center gap-1">
                                {booking?.total_containers}{" "}
                                <Container size={20} />
                              </span>
                            ) : (
                              "-"
                            )}
                          </Typography>
                        </TableCell>

                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.place_of_carrier_receipt?.location_name ??
                              "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.place_of_carrier_delivery
                              ?.location_name ?? "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.main_carriage?.length
                              ? booking?.main_carriage[0]?.vessel || "-"
                              : "N/A"}
                          </Typography>
                          <Typography variant={"small"}>
                            {booking?.main_carriage?.length
                              ? booking?.main_carriage[0]?.voyage || "-"
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.si_due_date &&
                            dayjs(booking?.si_due_date)?.isValid()
                              ? dayjs(booking?.si_due_date)?.format(
                                  "MMM-DD-YYYY"
                                )
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.cy_date &&
                            dayjs(booking?.cy_date)?.isValid()
                              ? dayjs(booking?.cy_date)?.format("MMM-DD-YYYY")
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="px-3 py-1 align-top">
                          <Typography>
                            {booking?.bill_of_lading
                              ? booking?.bill_of_lading
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        {role === "Vendor" || role === "Admin" ? (
                          <TableCell className="px-3 py-1 align-top">
                            {jobIds?.length > 0 ? (
                              jobIds?.length > 2 ? (
                                <>
                                  <Typography
                                    className="hover:cursor-pointer hover:bg-blue-100 hover:shadow rounded px-2 py-0.5"
                                    onClick={() =>
                                      role === "Admin"
                                        ? navigate(
                                            `/dashboard/admin-job-details/${jobIds[0]?.trim()}`
                                          )
                                        : `/dashboard/vendors/job-view/${jobIds[0]?.trim()}`
                                    }
                                  >
                                    {jobIds[0]}
                                  </Typography>
                                  <Popover>
                                    <PopoverTrigger>
                                      <Typography>
                                        <span className="text-xs text-blue-500 border rounded-full border-blue-600 px-2 py-0.5 bg-blue-100/80 ">
                                          Click for More
                                        </span>
                                      </Typography>
                                    </PopoverTrigger>
                                    <PopoverContent>
                                      {" "}
                                      <div className="space-y-1 divide-y">
                                        {jobIds?.map((str) => (
                                          <Typography
                                            className="hover:cursor-pointer hover:bg-blue-100 hover:shadow rounded px-2 py-0.5"
                                            onClick={() =>
                                              role === "Admin"
                                                ? navigate(
                                                    `/dashboard/admin-job-details/${str?.trim()}`
                                                  )
                                                : navigate(
                                                    `/dashboard/vendors/job-view/${str?.trim()}`
                                                  )
                                            }
                                          >
                                            {str}
                                          </Typography>
                                        ))}
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </>
                              ) : (
                                jobIds?.map((str) => (
                                  <Typography
                                    onClick={() =>
                                      role === "Admin"
                                        ? navigate(
                                            `/dashboard/admin-job-details/${str?.trim()}`
                                          )
                                        : navigate(
                                            `/dashboard/vendors/job-view/${str?.trim()}`
                                          )
                                    }
                                    className="hover:cursor-pointer hover:bg-blue-100 hover:shadow rounded px-2 py-0.5"
                                  >
                                    {str}
                                  </Typography>
                                ))
                              )
                            ) : (
                              <Typography className="">N/A</Typography>
                            )}
                          </TableCell>
                        ) : (
                          ""
                        )}
                        {role === "Admin" ? (
                          <TableCell className="px-3 py-1 align-top">
                            {booking?.si_id ? (
                              <>
                                <Typography
                                  onClick={() => {
                                    if (
                                      booking?.si_carrier_status === "Draft"
                                    ) {
                                      navigate(
                                        `/dashboard/booking/my-booking/create-si/${booking?.booking_id}&draft_id=${booking?.si_id}`
                                      );
                                    } else {
                                      navigate(
                                        `/dashboard/documentation/my-shipping-instruction/shipping-confirmation/${booking?.si_id}`
                                      );
                                    }
                                  }}
                                  className="cursor-pointer hover:underline"
                                >
                                  {booking?.si_id}
                                </Typography>
                                {booking?.si_carrier_status ? (
                                  <Typography
                                    style={{ backgroundColor: siStatusColor }}
                                    className="uppercase px-2 py-0.5 text-white text-center rounded-sm"
                                    variant={"small"}
                                  >
                                    {booking?.si_carrier_status}
                                  </Typography>
                                ) : (
                                  ""
                                )}
                              </>
                            ) : (
                              <>
                                <Typography>N/A</Typography>
                              </>
                            )}
                          </TableCell>
                        ) : (
                          ""
                        )}
                        <TableCell className="px-3 py-1 align-top">
                          <div
                            style={{ backgroundColor: bgColor }}
                            className={`flex justify-center px-2 py-0.5 rounded-sm`}
                          >
                            <Typography
                              variant={"small"}
                              style={{ color: color }}
                            >
                              {label ? label : booking?.booking_status}
                            </Typography>
                          </div>
                        </TableCell>

                        <TableCell className="flex justify-end py-1 pl-2 pr-5">
                          <Popover>
                            <PopoverTrigger asChild>
                              <EllipsisVertical />
                            </PopoverTrigger>
                            <PopoverContent className="w-40">
                              <div className="grid gap-4">
                                <div className="flex flex-col items-start gap-2">
                                  <Button
                                    variant={"ghost"}
                                    type="button"
                                    className="w-full "
                                    onClick={() =>
                                      navigate(
                                        `/dashboard/booking/booking-confirmation/${booking?.booking_id}?carrierName=${booking?.party_name}`
                                      )
                                    }
                                  >
                                    View
                                  </Button>
                                  {role === "Admin" ? (
                                    <>
                                      {booking?.booking_status ===
                                        BookingStatusEnums.CONFIRM ||
                                      booking?.booking_status ===
                                        BookingStatusEnums.REQUEST ? (
                                        <Button
                                          type="button"
                                          variant={"ghost"}
                                          className="w-full "
                                          onClick={() =>
                                            navigate(
                                              `/dashboard/booking/booking-request?amend_id=${booking?.booking_id}`
                                            )
                                          }
                                        >
                                          Amend
                                        </Button>
                                      ) : (
                                        ""
                                      )}
                                      {booking?.booking_status ===
                                      BookingStatusEnums.CONFIRM ? (
                                        <Button
                                          type="button"
                                          variant={"ghost"}
                                          className="w-full "
                                          onClick={() =>
                                            navigate(
                                              `/dashboard/booking/my-booking/create-job/${booking?.booking_id}`
                                            )
                                          }
                                        >
                                          Create Job
                                        </Button>
                                      ) : (
                                        ""
                                      )}
                                    </>
                                  ) : (
                                    ""
                                  )}

                                  <Button
                                    type="button"
                                    variant={"ghost"}
                                    className="w-full"
                                    onClick={() => {
                                      setDrawerOpen(true);
                                    }}
                                  >
                                    Track Booking
                                  </Button>
                                </div>
                                <TrackContainerPage
                                  open={drawerOpen}
                                  onOpenChange={(open) => setDrawerOpen(open)}
                                  inttraReferenceId={booking?.inttra_reference}
                                  carrierName={booking?.party_name}
                                  carrierBookingReferenceId={String(
                                    booking?.carrier_booking_number
                                  )}
                                  // inttraReferenceId={'2001070988'}
                                />
                              </div>
                            </PopoverContent>
                          </Popover>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </Fragment>
              ))}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </div>
  );
};

export default MyBookingListTable;
