import { useState } from "react";
import { Typography } from "@/components/typography";
import { But<PERSON> } from "@/components/ui/button";
import { Truck, Printer, Plus } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CancelBooking, fetchBookingConfirmation } from "@/services/admin/booking";
import { format, parseISO } from "date-fns";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import Loader from "@/components/Loader";
import TrackContainerPage from "../TrackContainer";
import { formatDate, formatDateTime } from "@/utils/functions";
import { BookingStatusEnums } from "@/types/booking";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import dayjs from "dayjs";

function BookingConfirmationView() {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const carrierName = searchParams.get("carrierName") || "N/A";

  const { role } = useAuthContext();

  const navigate = useNavigate();
  const [drawerOpen, setDrawerOpen] = useState(false);

  const {
    data: bookingConfirmationData,
    error: bookingConfirmationError,
    isFetching: bookingConfirmationFetching,
  } = useQuery({
    queryKey: ["fetchBookingConfirmation"],
    queryFn: () => fetchBookingConfirmation(id as string),
  });

  const confirmationData = bookingConfirmationData?.message;

  function haulageLabel(code: string) {
    switch (code) {
      case "MerchantCarrier":
        return "Merchant Haulage At Origin, Carrier Haulage At Destination (MC)";
      case "MerchantMerchant":
        return "Merchant Haulage At Origin, Merchant Haulage At Destination (MM)";
      case "CarrierCarrier":
        return "Carrier Haulage At Origin, Carrier Haulage At Destination (CC)";
      case "CarrierMerchant":
        return "Carrier Haulage At Origin, Merchant Haulage At Destination (CM)";
      default:
        return " ";
    }
  }

  // Submit Booking Handler.
  const {
    mutate: mutateBookingCancel,
    isPending: isPendingBookingCancel,
    isError: isErrorBookingCancel,
    isSuccess: isSuccessBookingCancel,
  } = useMutation({
    mutationFn: CancelBooking,
    onSuccess: (data) => {
      queryClient.setQueryData(["fetchBookingConfirmation"], (oldData: any) => {
        return {
          ...oldData,
          message: {
            ...oldData?.message,
            booking_status: "CANCEL",
          },
        };
      });
      toast.success("Booking Cancelled successfully!");
    },
    onError: () => {
      toast.error("Failed to cancel booking. Please try again.");
    },
  });

  return (
    <>
      {bookingConfirmationFetching ? (
        <div className="w-full h-[50dvh] flex justify-center items-center">
          <Loader />
        </div>
      ) : (
        <div className="bg-white text-sm text-gray-800 p-6 space-y-6 print-area print confirmation-div">
          <div className="flex flex-col ">
            <div className="flex flex-col gap-1.5">
              <Typography variant={"large"}>Product Channel - {confirmationData?.product_channel}</Typography>
              {confirmationData?.booking_status === "CONFIRM" && (
                <Typography variant={"large"}>Carrier Response Type: {"Accepted"}</Typography>
              )}
              <Typography variant="large" weight="medium">
                The following Booking was {confirmationData?.booking_status === "CONFIRM" && `Confirmed`}
                {confirmationData?.booking_status === "REQUEST" && `Requested`}
                {confirmationData?.booking_status === "AMEND" && `Amended`}
                {confirmationData?.booking_status === "CANCEL" && `Cancelled`} on{" "}
                {confirmationData?.modified_date &&
                  format(parseISO(confirmationData.modified_date), "EEEE, MMM d, yyyy 'at' HH:mm 'GMT'")}
              </Typography>
            </div>
            <div className="flex justify-end gap-1.5">
              <Button
                size={"lg"}
                variant={"outline"}
                className="border-sidebar print:hidden"
                onClick={() => {
                  setDrawerOpen(true);
                }}
              >
                <Truck /> <span className="">Track Container</span>
              </Button>
              <Button
                size={"lg"}
                variant={"outline"}
                className="print:hidden border-sidebar"
                onClick={() => window.print()}
              >
                <Printer /> <span className="">Printer-Friendly</span>
              </Button>
              {role === "Admin" ? (
                <>
                  {bookingConfirmationData?.message?.booking_status === BookingStatusEnums.CONFIRM ||
                  bookingConfirmationData?.message?.booking_status === BookingStatusEnums.REQUEST ||
                  bookingConfirmationData?.message?.booking_status === BookingStatusEnums.AMEND ? (
                    <AlertDialog>
                      <AlertDialogTrigger>
                        <Button
                          disabled={isPendingBookingCancel}
                          size={"lg"}
                          className="border-orange-600 bg-orange-500 hover:bg-orange-400 print:hidden"
                        >
                          {isPendingBookingCancel ? "Canceling.." : "Cancel"}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently cancel your booking.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Not sure</AlertDialogCancel>
                          <AlertDialogAction
                            className="bg-red-600 hover:bg-red-600/90"
                            onClick={() => mutateBookingCancel(bookingConfirmationData?.message?.name)}
                          >
                            Sure, Cancel
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    ""
                  )}
                  {bookingConfirmationData?.message?.booking_status === BookingStatusEnums.CONFIRM ||
                  bookingConfirmationData?.message?.booking_status === BookingStatusEnums.AMEND ? (
                    <Button
                      onClick={() => {
                        if (confirmationData?.si_main_status === "Draft") {
                          navigate(
                            `/dashboard/booking/my-booking/create-si/${confirmationData?.name}&draft_id=${confirmationData?.draft_si_id}`
                            );
                              } else {
                                  navigate(
                                    `/dashboard/booking/my-booking/create-si/${confirmationData?.name}`
                                    );
                                  } 
                    }
                  }
                      size={"lg"}
                      className="border-sidebar bg-sidebar hover:bg-sidebar/80 print:hidden"
                    >
                      <Plus /> <span className="">Create SI</span>
                    </Button>
                  ) : (
                    ""
                  )}
                </>
              ) : (
                ""
              )}
            </div>
          </div>
          <div className="border-1 border-[#D3DAE7] rounded-sm mt-3">
            <div className="py-4 px-6 bg-[#D3DAE7]">
              <Typography variant={"h4"}>Carrier Comments</Typography>
            </div>
            <div className="py-4 px-6">
              <Typography variant={"large"}>
                {confirmationData?.general_comments && confirmationData?.general_comments}
              </Typography>
            </div>
          </div>
          <div className="mt-5 ">
            <Table className="mt-2 border-1 border-[#D3DAE7] ">
              <TableHeader className="[&_tr]:border-b-0">
                <TableRow className="border-b-0">
                  <TableHead className="border-e-1 p-5 w-1/4">
                    <Typography weight={"semibold"}>Booker</Typography>
                  </TableHead>
                  <TableHead className="border-e-1 p-5 w-1/4">
                    <Typography weight={"semibold"}>Shipper</Typography>
                  </TableHead>
                  <TableHead className="border-e-1 p-5 w-1/4">
                    <Typography weight={"semibold"}>Carrier/NVOCC/Booking Agent</Typography>
                  </TableHead>
                  <TableHead className="p-5 w-1/4">
                    <Typography weight={"semibold"}>INTTRA Reference Number</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell rowSpan={3} className="border-e-1 px-5 align-top">
                    <Typography>{confirmationData?.booking_agent ?? " "}</Typography>
                  </TableCell>
                  <TableCell rowSpan={3} className="border-e-1 px-5 align-top">
                    <Typography>INTTRA ID: {confirmationData?.shipper?.[0]?.inttra_company_id}</Typography>
                    {confirmationData?.shipper?.[0]?.shipper_name ||
                    confirmationData?.shipper?.[0]?.shipper_address ||
                    confirmationData?.shipper?.[0]?.postal_code ? (
                      <div>
                        <Typography className="uppercase">{confirmationData?.shipper?.[0]?.shipper_name}</Typography>
                        <Typography>{confirmationData?.shipper?.[0]?.shipper_address}</Typography>
                        <Typography>Postal Code: {confirmationData?.shipper?.[0]?.postal_code ?? " "}</Typography>
                      </div>
                    ) : (
                      <div>
                        {confirmationData?.shipper_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </div>
                    )}

                    <div className="mt-10">
                      <Typography weight={"semibold"}>Contact</Typography>
                      <Typography>Name: {confirmationData?.shipper?.[0]?.contact_name}</Typography>
                      <Typography>
                        Phone Number:
                        {confirmationData?.shipper?.[0]?.shipper_phone ?? " "}
                      </Typography>
                    </div>
                  </TableCell>
                  <TableCell className="border-e-1 px-5 align-top">
                    <Typography>{confirmationData?.carrier?.[0]?.carrier_name ?? " "}</Typography>
                    <Typography>INTTRA ID: {confirmationData?.carrier?.[0]?.inttra_id ?? " "}</Typography>
                    <Typography>{confirmationData?.booking_agent}</Typography>
                    <Typography>{confirmationData?.carrier?.[0]?.address ?? " "}</Typography>
                    <Typography className="mt-4">
                      Postal Code: {confirmationData?.carrier?.[0]?.postal_code ?? " "}
                    </Typography>
                    <Typography>Country Code: {confirmationData?.carrier?.[0]?.country_code ?? " "}</Typography>
                  </TableCell>
                  <TableCell className="px-5 align-top">
                    <Typography>{confirmationData?.inttra_reference ?? " "}</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="border-e-1 p-5 align-top">
                    <Typography weight={"semibold"}>Carrier Booking Number</Typography>
                    <Typography>{confirmationData?.carrier_booking_number}</Typography>

                    {confirmationData?.parent_carrier_booking_number && (
                      <>
                        <Typography className="pt-3">{""}</Typography>
                        <Typography weight={"semibold"}>Parent Carrier Booking Number</Typography>
                        <Typography>{confirmationData?.parent_carrier_booking_number}</Typography>
                      </>
                    )}
                    {confirmationData?.carrier_source_booking_number && (
                      <>
                        <Typography className="pt-3">{""}</Typography>
                        <Typography weight={"semibold"}>Carrier Source Booking Number</Typography>
                        <Typography>{confirmationData?.carrier_source_booking_number}</Typography>
                      </>
                    )}
                  </TableCell>

                  <TableCell className="p-5 align-top">
                    <Typography weight={"semibold"}>Customer Shipment ID</Typography>
                    <Typography>{confirmationData?.customer_shipment_id}</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="border-e-1 flex flex-col h-auto">
                    <div className="px-5 pt-3 h-1/3">
                      <Typography weight={"semibold"}>Reference Numbers</Typography>
                    </div>
                    {confirmationData?.contract_number && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Contract Number</Typography>
                        <Typography>{confirmationData?.contract_number ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.contract_party_reference_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Contract Party Reference Number(s)</Typography>
                        <Typography>{confirmationData?.contract_party_reference_numbers ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.bl_reference_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>B/L Reference Number(s)</Typography>
                        <Typography>{confirmationData?.bl_reference_numbers ?? " "}</Typography>
                      </div>
                    )}

                    {confirmationData?.consignees_reference_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Consignee's Reference Number(s)</Typography>
                        <Typography>{confirmationData?.consignees_reference_numbers ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.forwarders_reference_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Freight Forwarder's Reference Number(s)</Typography>
                        <Typography>{confirmationData?.forwarders_reference_numbers ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.shippers_reference_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Shipper's Reference Number(s)</Typography>
                        <Typography>{confirmationData?.shippers_reference_numbers ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.purchase_order_numbers && (
                      <div className="px-5 pt-3 h-1/3">
                        <Typography weight={"semibold"}>Purchase Order Number(s)</Typography>
                        <Typography>{confirmationData?.purchase_order_numbers ?? " "}</Typography>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="p-5 align-top">
                    <Typography weight={"semibold"}>Booking Office</Typography>
                    <Typography>{confirmationData?.booking_office}</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 border-e-1 align-top">
                    <Typography className="mb-3" weight={"semibold"}>
                      Forwarder
                    </Typography>
                    <Typography>
                      {confirmationData?.forwarder
                        ? confirmationData?.forwarder
                        : confirmationData?.forwarder_on_booking}
                    </Typography>
                  </TableCell>
                  <TableCell className="p-5 border-e-1 align-top">
                    <Typography className="mb-3" weight={"semibold"}>
                      Consignee
                    </Typography>
                    {confirmationData?.consignee?.[0]?.consigne_name ||
                    confirmationData?.consignee?.[0]?.company_pass_through_code ||
                    confirmationData?.consignee?.[0]?.address ? (
                      <>
                        <Typography>
                          Name: {""} {confirmationData?.consignee?.[0]?.consigne_name}
                        </Typography>
                        <Typography>
                          Company Pass Through Code:{" "}
                          {confirmationData?.consignee?.[0]?.company_pass_through_code ?? " "}
                        </Typography>
                        <Typography>{confirmationData?.consignee?.[0]?.address}</Typography>
                        <Typography></Typography>
                      </>
                    ) : (
                      <div>
                        {confirmationData?.consignee_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="p-5 align-top" colSpan={2}>
                    <div className="h-24">
                      <Typography weight={"semibold"}>Additional Booking Details</Typography>
                    </div>
                    <div className="h-24">
                      <Typography weight={"semibold"}>Filing Of Cargo Manifest With Customs</Typography>
                      <Typography> </Typography>
                    </div>
                    {confirmationData?.vgm_due_date && (
                      <div className="mb-3">
                        <Typography weight={"semibold"}>VGM Cut-Off Date</Typography>
                        <Typography>{dayjs(confirmationData?.vgm_due_date)?.format("MMM DD YYYY") ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.si_due_date && (
                      <div className="mb-5">
                        <Typography weight={"semibold"}>SI Document Due Date</Typography>
                        <Typography>{dayjs(confirmationData?.si_due_date)?.format("MMM DD YYYY") ?? " "}</Typography>
                      </div>
                    )}
                    {confirmationData?.first_foreign_port_of_acceptance && (
                      <div className="mb-5">
                        <Typography weight={"semibold"}>First Foreign Place/Port of Acceptance</Typography>
                        <Typography>{confirmationData?.first_foreign_port_of_acceptance}</Typography>
                      </div>
                    )}
                    {confirmationData?.first_us_port && (
                      <div className="mb-5">
                        <Typography weight={"semibold"}>First US Port Visited {""}</Typography>
                        <Typography>{confirmationData?.first_us_port}</Typography>
                        <Typography>
                          Arrival Date at US First US Port: {""}
                          {/* Tue Feb 18 2025 00:00:00
                    GMT+0530 (India Standard Time) */}
                          {confirmationData?.estimated_arrival_date &&
                            format(
                              parseISO(confirmationData.estimated_arrival_date),
                              "EEEE, MMM d, yyyy 'at' HH:mm 'GMT'"
                            )}
                        </Typography>
                      </div>
                    )}

                    {confirmationData?.last_non_us_port && (
                      <div className="mb-5">
                        <Typography weight={"semibold"}>Last Non-US Port Visited {""}</Typography>
                        <Typography>{confirmationData?.last_non_us_port}</Typography>
                      </div>
                    )}
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell rowSpan={2} className="border-e-1 p-5 align-top">
                    <Typography weight={"semibold"} className="mb-3">
                      Contract Party
                    </Typography>
                    {confirmationData?.contract_party?.[0]?.party_name ||
                    confirmationData?.contract_party?.[0]?.address ||
                    confirmationData?.contract_party?.[0]?.email ||
                    confirmationData?.contract_party?.[0]?.postal_code ? (
                      <>
                        {/* <Typography>
                          INTTRA ID:{" "}
                          {confirmationData?.contract_party?.[0]
                            ?.inttra_company_id ?? " "}
                        </Typography> */}
                        {confirmationData?.contract_party?.[0]?.party_name && (
                          <Typography className="mb-3">{confirmationData.contract_party?.[0]?.party_name}</Typography>
                        )}
                        <Typography>{confirmationData?.contract_party?.[0]?.address ?? " "}</Typography>
                        <Typography>Email: {confirmationData?.contract_party?.[0]?.email ?? " "}</Typography>
                        <Typography>
                          Postal Code: {confirmationData?.contract_party?.[0]?.postal_code ?? " "}
                        </Typography>
                      </>
                    ) : (
                      <div>
                        {confirmationData?.contract_party_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </div>
                    )}
                  </TableCell>
                  <TableCell rowSpan={2} className="border-e-1 p-5 align-top">
                    <Typography weight={"semibold"} className="mb-3">
                      Notify Party Details
                    </Typography>

                    <Typography weight={"semibold"} className="mt-3">
                      Main Notify Party
                    </Typography>

                    {(confirmationData?.main_notify_party?.length ?? 0) > 0 ? (
                      confirmationData.main_notify_party.map((details, index) => (
                        <Typography key={index}>
                          {Object.entries(details)
                            .filter(([key]) => ["consigne_name", "address", "postal_code"].includes(key))
                            .map(([key, value]) => (
                              <div key={key}>{key === "postal_code" ? `Postal Code: ${value ?? ""}` : value ?? ""}</div>
                            ))}
                        </Typography>
                      ))
                    ) : (
                      <Typography>
                        {confirmationData?.notify_party_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </Typography>
                    )}
                    <Typography weight={"semibold"} className="mt-3">
                      First Additional Notify Party
                    </Typography>
                    {(confirmationData?.additional_notify_party_one?.length ?? 0) > 0 ? (
                      confirmationData.additional_notify_party_one.map((details, index) => (
                        <Typography key={index}>
                          {Object.entries(details)
                            .filter(([key]) => ["consigne_name", "address", "postal_code"].includes(key))
                            .map(([key, value]) => (
                              <div key={key}>{key === "postal_code" ? `Postal Code: ${value ?? ""}` : value ?? ""}</div>
                            ))}
                        </Typography>
                      ))
                    ) : (
                      <Typography>
                        {confirmationData?.notify_party_1_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </Typography>
                    )}

                    <Typography weight={"semibold"} className="mt-3">
                      Second Additional Notify Party
                    </Typography>
                    {(confirmationData?.additional_notify_party_two?.length ?? 0) > 0 ? (
                      confirmationData.additional_notify_party_two.map((details, index) => (
                        <Typography key={index}>
                          {Object.entries(details)
                            .filter(([key]) => ["consigne_name", "address", "postal_code"].includes(key))
                            .map(([key, value]) => (
                              <div key={key}>{key === "postal_code" ? `Postal Code: ${value ?? ""}` : value ?? ""}</div>
                            ))}
                        </Typography>
                      ))
                    ) : (
                      <Typography>
                        {confirmationData?.notify_party_2_on_booking?.split("\n").map((line, index) => (
                          <Typography key={index}>{line}</Typography>
                        ))}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell className="p-5 align-top" colSpan={2}>
                    <Typography weight={"semibold"} className="mb-3">
                      Freight Payer Party
                    </Typography>
                    <Typography>INTTRA ID: {confirmationData?.shipper?.[0]?.inttra_company_id ?? ""} </Typography>
                    <Typography>{confirmationData?.shipper?.[0]?.shipper_name ?? ""}</Typography>
                    <Typography>{confirmationData?.shipper?.[0]?.shipper_address ?? ""}</Typography>
                    <Typography></Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 align-top" colSpan={2}>
                    <Typography weight={"semibold"}>Customs Broker</Typography>
                  </TableCell>
                  <Typography>
                    {confirmationData?.customs_broker_on_booking?.split("\n").map((line, index) => (
                      <Typography key={index}>{line}</Typography>
                    ))}
                  </Typography>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 align-top border-e-1">
                    <Typography weight={"semibold"} className="mb-3">
                      Move Type
                    </Typography>
                    <Typography>{confirmationData?.move_type ?? " "}</Typography>
                  </TableCell>
                  <TableCell className="p-5 border-e-1 align-top">
                    <Typography weight={"semibold"} className="mb-3">
                      Place of Carrier Reciept
                    </Typography>
                    <Typography>{confirmationData?.place_of_carrier_receipt ?? " "}</Typography>
                    {confirmationData?.earliest_departure_date && (
                      <>
                        <Typography weight={"semibold"} className="mb-2 mt-3">
                          Earliest Departure Date
                        </Typography>
                        <Typography>
                          {dayjs(confirmationData?.earliest_departure_date)?.format("MMM DD YYYY") ?? " "}
                        </Typography>
                      </>
                    )}
                  </TableCell>
                  <TableCell className="p-5 align-top" colSpan={2}>
                    <Typography weight={"semibold"} className="mb-3">
                      Place of Carrier Delivery
                    </Typography>
                    <Typography>{confirmationData?.place_of_carrier_delivery ?? " "}</Typography>

                    {confirmationData?.latest_delivery_date && (
                      <>
                        <Typography weight={"semibold"} className="mb-2 mt-3">
                          Latest Delivery Date
                        </Typography>
                        <Typography>
                          {dayjs(confirmationData?.latest_delivery_date)?.format("MMM DD YYYY") ?? " "}
                        </Typography>
                      </>
                    )}
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 align-top border-e-1">
                    <Typography weight={"semibold"} className="mb-3">
                      Carrier, Vessel, Country, Lloyd's Code, Voyage
                    </Typography>
                    <Typography>
                      {confirmationData?.carrier?.[0]?.carrier_sac ?? "-- "},{" "}
                      {confirmationData?.main_carriage?.[0]?.vessel_name ?? "-- "},{" "}
                      {confirmationData?.main_carriage?.[0]?.country ?? "-- "},{" "}
                      {confirmationData?.main_carriage?.[0]?.lloyds_code ?? "-- "},{" "}
                      {confirmationData?.main_carriage?.[0]?.voyage_number ?? "-- "} {""}
                    </Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-e-1">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Main Port of Load</Typography>
                      <Typography>
                        {confirmationData?.main_carriage?.[0]
                          ? `${confirmationData?.main_carriage?.[0]?.port_of_load?.location_name}, ${confirmationData?.main_carriage?.[0]?.port_of_load?.country} (${confirmationData?.main_carriage?.[0]?.port_of_load?.locode})`
                          : " "}
                      </Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Estimated Sail Date</Typography>
                      <Typography>
                        {dayjs(confirmationData?.main_carriage?.[0]?.etd)?.format("MMM DD YYYY") ?? " "}
                      </Typography>
                    </div>
                  </TableCell>
                  <TableCell className="p-5 align-top" colSpan={2}>
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Main Port of Discharge</Typography>
                      <Typography>
                        {confirmationData?.main_carriage?.[confirmationData?.main_carriage.length - 1]
                          ? `${
                              confirmationData?.main_carriage?.[confirmationData?.main_carriage.length - 1]
                                ?.port_of_discharge?.location_name
                            } , ${
                              confirmationData?.main_carriage?.[confirmationData?.main_carriage.length - 1]
                                ?.port_of_discharge?.country
                            } (${
                              confirmationData?.main_carriage?.[confirmationData?.main_carriage.length - 1]
                                ?.port_of_discharge?.locode
                            })`
                          : " "}
                      </Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Estimated Arrival Date</Typography>
                      <Typography>
                        {dayjs(
                          confirmationData?.main_carriage?.[confirmationData?.main_carriage.length - 1]?.eta
                        )?.format("MMM DD YYYY") ?? " "}
                      </Typography>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="mt-[60px]">
            <Table className="border-1 border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="p-5">
                    <Typography variant={"h4"} weight={"medium"}>
                      Transport Plan Details
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {confirmationData?.pre_carriage &&
                  confirmationData?.pre_carriage?.length > 0 &&
                  confirmationData.pre_carriage.map((preCarriageData, index) => (
                    <TableRow>
                      <TableCell className="p-5">
                        <Typography weight={"semibold"} className="mb-3">
                          Pre Carriage {index + 1}
                        </Typography>
                        <Typography>
                          {`Transport Mode: ${preCarriageData?.mode ?? "N/A"}`}{" "}
                          {preCarriageData?.mean ? `/ Conveyance Type: ${preCarriageData?.mean}` : ""}
                          {confirmationData?.carrier?.[0]?.carrier_sac
                            ? ` / Carrier SCAC: ${confirmationData.carrier[0].carrier_sac}`
                            : ""}
                        </Typography>
                        <Typography>
                          {`Start Location: ${preCarriageData?.start?.location_name ?? "N/A"}, ${
                            preCarriageData?.start?.country ?? "N/A"
                          } (${preCarriageData?.start?.locode ?? "N/A"})
                            
                             / Estimated Departure Date: ${
                               dayjs(preCarriageData?.etd)?.format("MMM DD YYYY") ?? "N/A"
                             }`}
                        </Typography>
                        <Typography>
                          {`End Location: ${preCarriageData?.end?.location_name ?? "N/A"}, ${
                            preCarriageData?.end?.country ?? "N/A"
                          } (${preCarriageData?.end?.locode ?? "N/A"}) / Estimated Arrival Date: ${
                            dayjs(preCarriageData?.eta)?.format("MMM DD YYYY") ?? "N/A"
                          }`}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                {confirmationData?.main_carriage &&
                  confirmationData?.main_carriage?.length > 0 &&
                  confirmationData.main_carriage.map((mainCarriageData, index) => (
                    <TableRow key={index}>
                      <TableCell className="p-5">
                        <Typography weight={"semibold"} className="mb-3">
                          Main Carriage {index + 1}
                        </Typography>
                        <Typography>
                          {`Transport Mode: ${mainCarriageData?.mode ?? "N/A"}`}
                          {mainCarriageData?.mean ? `/ Conveyance Type: ${mainCarriageData?.mean}` : ""}
                          {confirmationData?.carrier?.[0]?.carrier_sac
                            ? ` / Carrier SCAC: ${confirmationData.carrier[0].carrier_sac}`
                            : ""}
                        </Typography>
                        <Typography>
                          {`Vessel: ${mainCarriageData?.vessel_name ?? "N/A"} / Country: ${
                            mainCarriageData?.country ?? "N/A"
                          } / Lloyd's Code: ${mainCarriageData?.lloyds_code ?? "N/A"} / Voyage: ${
                            mainCarriageData?.voyage_number ?? "N/A"
                          }`}
                        </Typography>
                        <Typography>
                          {`Start Location: ${mainCarriageData?.port_of_load?.location_name ?? "N/A"} (${
                            mainCarriageData?.port_of_load?.locode ?? "N/A"
                          }), ${mainCarriageData?.port_of_load?.country ?? ""} / Estimated Departure Date:
                  ${dayjs(mainCarriageData?.etd)?.format("MMM DD YYYY") ?? "N/A"}`}
                        </Typography>
                        <Typography>
                          {`End Location: ${mainCarriageData?.port_of_discharge?.location_name ?? "N/A"} (${
                            mainCarriageData?.port_of_discharge?.locode ?? "N/A"
                          }), ${mainCarriageData?.port_of_discharge?.country ?? ""} / Estimated Arrival Date:
                  ${dayjs(mainCarriageData?.eta)?.format("MMM DD YYYY") ?? "N/A"}`}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}

                {confirmationData?.on_carriage &&
                  confirmationData?.on_carriage?.length > 0 &&
                  confirmationData.on_carriage.map((onCarriageData, index) => (
                    <TableRow>
                      <TableCell className="p-5">
                        <Typography weight={"semibold"} className="mb-3">
                          On Carriage {index + 1}
                        </Typography>

                        <Typography>
                          {`Transport Mode: ${onCarriageData?.mode ?? "N/A"}`}{" "}
                          {onCarriageData?.mean ? `/ Conveyance Type: ${onCarriageData?.mean}` : ""}
                          {confirmationData?.carrier?.[0]?.carrier_sac
                            ? ` / Carrier SCAC: ${confirmationData.carrier[0].carrier_sac}`
                            : ""}
                        </Typography>
                        <Typography>
                          {`Start Location: ${onCarriageData?.start?.location_name ?? "N/A"}, ${
                            onCarriageData?.start?.country ?? "N/A"
                          } (${onCarriageData?.start?.locode ?? "N/A"})
                            
                             / Estimated Departure Date: ${dayjs(onCarriageData?.etd)?.format("MMM DD YYYY") ?? "N/A"}`}
                        </Typography>
                        <Typography>
                          {`End Location: ${onCarriageData?.end?.location_name ?? "N/A"}, ${
                            onCarriageData?.end?.country ?? "N/A"
                          } (${onCarriageData?.end?.locode ?? "N/A"}) / Estimated Arrival Date: ${
                            dayjs(onCarriageData?.eta)?.format("MMM DD YYYY") ?? "N/A"
                          }`}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>

          <div className="mt-8">
            <Table className="border-1 border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="border-1 w-[12%]">
                    <Typography weight={"semibold"} className="p- text-left5">
                      Cargo / Packing
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[12%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Packages
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[24%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Cargo Description
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[14%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Marks and Numbers
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[18%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Cargo Weight(Excluding Tare)
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[10%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Net Weight
                    </Typography>
                  </TableHead>
                  <TableHead className="border-1 w-[10%]">
                    <Typography weight={"semibold"} className="p-5 text-left">
                      Gross Volume
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(confirmationData?.cargo?.length ?? 0) > 0 ? (
                  confirmationData?.cargo.map((cargoData, key) => (
                    <TableRow key={cargoData.description + cargoData.location}>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[12%]">
                        <Typography>({key + 1})Outer</Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[12%]">
                        <Typography></Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[24%]">
                        <Typography className="text-wrap">{cargoData.description}</Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[14%]">
                        <Typography></Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[18%]">
                        <Typography>
                          {cargoData.cargo_gross_weight} {cargoData.net_weight_unit}
                        </Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[10%]">
                        <Typography>
                          {cargoData.net_weight != null ? Number(cargoData.net_weight).toFixed(2) : "-"}{" "}
                          {cargoData.net_weight_unit || ""}
                        </Typography>
                      </TableCell>
                      <TableCell className="px-5 py-8 align-top border-1 text-left w-[10%]">
                        <Typography>
                          {cargoData.gross_volume != null ? Number(cargoData.gross_volume).toFixed(2) : " "}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <></>
                )}
              </TableBody>
            </Table>
          </div>

          <div className="my-10">
            <Table className="border-1 border-[#D3DAE7]">
              <TableHeader>
                <TableRow className="h-24">
                  <TableHead className="p-5 align-top w-[80%] border-1 border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Container</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top w-[10%] border-1 border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Net Weight</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top w-[10%] border-1 border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Net Volume</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(confirmationData?.Booking_Container?.length ?? 0) > 0
                  ? confirmationData?.Booking_Container.map((containerData, index) => {
                      const fullDropOffParty = containerData?.haulage_detail?.points?.find(
                        (p) => p.haulageParty?.partyRole === "FullDropOFF"
                      )?.haulageParty;
                      const EmptyPickUpParty = containerData?.haulage_detail?.points?.find(
                        (p) => p.haulageParty?.partyRole === "EmptyPickUp"
                      )?.haulageParty;

                      const earliestDropOffDates = containerData?.haulage_detail?.points
                        ?.flatMap(
                          (point) => point.dates?.filter((date) => date.haulageDateType === "EarliestDropOffDate") || []
                        )
                        .map((date) => date.dateValue)[0];

                      const emptyPickupDate = containerData?.haulage_detail?.points
                        ?.flatMap(
                          (point) => point.dates?.filter((date) => date.haulageDateType === "EmptyPickupDate") || []
                        )
                        .map((date) => date.dateValue)[0];

                      const closingDate = containerData?.haulage_detail?.points
                        ?.flatMap(
                          (point) => point.dates?.filter((date) => date.haulageDateType === "ClosingDate") || []
                        )
                        .map((date) => date.dateValue)[0];
                      return (
                        <TableRow className="gap-6" key={index}>
                          <TableCell className="border-1 p-5 align-left space-y-5">
                            <div>
                              <Typography>Container Number: {containerData?.container_name ?? " "}</Typography>
                              <Typography>Quantity: {containerData?.number_of_containers ?? " "}</Typography>
                              <Typography>
                                Size/Type:{" "}
                                {containerData?.container_quantitytype ? containerData.container_quantitytype : ""}{" "}
                                {containerData?.container_descp && `(${containerData?.container_descp})`}
                              </Typography>
                              <Typography>Empty/Full: Full </Typography>
                              {containerData?.service_type && (
                                <Typography>
                                  Service Arrangement:{" "}
                                  {containerData?.service_type === "FCLFCL" ? "FCL/FCL" : containerData?.service_type}
                                </Typography>
                              )}
                              {containerData?.haulage_detail?.arrangement && (
                                <Typography>
                                  Haulage Arrangement: {haulageLabel(containerData?.haulage_detail?.arrangement)}
                                </Typography>
                              )}
                            </div>
                            {containerData?.container_comments && (
                              <div>
                                <Typography weight="semibold">Container Comments</Typography>
                                <Typography>{containerData?.container_comments}</Typography>
                                {/* <Typography>N/A</Typography> */}
                              </div>
                            )}

                            <div>
                              <Typography weight="semibold">Additional Container References</Typography>
                              <Typography>
                                Container Release Number: {confirmationData?.carrier_booking_number}
                              </Typography>
                              {/* <Typography>N/A</Typography> */}
                            </div>

                            {EmptyPickUpParty?.address && (
                              <>
                                <div>
                                  <Typography>---------------</Typography>
                                </div>
                                <div>
                                  <Typography weight="semibold">Empty Container Pick-up Location</Typography>
                                  <Typography>
                                    Company Pass Through Code: {EmptyPickUpParty?.passThroughCode ?? "N/A"}
                                  </Typography>
                                  <Typography>
                                    {EmptyPickUpParty?.address && (
                                      <>
                                        <Typography>{EmptyPickUpParty.address.unstructuredAddress01}</Typography>
                                        <Typography>{EmptyPickUpParty.address.unstructuredAddress02}</Typography>
                                        <Typography>{EmptyPickUpParty.address.unstructuredAddress03}</Typography>
                                        <Typography>{EmptyPickUpParty.address.unstructuredAddress04}</Typography>
                                        <Typography>{EmptyPickUpParty.address.street01}</Typography>
                                        <Typography>{EmptyPickUpParty.address.street02}</Typography>
                                        <Typography>Postal Code: {EmptyPickUpParty.address.postalCode}</Typography>
                                      </>
                                    )}
                                  </Typography>
                                  {/* <Typography></Typography> */}
                                </div>
                              </>
                            )}
                            {emptyPickupDate && (
                              <div>
                                <Typography weight="semibold">Empty Pickup Date</Typography>
                                <Typography>
                                  {emptyPickupDate ? dayjs(emptyPickupDate)?.format("MMM DD YYYY") : ""}{" "}
                                </Typography>
                              </div>
                            )}

                            {fullDropOffParty?.address && (
                              <>
                                <div>
                                  <Typography>---------------</Typography>
                                </div>
                                <div>
                                  <Typography weight="semibold">Full Container Drop-Off Location</Typography>
                                  <Typography>
                                    Company Pass Through Code: {fullDropOffParty?.passThroughCode ?? "N/A"}
                                  </Typography>
                                  <Typography>
                                    {fullDropOffParty?.address && (
                                      <>
                                        <Typography>{fullDropOffParty.address?.unstructuredAddress01}</Typography>
                                        <Typography>{fullDropOffParty.address?.unstructuredAddress02}</Typography>
                                        <Typography>{fullDropOffParty.address?.unstructuredAddress03}</Typography>
                                        <Typography>{fullDropOffParty.address?.unstructuredAddress04}</Typography>
                                        <Typography>{fullDropOffParty.address?.street01}</Typography>
                                        <Typography>{fullDropOffParty.address?.street02}</Typography>
                                        <Typography>Postal Code: {fullDropOffParty.address?.postalCode}</Typography>
                                      </>
                                    )}
                                  </Typography>
                                  {/* <Typography></Typography>
                              <Typography>Postal Code: {fullDropOffParty?.address?.postalCode}</Typography> */}
                                </div>
                              </>
                            )}
                            {earliestDropOffDates && (
                              <div>
                                <Typography weight="semibold">Earliest Drop-Off Date</Typography>
                                <Typography>
                                  {earliestDropOffDates ? dayjs(earliestDropOffDates)?.format("MMM DD YYYY") : ""}
                                </Typography>
                              </div>
                            )}
                            {closingDate && (
                              <div>
                                <Typography weight="semibold">Closing Date</Typography>
                                <Typography>{closingDate ? dayjs(closingDate)?.format("MMM DD YYYY") : ""}</Typography>
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="border-1 p-5 align-left align-top">
                            <Typography>
                              {containerData?.weight_value} {containerData?.weight_type}
                            </Typography>
                          </TableCell>
                          <TableCell className="border-1 p-5 align-left">
                            <Typography>{containerData?.weight_volume}</Typography>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  : null}
              </TableBody>
            </Table>
          </div>

          {/* <div className="my-10">
            <Typography variant={"h3"}>History</Typography>
            <Table className="mt-5 border-1 border-[#D3DAE7]">
              <TableHeader className="bg-[#D3DAE7]">
                <TableRow>
                  <TableHead className="p-8 w-[12%]">
                    <Typography weight={"semibold"}>Status</Typography>
                  </TableHead>
                  <TableHead className="p-8 w-[12%]">
                    <Typography weight={"semibold"}>Diff.</Typography>
                  </TableHead>
                  <TableHead className="p-8 w-[46%]">
                    <Typography weight={"semibold"}>
                      Created By & Email
                    </Typography>
                  </TableHead>
                  <TableHead className="p-8 w-[12%]">
                    <Typography weight={"semibold"}>Company</Typography>
                  </TableHead>
                  <TableHead className="p-8 w-[18%]">
                    <Typography weight={"semibold"}>
                      Date & Time(GMT)
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader> */}
          {/* <TableBody>
            <TableRow>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>Confirmed</Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>Santhiya MADHAN </Typography>
                <Typography><EMAIL></Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>CMA CGM</Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>10-Feb-2025 21:33GMT</Typography>
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>(1)Outer</Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>Waste, parings and scrap of so </Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>(1)Outer</Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>Waste, parings and scrap of so </Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>(1)Outer</Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1">
                <Typography>Waste, parings and scrap of so </Typography>
              </TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
              <TableCell className="p-8 align-left border-e-1"></TableCell>
            </TableRow>
          </TableBody> */}
          {/* </Table>
          </div> */}

          <div>
            <Typography className="mb-5">
              This booking response/confirmation, or other booking related message, is expressly subject to the
              following terms and conditions:
            </Typography>
            <ol className="list-decimal ml-5 space-y-2">
              <li>
                The carrier reserves the right to substitute the named and/or performing vessel(s) with another vessel
                or vessels, at any time.{" "}
              </li>
              <li>Booking response/confirmation is subject to space and equipment availability. </li>
              <li>
                All schedules, and all dates/times for arrival, berthing, departure, cut off and transit are estimated
                only, are provided without guarantee, and are subject to change without prior notice.{" "}
              </li>
              <li>
                Bookings, booking response/confirmations, and shipment/carriage are each subject to the carrier's terms,
                conditions and exceptions applicable to bookings and to the terms, conditions and exceptions of the
                carrier's contract of carriage. These terms, conditions and exceptions are available upon request from
                the carrier or its representative, and may be accessible on the carrier's website.{" "}
              </li>
              <li>
                For bookings involving cargo destined for or carried/transshipped via the USA: Booking confirmations are
                given subject to customer providing the correct cargo description in accordance with US Customs
                requirements as described in the United States Customs Service Regulations, 19 CFR Parts 4, 113 and 178
                of October 31, 2002.{" "}
              </li>
              <li>
                This booking response/confirmation, any other transport messages, and all related services are subject
                to the INTTRA Legal Terms and Conditions which are accessible on the INTTRA website at
                www.inttra.com/legal.
              </li>
            </ol>
          </div>
          <TrackContainerPage
            open={drawerOpen}
            onOpenChange={(open) => setDrawerOpen(open)}
            inttraReferenceId={String(confirmationData?.inttra_reference)}
            carrierName={carrierName}
            carrierBookingReferenceId={String(confirmationData?.carrier_booking_number)}
            // inttraReferenceId={'2001070988'}
          />
        </div>
      )}
    </>
  );
}

export default BookingConfirmationView;
