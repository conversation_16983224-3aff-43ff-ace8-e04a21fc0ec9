import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Typography } from "@/components/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Search, FilePenLine, Trash2 } from "lucide-react";
// import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  deleteBookingRequestTemplate,
  fetchBookingTemplate,
} from "@/services/admin/booking";
import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import Loader from "@/components/Loader";
import { useNavigate } from "react-router-dom";
import { BookingRequestFormType } from "../bookingRequest/schema";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { Fragment, useEffect, useRef, useState } from "react";

const MyTemplateView = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState("");

  const loaderRef = useRef<HTMLDivElement | null>(null);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["fetchBookingTemplate", { searchText, count: 10 }],
    queryFn: ({ pageParam = 1 }) =>
      fetchBookingTemplate({ pageParam, searchText, count: 10 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages, pageCount) => {
      const currentPage = lastPage?.message?.page;
      // @ts-ignore
      const totalPages = lastPage?.message?.total_pages ?? 5;

      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });

  useEffect(() => {
    const loader = loaderRef.current;
    if (!loader) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      {
        rootMargin: "0px 0px 200px 0px",
      }
    );

    observer.observe(loader);

    return () => {
      if (loader) observer.unobserve(loader);
      observer.disconnect();
    };
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Delete template data.
  const { mutate, isPending, isError, isSuccess } = useMutation({
    mutationFn: deleteBookingRequestTemplate,
    onSuccess: (data) => {
      if (data?.message?.status === "error") {
        toast.error(data?.message?.message);
      } else {
        toast.success("Template deleted successfully!");
        refetch();
      }
    },
    onError: () => {
      toast.error("Failed to delete Template. Please try again.");
    },
  });

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex gap-4 items-center ml-auto">
          <div className="relative">
            <Input
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="pr-10"
              placeholder="Search Template"
            />
            <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="pt-6 overflow-y-auto">
        <ScrollArea className="whitespace-nowrap">
          <Table className="table-auto border-1 border-[#D3DAE7] w-full">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                {/* <TableHead className="pl-3">
                                    <Checkbox className="bg-white" />
                                </TableHead> */}
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    TEMPLATE NAME
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    CARRIER
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    POL
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    POD
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    LAST MODIFIED
                  </Typography>
                </TableHead>
                <TableHead className="p-3">
                  <Typography variant={"h6"} weight={"medium"}>
                    ACTION
                  </Typography>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.pages?.map((page, i) => (
                <Fragment key={i}>
                  {page?.message?.data?.map((item, ind) => {
                    let bookingData: BookingRequestFormType | null;
                    if (typeof item?.booking_data === "string") {
                      bookingData = JSON.parse(item?.booking_data);
                    } else {
                      bookingData = item?.booking_data;
                    }
                    return (
                      <TableRow key={item.name}>
                        {/* <TableCell className="pl-3">
                                            <Checkbox className="bg-white scale-100" />
                                        </TableCell> */}
                        <TableCell className="p-3">
                          <Typography>{item.template_name || "N/A"}</Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {bookingData?.carrier?.partyname1
                              ? bookingData?.carrier?.partyname1?.slice(0, 8)
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {bookingData?.placeCarrierReceipt?.location
                              ? bookingData?.placeCarrierReceipt?.location?.split(
                                  ","
                                )[0]
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {bookingData?.placeCarrierDelivary?.location
                              ? bookingData?.placeCarrierDelivary?.location?.split(
                                  ","
                                )[0]
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {item.modified && dayjs(item.modified)?.isValid()
                              ? dayjs(item.modified)?.format("MMM-DD-YYYY")
                              : "N/A"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <div className="flex gap-1 relative">
                            <Button
                              onClick={() =>
                                navigate(
                                  `/dashboard/booking/booking-request?template_id=${item?.name}`
                                )
                              }
                              className="border-1  rounded-lg flex gap-2 p-5"
                            >
                              <FilePenLine />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger>
                                <Button
                                  disabled={isPending}
                                  variant={"outline"}
                                  className="border-1 border-[#D3DAE7] rounded-lg flex gap-2 p-5"
                                >
                                  <Trash2 className="text-[#929FB8]" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Are you absolutely sure?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will
                                    permanently delete your template.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>
                                    Not sure
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    className="bg-red-600 hover:bg-red-600/90"
                                    onClick={() => mutate(item?.name)}
                                  >
                                    Sure, Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </Fragment>
              ))}
            </TableBody>
          </Table>
          <div
            ref={loaderRef}
            className="w-full h-[50dvh] flex justify-center items-center"
          >
            {isFetchingNextPage ? (
              <div className="w-full flex justify-center">
                <Loader />
              </div>
            ) : (
              ""
            )}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </div>
  );
};

export default MyTemplateView;
