"use client";
import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  CalendarIcon,
  Search,
  ChevronRight,
  ChevronLeft,
  Plus,
  RefreshCcw,
  Eye,
  Pencil,
  Trash,
} from "lucide-react";
import { format, parse } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Typography } from "@/components/typography";
import UpdateBillSheet from "./UpdateBillSheet";
import IconDownload from "@/assets/img/IconDownload.svg";
import IconEdit from "@/assets/img/IconEdit.svg";
import IconView from "@/assets/img/IconView.svg";
import { useNavigate, Outlet } from "react-router-dom";
import {
  listBills,
  getVendors,
  deleteBillById,
} from "@/services/admin/billGenerate";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import dayjs from "dayjs";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

// Dummy Data

const statusList = ["Paid", "Pending", "Overdue"];

const uniqueStatuses = statusList.map((status) => ({
  label: status,
  value: status,
}));

const getStatusBadgeStyle = (status: string) => {
  switch (status.toLowerCase()) {
    case "paid":
      return { backgroundColor: "#D7FDE5", color: "#339D59" };
    case "pending":
      return { backgroundColor: "#FFF6D6", color: "#D39E10" };
    case "overdue":
      return { backgroundColor: "#FFE1E1", color: "#D33232" };
    default:
      return { backgroundColor: "#E5E8EF", color: "#4B5563" };
  }
};

export default function BillsListPage() {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();

  const statusFilter = searchParams.get("status") || "";
  const vendorFilter = searchParams.get("vendor") || "";
  const search = searchParams.get("search") || "";
  const dateStr = searchParams.get("date") || "";
  const date = dateStr ? parse(dateStr, "yyyy-MM-dd", new Date()) : null;
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );
  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const navigate = useNavigate();

  const rowsPerPage = 10;
  const { data: billDetails, refetch: refetchBillListData } = useQuery({
    queryKey: [
      "fetchListBillsBillDetails",
      vendorFilter,
      statusFilter,
      date,
      search,
    ],
    queryFn: () =>
      listBills({
        ...(vendorFilter && { vendor: vendorFilter }),
        ...(statusFilter && { status: statusFilter }),
        ...(date && { bill_date: format(date, "yyyy-MM-dd") }),
        ...(search && { search: search }),
      }),
  });

  const handleOpenDrawer = (id: number) => {
    setSelectedItemId(id);
    setOpenDrawer(true);
  };

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setSelectedItemId(null);
  };

  const totalCount = billDetails?.data?.length || 0;
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const paginatedData =
    billDetails?.data?.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    ) || [];
  const { data: vendorsData } = useQuery({
    queryKey: ["getVendors"],
    queryFn: () => getVendors(),
  });

  // useEffect(() => {
  //   const newParams = new URLSearchParams(searchParams);
  // newParams.set("page", "1");
  // setSearchParams(newParams);
  // }, [search, vendorFilter, statusFilter, date]);

  const useDeleteBill = () => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: (billId: string) => deleteBillById(billId),
      onSuccess: (response) => {
        if (response?.message?.status_code === 200) {
          toast.success(
            response.message?.message || "Bill deleted successfully"
          );
          refetchBillListData();
          setOpenDialog(false);
          setDeleteId(null);
          queryClient.invalidateQueries({ queryKey: ["bills"] });
        } else {
          setOpenDialog(false);
          setDeleteId(null);
          toast.error(response.message?.message || "Failed to delete bill");
        }
      },
      onError: (err: any) => {
        toast.error(err?.message || "Failed to delete bill");
      },
    });
  };
  const { mutate: deleteBill, isPending: isDeleting } = useDeleteBill();

  // if (!billDetails) {
  //   return <div className="p-6">Loading bill list...</div>;
  // }
  return (
    <div className="">
      {/* Filters */}
      <div className="flex items-center justify-between flex-wrap gap-4 mb-3">
        {/* Left: Filters in one row */}
        <div className="flex flex-wrap items-center gap-3 flex-1">
          <p className="text-sm sm:text-base mt-5">
            {totalCount}{" "}
            <span className="text-gray-500">
              Result{totalCount > 1 ? "s" : ""} Found
            </span>
          </p>
        </div>

        {/* Right: Create Bill Button */}
        <div className="flex gap-4 items-center ml-auto">
          <div className="relative">
            <Input
              className="pr-10"
              placeholder="Search here"
              value={search}
              onChange={(e) => {
                const newSearch = e.target.value;
                const newParams = new URLSearchParams(searchParams);
                newSearch
                  ? newParams.set("search", newSearch)
                  : newParams.delete("search");
                newParams.set("page", "1"); // reset to page 1
                setSearchParams(newParams);
              }}
            />
            <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => {
              const newStatus = e.target.value;
              const newParams = new URLSearchParams(searchParams);
              newStatus
                ? newParams.set("status", newStatus)
                : newParams.delete("status");
              newParams.set("page", "1"); // reset to page 1
              setSearchParams(newParams);
            }}
            className="h-10 min-w-[150px] border bg-gray-100 text-sm text-gray-700 px-3 rounded-md"
          >
            <option value="">All Statuses</option>
            {uniqueStatuses.map((status, i) => (
              <option key={i} value={status?.value}>
                {status?.label}
              </option>
            ))}
          </select>

          {/* Vendor Dropdown */}
          <select
            value={vendorFilter}
            onChange={(e) => {
              const newVendor = e.target.value;
              const newParams = new URLSearchParams(searchParams);
              newVendor
                ? newParams.set("vendor", newVendor)
                : newParams.delete("vendor");
              newParams.set("page", "1"); // reset to page 1
              setSearchParams(newParams);
            }}
            className="h-10 min-w-[150px] border bg-gray-100 text-sm text-gray-700 px-3 rounded-md"
          >
            <option value="">All Vendors</option>
            {vendorsData?.message?.data?.vendors?.map((vendor, i) => (
              <option key={i} value={vendor?.name}>
                {vendor?.vendor_name}
              </option>
            ))}
          </select>

          {/* Date Picker */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="h-10 bg-gray-100 text-sm text-gray-700 px-3 rounded-md"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? (
                  format(date, "dd-MM-yyyy")
                ) : (
                  <span className="text-gray-400">DD-MM-YYYY</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date || undefined}
                onSelect={(day) => {
                  const newParams = new URLSearchParams(searchParams);
                  if (day) {
                    newParams.set("date", format(day, "yyyy-MM-dd"));
                  } else {
                    newParams.delete("date");
                  }
                  newParams.set("page", "1");
                  setSearchParams(newParams);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          {/* Clear Filters Button */}
          {(date || search || vendorFilter || statusFilter) && (
            <Button
              variant="outline"
              className="h-10 text-sm text-gray-700"
              onClick={() => {
                setSearchParams({});
              }}
            >
              <RefreshCcw className="mr-2 h-4 w-4" /> Clear
            </Button>
          )}
          <Button
            onClick={() => navigate("/dashboard/bill-form")}
            className="bg-foreground h-10 px-4 text-white text-sm"
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Bill
          </Button>
        </div>
      </div>
      {/* Table */}
      <div className="pt-6 overflow-y-auto">
        <ScrollArea className="whitespace-nowrap">
          <Table className="table-auto w-full border-spacing-y-2">
            <TableHeader>
              <TableRow className="bg-[#D3DAE7] h-[55px]">
                {[
                  "BILL #",
                  "VENDOR BILL #",
                  "BOOKING #",
                  "JOB #",
                  "BILL DATE",
                  "VENDOR",
                  "ORIGIN",
                 <div className="leading-tight">TOTAL <br />AMOUNT $</div>,
                  // "QUICKBOOKS VENDOR NAME",
                  "STATUS",
                  "CREATED DATE",
                  "ACTION",
                ].map((head, idx) => (
                  <TableHead
                    key={idx}
                    className="p-3 text-left font-bold text-[#191C36] pl-6"
                  >
                    {head}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData && paginatedData.length > 0 ? (
                paginatedData.map((item) => (
                  <TableRow
                    key={item.id}
                    className="border border-[#D3DAE7] hover:bg-gray-300 transition-all duration-300 transform shadow-md"
                    style={{ border: "2px solid #D3DAE7" }}
                  >
                    {[
                      item.document_number,
                      item?.vendor_bill_no ?? "--",
                      item.booking_id,
                      item.job_id,
                      item.bill_date
                        ? dayjs(item.bill_date).format("MMM-DD-YYYY")
                        : "--",
                      item.vendor,
                      item?.origin ?? "--",
                      // item.total_amount,
                      item.total_amount !== undefined &&
                      item.total_amount !== null
                        ? new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD",
                            minimumFractionDigits: 2,
                          }).format(Number(item.total_amount))
                        : "N/A",
                      // item.quickbooks_vendor,
                    ].map((val, i) => (
                      <TableCell
                        key={i}
                        className={`p-3 text-bold border-t-2 border-b-2 pl-6 ${
                          i === 5
                            ? "break-words whitespace-normal max-w-[180px]"
                            : ""
                        }`}
                      >
                        {val ?? "--"}
                      </TableCell>
                    ))}
                    <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6">
                      <span
                        className="px-3 py-1 text-sm font-medium rounded-full capitalize whitespace-nowrap"
                        style={getStatusBadgeStyle(item.status)}
                      >
                        {item.status}
                      </span>
                    </TableCell>
                    <TableCell className="p-3 text-bold border-t-2 border-b-2 pl-6 max-w-[150px]">
                      {item.creation
                        ? dayjs(item.creation).format("MMM-DD-YYYY")
                        : "--"}
                    </TableCell>
                    <TableCell className="p-3 text-bold border-t-2 border-b-2 border-r-2 pl-6">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="bg-primary text-white rounded-sm px-3 py-2"
                          onClick={() =>
                            navigate(
                              `/dashboard/bill-view/${item.name}${
                                item?.job_id ? `?jobId=${item.job_id}` : ""
                              }`
                            )
                          }
                        >
                          <Eye />
                        </Button>
                        <Button
                          size="icon"
                          variant={"outline"}
                          className="border-2 border-grey bg-grey hover:bg-orange-600 text-black rounded-sm px-3 py-2"
                          onClick={() =>
                            navigate(`/dashboard/update-bill/${item.name}`)
                          }
                          // disabled={item.status === "Paid" ? true : false}
                        >
                          <Pencil />
                        </Button>

                        <Button
                          variant={"outline"}
                          className={`border-2 border-grey bg-grey hover:bg-orange-600 text-black rounded-sm px-3 py-2 ${
                            isDeleting ? "cursor-not-allowed" : ""
                          }`}
                          disabled={isDeleting}
                          onClick={() => {
                            setDeleteId(item.qb_bill_id);
                            setOpenDialog(true);
                          }}
                        >
                          <Trash />
                        </Button>

                        {/* <Button
                  size="icon"
                  className="bg-primary text-white rounded-sm px-3 py-2"
                >
                  <img src={IconDownload} alt="icon" />
                </Button> */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-6 text-gray-500 font-medium"
                  >
                    No Data Found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        <AlertDialog open={openDialog} onOpenChange={setOpenDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Bill</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this Bill?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className={isDeleting ? "cursor-not-allowed" : ""}
                disabled={isDeleting}
                onClick={(e) => {
                  e.preventDefault();
                  if (deleteId) {
                    deleteBill(deleteId);
                  }
                }}
              >
                Yes, Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* Drawer (outside of the table) */}
      {openDrawer && selectedItemId !== null && (
        <UpdateBillSheet open={openDrawer} onClose={handleCloseDrawer} />
      )}
    </div>
  );
}
