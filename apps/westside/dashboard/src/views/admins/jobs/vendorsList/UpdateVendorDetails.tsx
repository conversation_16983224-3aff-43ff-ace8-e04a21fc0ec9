
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { fetchVendorDetailsbyId } from "@/services/admin/vendordetailsById";
import { editVendor } from "@/services/admin/vendorList";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ComboBox } from "@/components/ui/comboBox";
import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import { State } from "country-state-city";
import { getData as getCountryData,
  getNames as getCountryNames,
  getCode as getCountryCode,
 } from "country-list";

interface FormState {
  first_name: string;
  last_name: string;
  parent_company: string;
  contact: string;
  phone: string;
  email_id: string;
  zip: string;
  city: string;
  state: string;
  country: string;
  vendor_address: string;
  vendor_code: string;
}

interface ErrorsState {
  [key: string]: string;
}

export default function UpdateVendorDetails({
  open,
  onClose,
  vendorId,
}: {
  open: boolean;
  onClose: () => void;
  vendorId: string;
}) {
  const [form, setForm] = useState<FormState>({
    first_name: "",
    last_name: "",
    parent_company: "",
    contact: "",
    phone: "",
    email_id: "",
    zip: "",
    city: "",
    state: "",
    country: "",
    vendor_address: "",
    vendor_code: "",
  });

  const [errors, setErrors] = useState<ErrorsState>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [countries, setCountries] = useState<{ label: string; value: string }[]>([]);
  const [states, setStates] = useState<{ label: string; value: string }[]>([]);

  useEffect(() => {
    const countriesList = getCountryData().map((c) => ({
      label: c.name,
      value: c.code,
    }));
    setCountries(countriesList.sort((a, b) => a.label.localeCompare(b.label)));
  }, []);

  useEffect(() => {
    if (open && vendorId) {
      setLoading(true);
      fetchVendorDetailsbyId(vendorId)
        .then((response: any) => {
          const vendorData = response?.message?.data;
          if (vendorData) {
            setForm({
              first_name: vendorData.first_name || "",
              last_name: vendorData.last_name || "",
              parent_company: vendorData.parent_company || "",
              contact: vendorData.contact || "",
              phone: vendorData.phone || "",
              email_id: vendorData.email_id || "",
              zip: vendorData.zip || "",
              city: vendorData.city || "",
              state: vendorData.state || "",
              country: vendorData.country?.code?.toUpperCase() || "",
              vendor_address: vendorData.vendor_address || "",
              vendor_code: vendorData.vendor_code || "",
            });
          } else {
            toast.error("No vendor data found.");
          }
        })
        .catch(() => {
          toast.error("Failed to fetch vendor details");
        })
        .finally(() => setLoading(false));
    }
  }, [open, vendorId]);

  useEffect(() => {
    if (form.country) {
      const selectedCountryCode = form.country.toUpperCase();

      const fetchedStates = selectedCountryCode
        ? State.getStatesOfCountry(selectedCountryCode)
        : [];
      const statesList = fetchedStates.map((s) => ({
        label: s.name,
        value: s.isoCode,
      }));
      setStates(statesList.sort((a, b) => a.label.localeCompare(b.label)));
    } else {
      setStates([]);
    }
  }, [form.country]);

  const handleChange = (field: keyof FormState, value: string) => {
    setForm((prev) => ({
      ...prev,
      [field]: value,
      ...(field === "country" ? { state: "" } : {}),
    }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: ErrorsState = {};
    if (!form.first_name.trim()) newErrors.first_name = "First name is required";
    if (!form.last_name.trim()) newErrors.last_name = "Last name is required";
    if (!form.parent_company.trim()) newErrors.parent_company = "Parent company is required";
    if (!form.contact.trim()) newErrors.contact = "Contact number is required";
    if (!form.phone.trim()) newErrors.phone = "Phone number is required";
    if (!form.email_id.trim()) newErrors.email_id = "Email ID is required";
    if (!form.zip.trim()) newErrors.zip = "Zip code is required";
    if (!form.vendor_code.trim()) newErrors.vendor_code = "Vendor Code is required";
    // if (!form.state.trim()) newErrors.state = "State is required";
    // if (!form.country.trim()) newErrors.country = "Country is required";
    if (!form.vendor_address.trim()) newErrors.vendor_address = "Vendor Address is required";

    const phoneRegex = /^[\d\s()+-]*$/;
    if (form.phone && !phoneRegex.test(form.phone)) newErrors.phone = "Invalid phone number format.";
    if (form.phone && /^0+$/.test(form.phone)) {
      newErrors.phone = "Phone number cannot be all zeros.";
    }
    if (form.contact && /[a-zA-Z]/.test(form.contact)) newErrors.contact = "Contact number cannot contain alphabets.";
    if (form.email_id && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email_id)) newErrors.email_id = "Invalid email format";

    if (form.first_name.trim() && !/[a-zA-Z0-9]/.test(form.first_name)) {
      newErrors.first_name = "First Name must contain letters or numbers";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      updateVendor(vendorId, form);
    }
  };

  const updateVendor = async (id: string, updatedData: FormState) => {
    try {
      const response = await editVendor({ formData: updatedData, vendor_id: id });
      if (response?.message?.status !== 200) {
        toast.error(response?.message?.message || "Failed to update vendor");
      } else {
        toast.success("Vendor updated successfully");
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message?.error || "Failed to update vendor");
    }
  };

  const hasError = (field: keyof FormState) => !!errors[field];

  useEffect(() => {
      if (form.country && states.length > 0) {
        const stateExists = states.find((s) => s.value === form.state);
        if (!stateExists) {
          setForm((form) => ({ ...form, state: "" }));
        }
      }
    }, [states, form.state, form.country]);
  return (
    <Sheet open={open} onOpenChange={(val) => !val && onClose()}>
      <SheetContent
        side="right1"
        className="p-6 sm:p-7 w-full max-w-full flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <div className="flex flex-col mb-4 relative">
          <div className="flex justify-between items-center">
            <SheetTitle>Update Vendor</SheetTitle>
          </div>
          <div className="absolute left-0 right-0 h-[1px] bg-gray-300 top-full mt-6 -mx-6" />
        </div>

        <form
          onSubmit={handleSubmit}
          className="flex-1 overflow-y-auto pt-8 p-4"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Input fields */}
            {[
              ["first_name", "First Name"],
              ["last_name", "Last Name"],
              ["vendor_code", "Vendor Code"],
              ["parent_company", "Parent Company"],
              ["contact", "Contact No"],
              ["phone", "Phone No"],
              ["email_id", "Email ID"],
              ["zip", "Zip Code"],
            ].map(([field, label]) => (
              <div key={field}>
                <label className="text-sm text-gray-600">
                  {label}
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  placeholder={label}
                  value={form[field as keyof FormState]}
                  onChange={(e) =>
                    handleChange(field as keyof FormState, e.target.value)
                  }
                  className={
                    hasError(field as keyof FormState) ? "border-red-500" : ""
                  }
                />
                {errors[field as keyof FormState] && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors[field as keyof FormState]}
                  </p>
                )}
              </div>
            ))}
            {/* City field now appears before Country/State */}
            <div>
              <label className="text-sm text-gray-600">City</label>
              <Input
                placeholder="City"
                value={form.city}
                onChange={(e) => handleChange("city", e.target.value)}
                className={hasError("city") ? "border-red-500" : ""}
              />
              {errors.city && (
                <p className="text-red-500 text-sm mt-1">{errors.city}</p>
              )}
            </div>
            <div>
              <ComboBox
                label="Country"
                value={
                  countries.find(
                    (s) => s.value.toLowerCase() === form.country?.toLowerCase()
                  )?.label || form.country
                }
                onChange={(selectedName) => {
                  const selected = countries.find(
                    (s) => s.label === selectedName
                  );
                  if (selected) {
                    handleChange("country", selected.value);
                  }
                }}
                options={countries.map((c) => c.label)}
                error={errors.country}
                placeholder="Select Country"
                required={false}
              />
            </div>
            <div>
              <ComboBox
                label="State"
                value={states.find((s) => s.value === form.state)?.label || ""}
                onChange={(selectedLabel) => {
                  const selected = states.find(
                    (s) => s.label === selectedLabel
                  );
                  if (selected) handleChange("state", selected.value);
                }}
                options={states.map((s) => s.label)}
                placeholder="Select State"
                error={errors.state}
                required={false}
              />
            </div>
            <div className="col-span-2">
              <label className="text-sm text-gray-600">
                Vendor Address<span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="Vendor Address"
                value={form.vendor_address}
                onChange={(e) => handleChange("vendor_address", e.target.value)}
                className={hasError("vendor_address") ? "border-red-500" : ""}
              />
              {errors.vendor_address && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.vendor_address}
                </p>
              )}
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <Button type="submit" className="bg-orange-500 hover:bg-gray-500 text-white">
              {loading ? "Updating.." : "Update Vendor"}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  );
}
