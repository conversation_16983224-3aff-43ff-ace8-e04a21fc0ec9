import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTitle } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner"; // or your preferred toast library

import {
  getNames as getCountryNames,
  getCode as getCountryCode,
  getData as getCountryData,
} from "country-list";
import { State } from "country-state-city";
import { createVendor } from "@/services/admin/vendorList";
import { ComboBox } from "@/components/ui/comboBox";

interface FormState {
  first_name: string;
  last_name: string;
  parent_company: string;
  contact: string;
  phone: string;
  email_id: string;
  zip: string;
  city: string;
  state: string;
  country: string;
  vendor_address: string;
}

interface ErrorsState {
  [key: string]: string;
}

export default function CreateVendorSheet({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const [form, setForm] = useState<FormState>({
    first_name: "",
    last_name: "",
    parent_company: "",
    contact: "",
    phone: "",
    email_id: "",
    zip: "",
    city: "",
    state: "",
    country: "",
    vendor_address: "",
  });

  const [errors, setErrors] = useState<ErrorsState>({});

  // Fetch countries list using React Query
  const { data: countries = [] } = useQuery({
    queryKey: ["countries"],
    queryFn: () => {
      return getCountryData().map((country) => ({
        label: country.name,
        value: country.code,
      }));
    },
    staleTime: Infinity,
  });
  const [states, setStates] = useState<{ label: string; value: string }[]>([]);
  
  useEffect(() => {
    if (form.country) {
      const selectedCountryCode = form.country.toUpperCase();

      const fetchedStates = selectedCountryCode
        ? State.getStatesOfCountry(selectedCountryCode)
        : [];
      const statesList = fetchedStates.map((s) => ({
        label: s.name,
        value: s.isoCode,
      }));
      setStates(statesList.sort((a, b) => a.label.localeCompare(b.label)));
    } else {
      setStates([]);
    }
  }, [form.country]);
  // Mutation for creating vendor
  const createVendorMutation = useMutation({
    mutationFn: createVendor,
    onSuccess: () => {
      toast.success("Vendor created successfully");
      onClose();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message?.error || "Failed to create vendor"
      );
      console.error("Error creating vendor:", error);
    },
  });

  const handleChange = (field: keyof FormState, value: string) => {
    setForm((prev) => ({
      ...prev,
      [field]: value,
      ...(field === "country" ? { state: "" } : {}), // reset state when country changes
    }));

    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: ErrorsState = {};
    if (!form.first_name.trim())
      newErrors.first_name = "First name is required";
    if (!form.last_name.trim()) newErrors.last_name = "Last name is required";
    if (!form.parent_company.trim())
      newErrors.parent_company = "Parent company is required";
    if (!form.contact.trim()) newErrors.contact = "Contact number is required";
    if (!form.phone.trim()) newErrors.phone = "Phone number is required";
    if (!form.email_id.trim()) newErrors.email_id = "Email ID is required";
    if (!form.zip.trim()) newErrors.zip = "Zip code is required";
    // if (!form.city.trim()) newErrors.city = "City is required";
    // if (!form.state.trim()) newErrors.state = "State is required";
    // if (!form.country.trim()) newErrors.country = "Country is required";
    if (!form.vendor_address.trim())
      newErrors.vendor_address = "Vendor Address is required";

    // if (form.contact && !/^\d{10}$/.test(form.contact)) {
    //   newErrors.contact = "Contact number must be 10 digits";
    // }
    // if (form.phone && !/^\d{10}$/.test(form.phone)) {
    //   newErrors.phone = "Phone number must be 10 digits";
    // }
    const phoneRegex = /^[\d\s()+-]*$/;

    if (form.phone && !phoneRegex.test(form.phone)) {
      newErrors.phone = "Invalid phone number format.";
    }
    if (form.phone && /^0+$/.test(form.phone)) {
      newErrors.phone = "Phone number cannot be all zeros.";
    }
    if (form.contact && /[a-zA-Z]/.test(form.contact)) {
      newErrors.contact = "Contact number cannot contain alphabets.";
    }
    if (form.email_id && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email_id)) {
      newErrors.email_id = "Invalid email format";
    }
    // if (form.zip && !/^\d+$/.test(form.zip)) {
    //   newErrors.zip = "Zip must be numeric";
    // }
    if (form.first_name.trim()) {
      if (!/[a-zA-Z0-9]/.test(form.first_name)) {
        newErrors.first_name = "First Name must contain letters or numbers";
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      createVendorMutation.mutate(form);
    }
  };

  const hasError = (field: keyof FormState) => !!errors[field];

  return (
    <Sheet open={open} onOpenChange={(val) => !val && onClose()}>
      <SheetContent
        side="right1"
        className="p-6 sm:p-7 w-full max-w-full flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <div className="flex flex-col mb-4 relative">
          <div className="flex justify-between items-center">
            <SheetTitle>Create Vendor</SheetTitle>
          </div>
          <div className="absolute left-0 right-0 h-[1px] bg-gray-300 top-full mt-6 -mx-6" />
        </div>

        <form
          onSubmit={handleSubmit}
          className="flex-1 overflow-y-auto pt-8 p-4"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* First Name */}
            <div>
              <label className="text-sm text-gray-600">
                First Name<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="First Name"
                value={form.first_name}
                onChange={(e) => handleChange("first_name", e.target.value)}
                className={hasError("first_name") ? "border-red-500" : ""}
              />
              {errors.first_name && (
                <p className="text-red-500 text-sm mt-1">{errors.first_name}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="text-sm text-gray-600">
                Last Name<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Last Name"
                value={form.last_name}
                onChange={(e) => handleChange("last_name", e.target.value)}
                className={hasError("last_name") ? "border-red-500" : ""}
              />
              {errors.last_name && (
                <p className="text-red-500 text-sm mt-1">{errors.last_name}</p>
              )}
            </div>

            {/* Parent Company */}
            <div>
              <label className="text-sm text-gray-600">
                Parent Company<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Parent Company"
                value={form.parent_company}
                onChange={(e) => handleChange("parent_company", e.target.value)}
                className={hasError("parent_company") ? "border-red-500" : ""}
              />
              {errors.parent_company && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.parent_company}
                </p>
              )}
            </div>

            {/* Contact No */}
            <div>
              <label className="text-sm text-gray-600">
                Contact No<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Contact No"
                value={form.contact}
                onChange={(e) => handleChange("contact", e.target.value)}
                className={hasError("contact") ? "border-red-500" : ""}
              />
              {errors.contact && (
                <p className="text-red-500 text-sm mt-1">{errors.contact}</p>
              )}
            </div>

            {/* Phone No */}
            <div>
              <label className="text-sm text-gray-600">
                Phone No<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Phone No"
                value={form.phone}
                onChange={(e) => handleChange("phone", e.target.value)}
                className={hasError("phone") ? "border-red-500" : ""}
                type="text"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
              )}
            </div>

            {/* Email ID */}
            <div>
              <label className="text-sm text-gray-600">
                Email ID<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Email ID"
                value={form.email_id}
                onChange={(e) => handleChange("email_id", e.target.value)}
                className={hasError("email_id") ? "border-red-500" : ""}
              />
              {errors.email_id && (
                <p className="text-red-500 text-sm mt-1">{errors.email_id}</p>
              )}
            </div>

            {/* Zip */}
            <div>
              <label className="text-sm text-gray-600">
                Zip Code<span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Zip Code"
                value={form.zip}
                onChange={(e) => handleChange("zip", e.target.value)}
                className={hasError("zip") ? "border-red-500" : ""}
                type="text"
              />
              {errors.zip && (
                <p className="text-red-500 text-sm mt-1">{errors.zip}</p>
              )}
            </div>

            {/* City */}
            <div>
              <label className="text-sm text-gray-600">City</label>
              <Input
                placeholder="City"
                value={form.city}
                onChange={(e) => handleChange("city", e.target.value)}
                className={hasError("city") ? "border-red-500" : ""}
              />
              {errors.city && (
                <p className="text-red-500 text-sm mt-1">{errors.city}</p>
              )}
            </div>
            {/* Country */}
            <div>
              <ComboBox
                label="Country"
                value={
                  countries.find(
                    (s) => s.value.toLowerCase() === form.country?.toLowerCase()
                  )?.label || form.country
                }
                onChange={(selectedName) => {
                  const selected = countries.find(
                    (s) => s.label === selectedName
                  );
                  if (selected) {
                    handleChange("country", selected.value);
                  }
                }}
                options={countries.map((c) => c.label)}
                error={errors.country}
                placeholder="Select Country"
                required={false}
              />
            </div>

            {/* State */}
            <div>
              {/* <ComboBox
                label="State"
                value={form.state}
                onChange={(value: any) => handleChange("state", value)}
                options={states.map((s) => s.name)}
                placeholder="Select State"
                error={errors.state}
              /> */}
              <ComboBox
                label="State"
                value={states.find((s) => s.value === form.state)?.label || ""}
                onChange={(selectedLabel) => {
                  const selected = states.find(
                    (s) => s.label === selectedLabel
                  );
                  if (selected) handleChange("state", selected.value);
                }}
                options={states.map((s) => s.label)}
                placeholder="Select State"
                error={errors.state}
                required={false}
              />
            </div>

            {/* Vendor Address */}
            <div className="col-span-full">
              <label className="text-sm text-gray-600">
                Vendor Address<span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="Vendor Address"
                value={form.vendor_address}
                onChange={(e) => handleChange("vendor_address", e.target.value)}
                className={hasError("vendor_address") ? "border-red-500" : ""}
              />
              {errors.vendor_address && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.vendor_address}
                </p>
              )}
            </div>
          </div>

          {/* Submit button */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <div className="flex justify-end">
              <Button
                type="submit"
                className="bg-orange-500 text-white px-6 hover:bg-gray-500"
                disabled={createVendorMutation.isPending}
              >
                {createVendorMutation.isPending ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  );
}
