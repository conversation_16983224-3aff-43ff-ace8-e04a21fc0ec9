import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command";
import { Check, ChevronLast, Minus, PlusCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import * as AlertDialog from "@radix-ui/react-alert-dialog";

interface ContainerDetailsProps {
  containers: any[];
  containerTypes: any[];
  containerErrors: any[];
  handleContainerChange: (
    idx: number,
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => void;
  handleCargoChange: (
    idx: number,
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => void;
  setContainers: React.Dispatch<React.SetStateAction<any[]>>;
  addContainer: () => void;
  removeContainer: (idx: number) => void;
  isEditMode?: boolean;
  packageTypes?: any[];
  setPackageSearch?: (value: string) => void;
  packageSearch?: string;
  setHasTouchedContainers?: (value: boolean) => void;
}

export default function ContainerDetails({
  containers,
  containerTypes,
  containerErrors,
  handleContainerChange,
  handleCargoChange,
  setContainers,
  addContainer,
  removeContainer,
  isEditMode,
  packageTypes,
  setPackageSearch,
  packageSearch,
  setHasTouchedContainers,
}: ContainerDetailsProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [open, setOpen] = useState(false);
  const [packageOpenIndex, setPackageOpenIndex] = useState<number | null>(null);
  return (
    <div className="p-4 border rounded-lg">
      <h2 className="font-semibold mb-2">Container Details</h2>

      {/* Labels Row */}
      <div className="flex flex-wrap lg:flex-nowrap gap-3 mb-4 items-start text-sm font-medium text-gray-800">
        <span className="flex items-center w-[150px]">
          Container Number<span className="text-red-500">*</span>
        </span>
        <span className="flex items-center w-[200px]">
          Container Type<span className="text-red-500">*</span>
        </span>
        <span className="flex items-center w-[220px]">
          Container Description<span className="text-red-500">*</span>
        </span>
        <span className="flex items-center w-[130px]">Shipper Seal</span>
        {/* <span>Carrier Seal</span> */}
        {/* <span>
          Container Weight <span className="text-red-500">*</span>
        </span> */}
        <span className="flex items-center w-[180px]">
          Cargo Gross Weight <span className="text-red-500">*</span>
        </span>
        <span className="flex items-center w-[170px]">
          Gross Volume
        </span>
        <span className="flex  w-[120px]">Package Count</span>
        <span className="flex items-center  w-[160px]">
          Package Type <span className="text-red-500">*</span>
        </span>
        {containers.length > 1 && (
          <span className="flex items-center w-[60px]"></span>
        )}
      </div>

      {containers.map((container, idx) => (
        <div
          key={idx}
          className="flex flex-wrap lg:flex-nowrap gap-3 mb-4 items-start"
        >
          {/* Container Number */}
          <div className="flex flex-col w-[150px]">
            <Input
              name="equipment_name"
              placeholder="Container Number"
              value={container.equipment_name}
              onChange={(e) => handleContainerChange(idx, e)}
              className={cn(
                "h-11",
                containerErrors[idx]?.equipment_name && "border-red-500"
              )}
            />
            {containerErrors[idx]?.equipment_name && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].equipment_name}
              </p>
            )}
          </div>

          {/* Container Type */}
          <div className="flex flex-col w-[200px]">
            <Popover
              open={openIndex === idx}
              onOpenChange={(isOpen) => setOpenIndex(isOpen ? idx : null)}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-between h-11 truncate",
                    containerErrors[idx]?.code_value && "border-red-500"
                  )}
                >
                  <span
                    className={cn(
                      "truncate",
                      containerErrors[idx]?.code_value && "text-gray-400"
                    )}
                  >
                    {container.code_value
                      ? containerTypes?.find(
                          (c: any) =>
                            String(c.typecode) === container.code_value
                        )?.shortdescription
                      : "Select container type..."}
                  </span>
                  <ChevronLast className="opacity-50 shrink-0 ml-2" />
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput
                    placeholder="Search Container Type..."
                    className="h-9"
                  />
                  <CommandList>
                    <CommandEmpty>No Container Type Found.</CommandEmpty>
                    <CommandGroup>
                      {containerTypes?.map((c: any) => (
                        <CommandItem
                          key={c.typecode}
                          value={String(c.typecode)}
                          onSelect={() => {
                            setHasTouchedContainers &&
                              setHasTouchedContainers(true);
                            setContainers((prev) => {
                              const updated = [...prev];
                              updated[idx].code_value = String(c.typecode);
                              if (
                                !updated[idx].description ||
                                updated[idx].description.trim() === ""
                              ) {
                                updated[idx].description = String(
                                  c.description
                                );
                                handleContainerChange(idx, c.description);
                              }
                              updated[idx].equipment_type = String(
                                c.shortdescription
                              );
                              return updated;
                            });
                            setOpenIndex(null); // close after selection
                          }}
                        >
                          {c.shortdescription}
                          <Check
                            className={cn(
                              "ml-auto h-4 w-4",
                              container.code_value === String(c.typecode)
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            {!container?.equipment_type && container?.equipment_type_value && (
              <span className="text-xs text-amber-600 font-medium italic">
                ⚠ {container?.equipment_type_value}
              </span>
            )}
            {containerErrors[idx]?.code_value && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].code_value}
              </p>
            )}
          </div>

          {/* Description */}
          <div className="flex flex-col w-[220px]">
            <textarea
              name="description"
              placeholder="Container Description"
              value={container.description}
              onChange={(e) => handleContainerChange(idx, e)}
              className={`resize-y border rounded-md px-3 py-2 text-sm h-11 ${
                containerErrors[idx]?.description ? "border-red-500" : ""
              }`}
            />
            {containerErrors[idx]?.description && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].description}
              </p>
            )}
          </div>

          {/* Shipper Seal */}
          <div className="flex flex-col w-[130px]">
            <Input
              name="shipper_seal_number"
              placeholder="Shipper Seal"
              value={container.shipper_seal_number}
              onChange={(e) => handleContainerChange(idx, e)}
            />
          </div>

          {/* Carrier Seal */}
          {/* <div className="flex flex-col">
            <Input
              name="carrier_seal_number"
              placeholder="Carrier Seal"
              value={container.carrier_seal_number}
              onChange={(e) => handleContainerChange(idx, e)}
            />
          </div> */}

          {/* Container Weight */}
          {/* <div className="flex flex-col">
            <div className="flex gap-1">
              <Input
                name="weight_value"
                placeholder="Container Weight"
                value={container.weight_value}
                onChange={(e) => handleContainerChange(idx, e)}
              />
              <select
                name="weight_type"
                value={container.weight_type}
                onChange={(e) => handleContainerChange(idx, e)}
                className="h-11 border px-1 bg-white w-17"
              >
                <option value="KG">Kgs</option>
                <option value="LBM">Lbs</option>
              </select>
            </div>
            {containerErrors[idx]?.weight_value && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].weight_value}
              </p>
            )}
          </div> */}

          {/* Gross Weight */}
          <div className="flex flex-col w-[180px]">
            <div className="flex gap-1">
              <Input
                name="cargo_gross_weight"
                placeholder="Gross Weight"
                value={container.cargo_gross_weight}
                onChange={(e) => handleContainerChange(idx, e)}
                className={cn(
                  "h-11",
                  containerErrors[idx]?.cargo_gross_weight && "border-red-500"
                )}
              />
              <select
                name="weight_type"
                value={container.weight_type}
                onChange={(e) => handleContainerChange(idx, e)}
                className="h-11 border px-1 bg-white w-17"
              >
                <option value="KG">Kgs</option>
                <option value="LBM">Lbs</option>
              </select>
            </div>
            {containerErrors[idx]?.cargo_gross_weight && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].cargo_gross_weight}
              </p>
            )}
          </div>

          {/* Gross Volume + Unit */}
          <div className="flex flex-col w-[170px]">
            <div className="flex gap-1">
              <Input
                name="gross_volume"
                placeholder="Gross Volume"
                value={container.gross_volume}
                onChange={(e) => handleContainerChange(idx, e)}
                className="h-11 flex-grow"
              />
              <select
                name="gross_volume_unit"
                value={container.gross_volume_unit}
                onChange={(e) => handleContainerChange(idx, e)}
                className="h-11 border px-1 bg-white w-17"
              >
                <option value="CBM">CBM</option>
                <option value="CBF">CBF</option>
              </select>
            </div>
            {containerErrors[idx]?.gross_volume && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].gross_volume}
              </p>
            )}
          </div>
          <div className="flex flex-col w-[120px]">
            <Input
              name="package_count"
              placeholder="Package Count"
              value={container.package_count}
              onChange={(e) => handleContainerChange(idx, e)}
              className="h-11"
            />
          </div>

          <div className="flex flex-col w-[160px]">
            <Popover
              open={packageOpenIndex === idx}
              onOpenChange={(isOpen) =>
                setPackageOpenIndex(isOpen ? idx : null)
              }
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-between h-11 truncate",
                    containerErrors[idx]?.package_type_description &&
                      "border-red-500"
                  )}
                >
                  <span
                    className={cn(
                      "truncate",
                      !container.package_type_description && "text-gray-400"
                    )}
                  >
                    {container.package_type_description
                      ? packageTypes?.find(
                          (p) =>
                            p.short_name === container.package_type_description
                        )?.short_name || container.package_type_description
                      : "Select package type..."}
                  </span>
                  <ChevronLast className="opacity-50 shrink-0 ml-2" />
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput
                    placeholder="Search Package Type..."
                    className="h-9"
                    value={packageSearch}
                    onValueChange={(val) => setPackageSearch(val)}
                  />
                  <CommandList>
                    <CommandEmpty>No Package Type Found.</CommandEmpty>
                    <CommandGroup>
                      {packageTypes?.map((p) => (
                        <CommandItem
                          key={p.name}
                          value={p.short_name}
                          onSelect={() => {
                            setHasTouchedContainers &&
                              setHasTouchedContainers(true);
                            setContainers((prev) => {
                              const updated = [...prev];
                              updated[idx].package_type_description =
                                p.short_name;
                              updated[idx].package_counttype_outermost = p.name;
                              return updated;
                            });
                            setPackageOpenIndex(null);
                          }}
                        >
                          {p.short_name}
                          <Check
                            className={cn(
                              "ml-auto h-4 w-4",
                              container.package_type_description ===
                                p.short_name
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>

            {containerErrors[idx]?.package_type_description && (
              <p className="text-red-500 text-xs mt-1">
                {containerErrors[idx].package_type_description}
              </p>
            )}
          </div>

          {/* Remove button */}
          {containers.length > 1 && (
            <div className="flex flex-col w-[60px]">
              <AlertDialog.Root>
                <AlertDialog.Trigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    className="justify-self-center mt-2 lg:mt-0"
                  >
                    <Minus />
                  </Button>
                </AlertDialog.Trigger>

                <AlertDialog.Portal>
                  <AlertDialog.Overlay className="fixed inset-0 bg-black/40" />
                  <AlertDialog.Content className="fixed top-1/2 left-1/2 w-[300px] -translate-x-1/2 -translate-y-1/2 bg-white rounded-md p-5 shadow-lg">
                    <AlertDialog.Title className="text-lg font-semibold">
                      Remove Container
                    </AlertDialog.Title>
                    <AlertDialog.Description className="mt-2 text-sm text-gray-600">
                      Are you sure you want to remove this container? This
                      action cannot be undone.
                    </AlertDialog.Description>
                    <div className="flex justify-end gap-3 mt-4">
                      <AlertDialog.Cancel asChild>
                        <Button variant="outline">Cancel</Button>
                      </AlertDialog.Cancel>
                      <AlertDialog.Action asChild>
                        <Button
                          variant="default"
                          onClick={() => removeContainer(idx)}
                        >
                          Yes, Remove
                        </Button>
                      </AlertDialog.Action>
                    </div>
                  </AlertDialog.Content>
                </AlertDialog.Portal>
              </AlertDialog.Root>
            </div>
          )}
        </div>
      ))}

      {/* Add button */}
      {/* {!isEditMode && ( */}
      <div className="flex justify-end">
        <Button variant="outline" size="sm" onClick={addContainer}>
          <PlusCircle className="w-4 h-4 mr-2" /> Add Container
        </Button>
      </div>
    </div>
  );
}
