import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Eye,
  ChevronsUpDown,
  Check,
  RefreshCcw,
  ChevronRight,
  ChevronLeft,
  Plus,
  Pen,
  Paperclip,
  Search,
  MoreHorizontal,Package
} from "lucide-react";
import { FormProvider, useForm } from "react-hook-form";
// import { ComboBox } from "../shippingInstruction/ComboBox";
import { BLComboBox } from "./blComboBox";
import {
  fetchBillOfLadingList,
  filterConsignee,
  filterCarrier,
} from "@/services/admin/billOfLading";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useSearchParams } from "react-router-dom";
import Loader from "@/components/Loader";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { useFormContext } from "react-hook-form";
import { fetchBookingLocations } from "@/services/admin/booking";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { format, parse } from "date-fns";
import dayjs from "dayjs";
import { ViewAttachedFiles } from "@/components/ui/preview";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const BillOfLaddingView = () => {
  const itemsPerPage = 20;

  const methods = useForm();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );
  const initialConsigneeQuery = searchParams.get("consignee") || "";
  const initialCarrierQuery = searchParams.get("carrier") || "";
  const initialSearchQuery = searchParams.get("searchText") || "";
  const initialPortOfLoad = {
    name: searchParams.get("pol_name") || "",
    location: searchParams.get("pol_location") || "",
    locode: searchParams.get("pol_locode") || "",
  };
  const [portOfLoadQuery, setPortOfLoadQuery] = useState("");
  const [portOfLoadOpen, setPortOfLoadOpen] = useState(false);
  const [shownValue, setShownValue] = useState(
    initialPortOfLoad.location || ""
  );

  const searchQuery = searchParams.get("searchText") || "";

  const [consigneeQuery, setConsigneeQuery] = useState(initialConsigneeQuery);
  const [carrierQuery, setCarrierQuery] = useState(initialCarrierQuery);
  const [searchText, setSearchText] = useState(initialSearchQuery);
  const portOfLoadValue = methods.watch("portOfLoad");
  const [filtersCleared, setFiltersCleared] = useState(false);

  useEffect(() => {
    const hasInitialValue =
      initialPortOfLoad.name ||
      initialPortOfLoad.location ||
      initialPortOfLoad.locode;

    if (hasInitialValue && !portOfLoadValue?.locode && !filtersCleared) {
      methods.setValue("portOfLoad", initialPortOfLoad);
    }
  }, [initialPortOfLoad, portOfLoadValue, methods, filtersCleared]);

  const {
    data: billOfLadingListData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: [
      "fetchBillOfLadingList",
      currentPage,
      // consigneeQuery,
      portOfLoadValue?.locode,
      carrierQuery,
      consigneeQuery,
      searchQuery,
    ],
    queryFn: () =>
      fetchBillOfLadingList(
        currentPage,
        // consigneeQuery,
        portOfLoadValue?.locode,
        carrierQuery,
        consigneeQuery,
        searchQuery
      ),
  });
  const totalCount = billOfLadingListData?.total_count || 0;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const { data: consigneeListData } = useQuery({
    queryKey: ["fetchConsigneeList"],
    queryFn: () => filterConsignee(),
  });

  const { data: carrierListData } = useQuery({
    queryKey: ["fetchCarrierList"],
    queryFn: () => filterCarrier(),
  });

  const { data: portOfLoadData, isFetching: isPortOfLoadFetching } = useQuery({
    queryKey: ["fetchBookingLocation", { search: portOfLoadQuery }],
    queryFn: fetchBookingLocations,
  });

  const handleSearch = (val: string) => {
    console.log("Search value:", val);
    setPortOfLoadQuery(val);
  };

  // useEffect(() => {
  //   fetchBillOfLadingList(
  //     currentPage,
  //     consigneeQuery,
  //     portOfLoadValue?.locode,
  //     carrierQuery
  //   );
  // }, [currentPage, consigneeQuery, portOfLoadValue?.locode, carrierQuery]);
  useEffect(() => {
    refetch();
  }, [currentPage, portOfLoadValue?.locode, carrierQuery, consigneeQuery]);

  const getFormattedDate = (dateString: string) => {
    if (!dateString) return "";
    const parsedDate = parse(dateString, "yyyy-MM-dd HH:mm:ss", new Date());
    const formatted = format(parsedDate, "MMM-dd-yyyy");
    return formatted;
  };
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{
    name: string;
    license: string;
  } | null>(null);
  const handleFileClick = (file) => {
    setSelectedFile({
      name: file.file_name,
      license: "/files/" + file.file_name,
    });
    setPreviewOpen(true);
  };
  const getStatusColorGradient = (status: string) => {
    switch (status) {
      case "New":
        return "#DBEAFE";
      case "Rejected":
        return "#FEE2E2";
      case "Accepted":
        return "#DCFCE7";
      case "Acknowledged":
        return "#FFEDD5";
      case "Reopen":
        return "#FEF9C3";
      case "Open":
        return "#EDE9FE";
      case "Sent":
        return "#cceded";
      case "Revised":
        return "#b6d4e3";
      default:
        return "#F3F4F6";
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case "New":
        return "#1D4ED8";
      case "Rejected":
        return "#B91C1C";
      case "Accepted":
        return "#15803D";
      case "Acknowledged":
        return "#C2410C";
      case "Reopen":
        return "#A16207";
      case "Open":
        return "#6D28D9";
      case "Sent":
        return "#2f9696";
      case "Revised":
        return "#1782b8";
      default:
        return "#374151";
    }
  };

  const getStatusBorderColor = (status: string) => {
    switch (status) {
      case "New":
        return "#3B82F6";
      case "Rejected":
        return "#EF4444";
      case "Accepted":
        return "#22C55E";
      case "Acknowledged":
        return "#F97316";
      case "Reopen":
        return "#EAB308";
      case "Open":
        return "#8B5CF6";
      case "Sent":
        return "#2f9696";
      case "Revised":
        return "#1782b8";
      default:
        return "#9CA3AF";
    }
  };
  // if (isLoading) {
  //   return (
  //     <div className="flex justify-center items-center h-screen w-screen">
  //       <Loader />
  //     </div>
  //   );
  // }
  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {/* Left side - Total Count */}
        <p className="text-sm sm:text-base mt-2 sm:mt-0">
          {totalCount}{" "}
          <span className="text-gray-500">
            Result{totalCount > 1 ? "s" : ""} Found
          </span>
        </p>

        {/* Right side - Filters */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 w-full sm:w-auto">
          {/* Port of Load filter */}
          <div className="flex-1 min-w-[200px]">
            <FormProvider {...methods}>
              <FormField
                control={methods.control}
                name="portOfLoad"
                render={({ field }) => {
                  const { value, onChange } = field;
                  return (
                    <FormItem className="w-full">
                      <FormLabel className="flex justify-between items-center">
                        <span className="text-sm text-black font-medium">
                          Port of Load
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Popover
                          open={portOfLoadOpen}
                          onOpenChange={setPortOfLoadOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="justify-between w-full overflow-hidden h-11"
                            >
                              {value?.name ? (
                                shownValue
                              ) : (
                                <span className="text-gray-400">
                                  Select Location...
                                </span>
                              )}
                              <ChevronsUpDown className="opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search Location..."
                                value={portOfLoadQuery}
                                onValueChange={(value) => handleSearch(value)}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {isPortOfLoadFetching ? (
                                    <div className="flex justify-center w-full">
                                      <SpinnerLoader />
                                    </div>
                                  ) : (
                                    "No Location Found."
                                  )}
                                </CommandEmpty>
                                <CommandGroup>
                                  {portOfLoadData?.message?.results?.map(
                                    (location) => {
                                      const locationStr = `${location?.location_name}, ${location.country} (${location.locode})`;
                                      return (
                                        <CommandItem
                                          key={location.name}
                                          value={locationStr}
                                          onSelect={() => {
                                            onChange({
                                              name: String(location.name),
                                              location: locationStr,
                                              locode: location.locode,
                                            });
                                            methods.setValue(
                                              "portOfLoadBLas",
                                              `${location.location_name}, ${
                                                location.sub_division
                                                  ? `${location.sub_division}, `
                                                  : ""
                                              }${location.country}`
                                            );
                                            setShownValue(locationStr);
                                            setPortOfLoadOpen(false);
                                            setSearchParams((prev) => {
                                              const newParams =
                                                new URLSearchParams(prev);
                                              newParams.set(
                                                "pol_name",
                                                location.name
                                              );
                                              newParams.set(
                                                "pol_location",
                                                locationStr
                                              );
                                              newParams.set(
                                                "pol_locode",
                                                location.locode
                                              );
                                              return newParams;
                                            });
                                          }}
                                        >
                                          {locationStr}
                                          <Check
                                            className={`ml-auto ${
                                              value?.name === location.name
                                                ? "opacity-100"
                                                : "opacity-0"
                                            }`}
                                          />
                                        </CommandItem>
                                      );
                                    }
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </FormProvider>
          </div>

          {/* Carrier filter */}
          <div className="flex-1 min-w-[200px]">
            <BLComboBox
              label="Carriers"
              required={false}
              value={carrierQuery}
              onChange={(value) => {
                setCarrierQuery(value);
                setCurrentPage(1);
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("carrier", value);
                  return newParams;
                });
              }}
              options={carrierListData?.message?.carriers.map((c: any) => ({
                label: c.partyname1,
                value: c.partyalias,
              }))}
              placeholder="Select Carrier"
            />
          </div>
          {/* consignee */}
          <div className="flex-1 min-w-[200px]">
            <BLComboBox
              label="Consignee"
              required={false}
              value={consigneeQuery}
              onChange={(value) => {
                setConsigneeQuery(value);
                setCurrentPage(1);
                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);
                  newParams.set("consignee", value);
                  return newParams;
                });
              }}
              options={consigneeListData?.message?.consignee.map((c: any) => ({
                label: c.customer_name,
                value: c.name,
              }))}
              placeholder="Select Consignee"
            />
          </div>
          <div className="flex-1 min-w-[200px] mt-5 relative">
            <Input
              className="pr-10"
              placeholder="Search here"
              value={searchText}
              onChange={(e) => {
                const value = e.target.value;
                setSearchText(value);
                setCurrentPage(1);

                setSearchParams((prev) => {
                  const newParams = new URLSearchParams(prev);

                  if (value.length >= 4) {
                    newParams.set("searchText", value);
                    newParams.set("page", "1");
                  } else {
                    // Clear search if less than 4 characters
                    newParams.delete("searchText");
                    newParams.set("page", "1");
                  }

                  return newParams;
                });
              }}
            />
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
          {/* Clear Filters button */}
          {(searchText ||
            portOfLoadQuery ||
            carrierQuery ||
            consigneeQuery ||
            (portOfLoadValue && portOfLoadValue.locode !== "")) && (
            <div className="flex-1 mt-5">
              <Button
                variant="outline"
                className="h-11 w-full sm:w-auto"
                onClick={() => {
                  setFiltersCleared(true);
                  setPortOfLoadQuery("");
                  setCarrierQuery("");
                  setConsigneeQuery("");
                  setSearchText("");
                  setCurrentPage(1);
                  methods.reset({
                    portOfLoad: { name: "", location: "", locode: "" },
                  });
                  setShownValue("");
                  setSearchParams({});
                }}
              >
                <RefreshCcw /> Clear Filters
              </Button>
            </div>
          )}
          <div className="flex-1 mt-5">
            <Button
              size={"lg"}
              type="button"
              variant={"outline"}
              className="border-sidebar bg-sidebar hover:bg-sidebar/80 h-11 sm:w-auto"
              onClick={() =>
                navigate(`/dashboard/documentation/create-bill-of-ladding`)
              }
            >
              <Plus className="text-white hover:text-gray-200" />
              <span className="text-white hover:text-gray-200">Upload BOL</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="mt-10 overflow-x-auto overflow-y-auto">
        <ScrollArea className="min-w-full">
          <Table className="table-auto border-1 border-[#D3DAE7] w-full">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    <span>Bl Number</span>
                    <span className="text-xs text-gray-400">
                      Carrier booking #
                    </span>
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    <span>POL</span>
                    <span className="text-xs text-gray-400">Sail Date</span>
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    <span>POD</span>
                    <span className="text-xs text-gray-400">ETA</span>
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Carrier
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Consignee
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Received Date
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Docket Status
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  BOL Status
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Total <br />
                  Container
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Total <br />
                  Gross wt
                </TableHead>
                <TableHead className="p-3 w-[5%] font-bold text-[#191C36]">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {billOfLadingListData?.message?.length ? (
                billOfLadingListData.message.map((item: any, index: number) => (
                  <TableRow
                    key={index}
                    className="h-0 hover:bg-gray-300 transition-all duration-300 transform shadow-md"
                  >
                    <TableCell className="px-3 py-1">
                      <div className="flex flex-col">
                        {item?.bol_number}
                        <span className="text-xs text-gray-500">
                          {item?.carrier_booking_number}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words">
                      <div className="flex flex-col">
                        <span className="break-words whitespace-normal">
                          {item?.port_of_load_location}, ({item?.port_of_load})
                        </span>
                        <span className="text-xs text-gray-500">
                          {getFormattedDate(item?.main_transport_sail_date)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words whitespace-normal">
                      <div className="flex flex-col">
                        <span className="break-words whitespace-normal">
                          {item?.port_of_discharge_location}, (
                          {item?.port_of_discharge})
                        </span>
                        <span className="text-xs text-gray-500">
                          {getFormattedDate(item?.eta)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words whitespace-normal">
                      {item?.carrier}
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words whitespace-normal">
                      {item?.consignee}
                    </TableCell>
                    <TableCell className="px-3 py-1">
                      {item?.create_date_time
                        ? dayjs(item?.create_date_time).format("MMM-DD-YYYY")
                        : ""}
                    </TableCell>
                    <TableCell className="px-3 py-1">
                      <span
                        className={`px-3 py-1 rounded-sm text-sm font-normal`}
                        style={{
                          color: getStatusTextColor(item.docket_status),
                          borderColor: getStatusBorderColor(item.docket_status),
                          backgroundColor: getStatusColorGradient(
                            item.docket_status
                          ),
                          borderWidth: "1px",
                          borderStyle: "solid",
                        }}
                      >
                        {item?.docket_status ?? "To Be Created"}
                      </span>
                    </TableCell>
                    <TableCell className="px-3 py-1">
                      <span className="font-bold text-gray-500">
                        {item?.message_status}
                      </span>
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words whitespace-normal">
                      {item?.total_equipment || 0}
                    </TableCell>
                    <TableCell className="px-3 py-1 break-words whitespace-normal">
                      {/* {item?.total_gross_weight || 0} */}
                      {item.total_gross_weight !== undefined && item.total_gross_weight !== null
                          ? new Intl.NumberFormat("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(item.total_gross_weight))
                          : "0"}
                    </TableCell>
                    <TableCell className="px-3 py-1 gap-2">
                      <div className="flex items-center justify-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              className="border-2 border-gray-300 bg-white hover:bg-gray-100 rounded-sm"
                            >
                              <MoreHorizontal className="h-4 w-4 text-gray-600" />
                            </Button>
                          </DropdownMenuTrigger>

                          <DropdownMenuContent align="end" className="w-40">
                            <DropdownMenuItem
                              disabled={item.attachments.length === 0}
                              onClick={(e) =>{
                                e.preventDefault();
                                handleFileClick(item.attachments[0])
                              }
                              }
                              className="flex items-center gap-2"
                            >
                              <Paperclip className="h-4 w-4 text-gray-600" />
                              Attachments
                            </DropdownMenuItem>

                            <DropdownMenuItem
                              onClick={() =>
                                navigate(
                                  `/dashboard/documentation/update-bill-of-ladding/${item.name}`
                                )
                              }
                              className="flex items-center gap-2"
                            >
                              <Pen className="h-4 w-4 text-gray-600" />
                              Edit BOL
                            </DropdownMenuItem>

                            <DropdownMenuItem
                              onClick={() =>
                                navigate(
                                  `/dashboard/documentation/my-bill-of-ladding/${item.name}`
                                )
                              }
                              className="flex items-center gap-2"
                            >
                              <Eye className="h-4 w-4 text-gray-600" />
                              View BOL
                            </DropdownMenuItem>

                            <DropdownMenuItem
                              onClick={() => {
                                if (item?.docket_id) {
                                  navigate(`/dashboard/customers/customer-docket-view/${item?.docket_id}?carrierBooking=${item?.carrier_booking_number}&main_status=${item?.docket_status}`);
                                } else {
                                  navigate(
                                    `/dashboard/customers/create-docket/${item?.carrier_booking_number}`
                                  );
                                }
                              }}
                              className={`flex items-center gap-2`}
                            >
                              <Package className="h-4 w-4 text-gray-600" />
                              {item?.docket_id
                                ? "View Docket"
                                : "Create Docket"}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center text-[#929FB8] py-4"
                  >
                    No rows to show
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
      <ViewAttachedFiles
        open={previewOpen}
        selectedFile={selectedFile}
        onOpenChange={setPreviewOpen}
        setSelectedFile={setSelectedFile}
      />
      {/* <div className="mt-8 flex justify-between items-center pr-8">
                <Typography className="text-[#929FB8]">
                    Showing 1 to 1 of 1 entries
                </Typography>
                <div className="flex gap-3">
                    <Button variant={"outline"}>
                        <ChevronLeft className="text-[#929FB8]" />
                        <span className="text-[#929FB8]">Previous</span>
                    </Button>
                    <Button variant={"outline"}>
                        <span className="text-[#929FB8]">Next</span>
                        <ChevronRight className="text-[#929FB8]" />
                    </Button>
                </div>
            </div> */}
    </div>
  );
};

export default BillOfLaddingView;
