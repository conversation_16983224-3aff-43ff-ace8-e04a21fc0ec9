import React, { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import * as Tabs from "@radix-ui/react-tabs";
import {
  PlusCircle,
  Minus,
  FileText,
  X,
  Image,
  ChevronLast,
} from "lucide-react";
import {
  createBillOfLading,
  multipleFileUploadBol,
  readOcrBillOfLading,
} from "@/services/admin/billOfLading";
import { fetchHsCodes, fetchPackageTypes } from "@/services/admin/common";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { fecthBasicBookingRequestData } from "@/services/admin/booking";
import { useQuery } from "@tanstack/react-query";
import { BLComboBox } from "../blComboBox";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

import ContainerDetails from "../containerDetails";
import CargoDetails from "../cargoDetails";
import { fetchBookingLocations } from "@/services/admin/booking";
import { PortSelectField } from "../portSelectSection";
import { fetchContractPartyList } from "@/services/admin/common";

const BillOfLadingCreate = () => {
  const navigate = useNavigate();

  // form state
  const [formData, setFormData] = useState({
    bol_number: "",
    message_status: "",
    document_type: "",
    issuingOffice: "",
    carrier: "",
    carrier_booking_number: "",
    shipper: "",
    inttra_si_number: "",
    main_vessel: "",
    main_voyage: "",
    // sail_date: "",
    place_of_receipt: "",
    place_of_delivery: "",
    port_of_load: "",
    port_of_discharge: "",
    letter_of_credit_number: "",
    export_license_issue: "",
    export_license_expiry: "",
    total_equipment: "",
    total_gross_weight: "",
    total_packages: "",
    message_type: "",
    consignee: "",
    notifyparty: "",
    contractParty: "",
    document_version: "",
    shipment_id: "",
    contract_number: "",
    document_date: "",
    shipped_on_board_date: "",
    movement_type: "",
    service_type: "",
    transport_mode: "",
    total_gross_volume: "",
    bol_release_date: "",
    bol_release_location: "",
    main_transport_sail_date: "",
    document_number: "",
    create_date_time: "",
    rated_indicator: "",
    copy_indicator: "",
    stock_required: "",
    transport_stage: "",
    lloyds_code: "",
    transport_means_type: "",
    transport_means: "",
    shipment_declared_amount: "",
    shipment_declared_currency: "",
    export_license_number: "",
    received_for_shipment: "",
    document_identifier: "",
    freight_payment_location: "",
    freight_payment_date: "",
  });

  const [containers, setContainers] = useState([
    {
      equipment_name: "",
      code_value: "",
      equipment_type: "",
      description: "",
      shipper_seal_number: "",
      carrier_seal_number: "",
      weight_value: "",
      weight_type: "KG",
      cargo_gross_weight: "",
      gross_volume: "",
      gross_volume_unit: "CBM",
      package_count: "",
      package_type_description: "",
      package_counttype_outermost: "",
      name: "",
    },
  ]);

  const [cargo, setCargo] = useState([
    {
      hs_code: "",
      cargo_description: "",
    },
  ]);

  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // OCR popup state
  const [ocrOpen, setOcrOpen] = useState(false);
  const [ocrFile, setOcrFile] = useState<File | null>(null);
  const [ocrLoading, setOcrLoading] = useState(false);
  const [showAdditional, setShowAdditional] = useState(false);

  const {
    data: initialData,
    error: initialDataError,
    isFetching: initialDataFetching,
  } = useQuery({
    queryKey: ["fetchInitalBookigData"],
    queryFn: fecthBasicBookingRequestData,
    refetchOnWindowFocus: false,
  });
  const [packageSearch, setPackageSearch] = useState("");
  const { data: packageData } = useQuery({
    queryKey: ["package-types", { search: packageSearch }], // you can pass search state here
    queryFn: fetchPackageTypes,
  });
  const [hsSearch, setHsSearch] = useState("");
  const { data: hsCodeData } = useQuery({
    queryKey: ["hs-codes", { search: hsSearch }],
    queryFn: fetchHsCodes,
  });
  const {
    data: contractPartyData,
    error: contractPartyError,
    isFetching: contractPartyFetching,
  } = useQuery({
    queryKey: ["fetchContractPartyList"],
    queryFn: fetchContractPartyList,
    refetchOnWindowFocus: false,
  });
  const packageTypes = packageData?.message?.results ?? [];
  const hsCodes = hsCodeData?.message?.results ?? [];
  const containerTypes = initialData?.message?.data?.container_types ?? [];

  const [warning2PortOfLoad, setWarning2PortOfLoad] = useState("");
  const [warning2PortOfDischarge, setWarning2PortOfDischarge] = useState("");
  const [portOfLoadLocation, setPortOfLoadLocation] = useState<string | null>(
    null
  );
  const [portOfDischargeLocation, setPortOfDischargeLocation] = useState<
    string | null
  >(null);

  const portOfLoadCheck = [
    { name: "New York", code: "USNYC" },
    { name: "Baltimore", code: "USBAL" },
    { name: "Atlanta", code: "USATL" },
    { name: "SAVANNAH", code: "USSAV" },
    { name: "Norfolk", code: "USORF" },
    { name: "Richmond", code: "USRIC" },
    { name: "SAN JUAN", code: "PRSJU" },
  ];
  const portOfDischargeCheck = [
    { name: "Nhava Sheva", code: "INNSA" },
    { name: "MUNDRA", code: "INMUN" },
  ];

  const [portOfLoadQuery, setPortOfLoadQuery] = useState("");
  const [portOfLoadOpen, setPortOfLoadOpen] = useState(false);
  const [shownPortOfLoadValue, setShownPortOfLoadValue] = useState("");

  // Destination Port state
  const [portOfDischargeQuery, setPortOfDischargeQuery] = useState("");
  const [portOfDischargeOpen, setPortOfDischargeOpen] = useState(false);
  const [shownPortOfDischargeValue, setShownPortOfDischargeValue] =
    useState("");

  const [placeOfReceiptQuery, setPlaceOfReceiptQuery] = useState("");
  const [placeOfReceiptOpen, setPlaceOfReceiptOpen] = useState(false);
  const [shownPlaceOfReceiptValue, setShownPlaceOfReceiptValue] = useState("");

  const [placeOfDeliveryQuery, setPlaceOfDeliveryQuery] = useState("");
  const [placeOfDeliveryOpen, setPlaceOfDeliveryOpen] = useState(false);
  const [shownPlaceOfDeliveryValue, setShownPlaceOfDeliveryValue] =
    useState("");

  const { data: portOfLoadData, isFetching: isPortOfLoadFetching } = useQuery({
    queryKey: ["fetchPortOfLoadLocation", { search: portOfLoadQuery }],
    queryFn: fetchBookingLocations,
  });
  const { data: portOfDischargeData, isFetching: isPortOfDischargeFetching } =
    useQuery({
      queryKey: [
        "fetchPortOfDischargeLocation",
        { search: portOfDischargeQuery },
      ],
      queryFn: fetchBookingLocations,
    });
  const { data: placeOfReceiptData, isFetching: isPlaceOfReceiptFetching } =
    useQuery({
      queryKey: [
        "fetchPlaceOfReceiptLocation",
        { search: placeOfReceiptQuery },
      ],
      queryFn: fetchBookingLocations,
    });

  const { data: placeOfDeliveryData, isFetching: isPlaceOfDeliveryFetching } =
    useQuery({
      queryKey: [
        "fetchPlaceOfDeliveryLocation",
        { search: placeOfDeliveryQuery },
      ],
      queryFn: fetchBookingLocations,
    });

  const handleOcrUpload = async () => {
    if (!ocrFile) {
      toast.error("Please upload a file first");
      return;
    }

    setOcrLoading(true); // start loader
    try {
      const formData1 = new FormData();
      formData1.append("file", ocrFile);

      console.log("Uploading to OCR:", ocrFile);
      const ocrRes = await readOcrBillOfLading(formData1);
      console.log("OCR Response:", ocrRes);

      if (ocrRes?.status === "success") {
        const testID = "empty";
        toast.success("Document scanned successfully.");
        setOcrOpen(false);
        setOcrFile(null);
        ocrRes.data &&
          navigate(
            `/dashboard/documentation/update-bill-of-ladding/${testID}`,
            {
              state: {
                ocrData: ocrRes.data,
                ocrFile: ocrFile,
                jsonFile: ocrRes.extracted_file_url,
              },
            }
          );
      } else {
        toast.error("Failed to process OCR. Please try again.");
      }
    } catch (err) {
      toast.error("Error sending file for OCR");
    } finally {
      setOcrLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => {
      const newErrors = { ...prev };
      if (value && newErrors[name]) {
        delete newErrors[name];
      }
      return newErrors;
    });
  };
const [hasTouchedContainers, setHasTouchedContainers] = useState(false);
const [hasTouchedCargo, setHasTouchedCargo] = useState(false);

  const handleContainerChange = (index, e) => {
    const { name, value } = e.target;
    setHasTouchedContainers(true);
    setContainers((prev) => {
      const updated = [...prev];
      updated[index][name] = value;
      return updated;
    });
  };

  const addContainer = () => {
    setContainers((prev) => [
      ...prev,
      {
        equipment_name: "",
        code_value: "",
        equipment_type: "",
        description: "",
        shipper_seal_number: "",
        carrier_seal_number: "",
        weight_value: "",
        weight_type: "",
        cargo_gross_weight: "",
        gross_volume: "",
        gross_volume_unit: "",
        package_count: "",
        package_type_description: "",
        package_counttype_outermost: "",
        name: "",
      },
    ]);
  };

  const removeContainer = (index) => {
    setContainers((prev) => prev.filter((_, i) => i !== index));
  };

  const handleCargoChange = (index, e) => {
    const { name, value } = e.target;
    setHasTouchedCargo(true);
    setCargo((prev) => {
      const updated = [...prev];
      updated[index][name] = value;
      return updated;
    });
  };

  // add cargo row
  const addCargo = () => {
    setCargo((prev) => [
      ...prev,
      {
        hs_code: "",
        cargo_description: "",
      },
    ]);
  };

  // remove cargo row
  const removeCargo = (index) => {
    setCargo((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
    e.target.value = "";
  };

  const handleAddAttachment = () => {
    setAttachments((prev) => [...prev, undefined as unknown as File]);
  };
  const handleSubmit1 = () => {
    console.log("Submit form data:", formData, containers, attachments);
  };

  const [containerErrors, setContainerErrors] = useState<
    {
      equipment_name?: string;
      code_value?: string;
      description?: string;
      // weight_value?: string;
      // weight_type?: string;
      cargo_gross_weight?: string;
      // gross_volume?: string;
      package_type_description?: string;
    }[]
  >([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const validateFormData = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.bol_number) {
      newErrors.bol_number = "BL number is required";
    }
    if (!formData.carrier_booking_number) {
      newErrors.carrier_booking_number = "Carrier booking number is required";
    }
    if (!formData.total_equipment) {
      newErrors.total_equipment = "Total equipment is required";
    }
    if (!formData.total_gross_weight) {
      newErrors.total_gross_weight = "Total gross weight is required";
    }

    if (!formData.port_of_discharge) {
      newErrors.port_of_discharge = "Port of Discharge is required";
    }

    if (!formData.port_of_load) {
      newErrors.port_of_load = "Port of Load is required";
    }
    if (!formData.carrier) {
      newErrors.carrier = "Carrier is required";
    }
    if (!formData.shipper) {
      newErrors.shipper = "Shipper is required";
    }
    if (!formData.consignee) {
      newErrors.consignee = "Consignee is required";
    }
    // if (!formData.place_of_delivery) {
    //   newErrors.place_of_delivery = "Place of Delivery is required";
    // }
    // if (!formData.place_of_receipt) {
    //   newErrors.place_of_receipt = "Place of Receipt is required";
    // }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      setErrors((prevErrors) => {
        const updatedErrors = { ...prevErrors };

        Object.keys(updatedErrors).forEach((field) => {
          if (formData[field as keyof typeof formData]) {
            delete updatedErrors[field]; // clear error if value exists
          }
        });

        return updatedErrors;
      });
    }

    if (formData?.port_of_load && portOfLoadLocation) {
      const found = portOfLoadCheck.find(
        (port: any) =>
          port.code.toLowerCase() === formData.port_of_load.toLowerCase()
      );
      if (!found) {
        const match = portOfLoadCheck.find((port) =>
          // port.name.toLowerCase() === portOfLoadLocation?.toLowerCase()
          portOfLoadLocation?.toLowerCase().includes(port.name.toLowerCase())
        );

        setWarning2PortOfLoad(
          match
            ? `${match.name} - ${match.code}`
            : // : selectedBillOfLading
              // ? `${selectedBillOfLading.port_of_load_location} - ${selectedBillOfLading.port_of_load}`
              ""
        );
      } else {
        setWarning2PortOfLoad("");
      }
    }

    if (formData?.port_of_discharge && portOfDischargeLocation) {
      const found = portOfDischargeCheck.find((port: any) =>
        // port.code.toLowerCase() === formData.port_of_discharge.toLowerCase()
        formData.port_of_discharge
          .toLowerCase()
          .includes(port.code.toLowerCase())
      );
      if (!found) {
        const match = portOfDischargeCheck.find((port) =>
          portOfDischargeLocation
            ?.toLowerCase()
            .includes(port.name.toLowerCase())
        );

        setWarning2PortOfDischarge(
          match
            ? `${match.name} - ${match.code}`
            : // : selectedBillOfLading
              // ? `${selectedBillOfLading.port_of_discharge_location} - ${selectedBillOfLading.port_of_discharge}`
              ""
        );
      } else {
        setWarning2PortOfDischarge("");
      }
    }
  }, [formData]);

  // validation function
  const validateContainers = () => {
    const errors = containers.map((c) => {
      const err: {
        equipment_name?: string;
        code_value?: string;
        description?: string;
        // weight_value?: string;
        cargo_gross_weight?: string;
        // gross_volume?: string;
        package_type_description?: string;
      } = {};

      // Container number validation
      if (!c.equipment_name) {
        err.equipment_name = "Container Number is required";
      } else if (!/^[A-Z]{4}\d{6,7}$/.test(c.equipment_name)) {
        err.equipment_name =
          "Invalid Container Number. Must be 4 letters (A–Z) followed by 6 or 7 digits.";
      }

      if (!c.code_value) {
        err.code_value = "Container Type is required";
      }

      // Container description validation
      if (!c.description) {
        err.description = "Container Description is required";
      }
      // if (!c.weight_value) {
      //   err.weight_value = "Container Weight is required";
      // }
      // if (!c.weight_type) {
      //   err.weight_type = "Container Weight Type is required";
      // }
      // Cargo Gross Weight validation
      if (!c.cargo_gross_weight) {
        err.cargo_gross_weight = "Gross Weight is required";
      }
      // if (!c.gross_volume) {
      //   err.gross_volume = "Gross Volume is required";
      // }
      // Package Type Description validation
      if (!c.package_type_description) {
        err.package_type_description = "Package Type is required";
      }

      return err;
    });

    setContainerErrors(errors);
    return errors.every((e) => Object.keys(e).length === 0);
  };

  const [cargoErrors, setCargoErrors] = useState<
    {
      hs_code?: string;
      cargo_description?: string;
    }[]
  >([]);

  // validation function
  const validateCargo = () => {
    const errors = cargo.map((c) => {
      const err: {
        hs_code?: string;
        cargo_description?: string;
      } = {};

      // HS Code validation
      if (!c.hs_code) {
        err.hs_code = "HS Code is required";
      }

      // Cargo Description validation
      if (!c.cargo_description) {
        err.cargo_description = "Cargo Description is required";
      }

      // } else if (isNaN(Number(c.cargo_gross_weight))) {
      //   err.cargo_gross_weight = "Gross Weight must be a number";
      // }

      return err;
    });

    setCargoErrors(errors);
    return errors.every((e) => Object.keys(e).length === 0);
  };
  useEffect(() => {
    if (!hasTouchedContainers) return;
    if (containers.length > 0) {
      validateContainers();
    }
  }, [containers]);

  useEffect(() => {
    if (!hasTouchedCargo) return;
    if (cargo.length > 0) {
      validateCargo();
    }
  }, [cargo]);
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const isContainersValid = validateContainers();
      const isCargoValid = validateCargo();
      const isFormDataValid = validateFormData();

      if (!isContainersValid || !isCargoValid || !isFormDataValid) {
        toast.error("Please check all required fields");
        setIsSubmitting(false);
        return;
      }
      const res = await createBillOfLading({
        // ...formData,
        master_data: formData,
        equipment: containers,
        cargo: cargo,
        parties: [
          { partner_role: "Shipper", name: formData.shipper },
          { partner_role: "Consignee", name: formData.consignee },
          { partner_role: "NotifyParty", name: formData.notifyparty },
          { partner_role: "Carrier", name: formData.carrier },
          { partner_role: "Contract Party", name: formData.contractParty },
        ],
        //       "references": [
        //   {
        //     "reference_type": "BookingNumber",
        //     "text": "BK-12345"
        //   },
        //   {
        //     "reference_type": "BillOfLadingNumber",
        //     "text": "NAM1234567"
        //   }
        // ],
        // "instructions": [
        //   {
        //     "main_reference": "Shipment Comments",
        //     "comment_type": "General",
        //     "text": "Handle with care"
        //   }
        // ],
      });

      // if (res?.message?.status_code !== 200) {
      //   toast.error("Failed to create Bill of Lading");
      //   return;
      // }

      if (res?.message?.status_code == 200) {
        toast.success("Bill of Lading created successfully");
        const bolId = res?.message?.bol_id;

        if (
          attachments.filter((file) => file instanceof File).length > 0 &&
          bolId
        ) {
          const uploadData = new FormData();
          attachments
            .filter((file) => file instanceof File)
            .forEach((file) => uploadData.append("attachment", file));
          uploadData.append("bol_id", bolId);
          await multipleFileUploadBol(uploadData);
        }

        navigate(-1);
      } else {
        toast.error("Failed to create Bill of Lading");
        return;
      }
    } catch (err) {
      toast.error("Error creating Bill of Lading");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pt-6">
      {/* <h1 className="text-xl font-semibold">Create Bill of Lading</h1> */}
      <div className="flex gap-4 mt-6 justify-end">
        <Button
          type="submit"
          variant={"secondary"}
          className="h-11 px-5 bg-red-600 text-white hover:bg-orange-500 "
          onClick={() => setOcrOpen(true)}
        >
          Read OCR
          <FileText className="ml-1 w-5 h-5" />
        </Button>
      </div>

      <div className="p-4 border rounded-lg">
        <Tabs.Root defaultValue="general" className="w-full">
          {/* Tab Headers */}
          <Tabs.List className="flex border-b mb-4">
            <Tabs.Trigger
              value="general"
              className="px-5 py-2 text-sm sm:text-base font-medium text-gray-600 
                         hover:text-gray-900 hover:bg-gray-50 
                         data-[state=active]:text-blue-600 
                         data-[state=active]:border-b-2 
                         data-[state=active]:border-blue-600 
                         transition-colors"
            >
              General Details
            </Tabs.Trigger>

            <Tabs.Trigger
              value="additional"
              className="px-5 py-2 text-sm sm:text-base font-medium text-gray-600 
                         hover:text-gray-900 hover:bg-gray-50 
                         data-[state=active]:text-blue-600 
                         data-[state=active]:border-b-2 
                         data-[state=active]:border-blue-600 
                         transition-colors"
            >
              Additional Details
            </Tabs.Trigger>
          </Tabs.List>

          {/* General Details */}
          <Tabs.Content value="general">
            <div className="grid grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  BL Number <span className="text-red-500">*</span>
                </label>
                <Input
                  name="bol_number"
                  value={formData.bol_number}
                  onChange={handleChange}
                  placeholder="Enter BL Number"
                  required
                />
                <p className="text-sm text-red-500 mt-1">
                  {errors?.bol_number}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <Input
                  name="message_status"
                  value={formData.message_status}
                  onChange={handleChange}
                  placeholder="Enter Status"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Message Type
                </label>
                <Input
                  name="message_type"
                  value={formData.message_type}
                  onChange={handleChange}
                  placeholder="Enter message type"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Document Type
                </label>
                <Input
                  name="document_type"
                  value={formData.document_type}
                  onChange={handleChange}
                  placeholder="Enter Document Type"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Issuing Office
                </label>
                <Input
                  name="issuingOffice"
                  value={formData.issuingOffice}
                  onChange={handleChange}
                  placeholder="Enter Issuing Office"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Carrier Booking Number<span className="text-red-500">*</span>
                </label>
                <Input
                  name="carrier_booking_number"
                  value={formData.carrier_booking_number}
                  onChange={handleChange}
                  placeholder="Enter Carrier Booking Number"
                  required
                />
                <p className="text-sm text-red-500 mt-1">
                  {errors?.carrier_booking_number}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  INTTRA SI Number
                </label>
                <Input
                  name="inttra_si_number"
                  value={formData.inttra_si_number}
                  onChange={handleChange}
                  placeholder="Enter INTTRA SI Number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Main Vessel
                </label>
                <Input
                  name="main_vessel"
                  value={formData.main_vessel}
                  onChange={handleChange}
                  placeholder="Enter Main Vessel"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Main Voyage
                </label>
                <Input
                  name="main_voyage"
                  value={formData.main_voyage}
                  onChange={handleChange}
                  placeholder="Enter Main Voyage"
                />
              </div>

              <div>
                  <label className="block text-sm font-medium mb-1">
                    Main Transport Sail Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="main_transport_sail_date"
                    value={formData.main_transport_sail_date}
                    onChange={handleChange}
                  />
                </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Transport Mode
                </label>
                <Input
                  name="transport_mode"
                  value={formData.transport_mode}
                  onChange={handleChange}
                  placeholder="Enter Transport Mode"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Movement Type
                </label>
                <Input
                  name="movement_type"
                  value={formData.movement_type}
                  onChange={handleChange}
                  placeholder="Enter Movement Type"
                />
              </div>

              {/* <div>
                <label className="block text-sm font-medium mb-1">
                  Place of Receipt
                </label>
                <Input
                  name="place_of_receipt"
                  value={formData.place_of_receipt}
                  onChange={handleChange}
                  placeholder="Enter Place of Receipt"
                />
              </div> */}
              <PortSelectField
                isRequired={false}
                name="place_of_receipt"
                label="Place of Receipt"
                query={placeOfReceiptQuery}
                setQuery={setPlaceOfReceiptQuery}
                open={placeOfReceiptOpen}
                setOpen={setPlaceOfReceiptOpen}
                shownValue={shownPlaceOfReceiptValue}
                setShownValue={setShownPlaceOfReceiptValue}
                data={placeOfReceiptData}
                isFetching={isPlaceOfReceiptFetching}
                value={formData.place_of_receipt || null}
                onChange={(val) => {
                  if (val === null) {
                    setFormData((prev) => ({
                      ...prev,
                      place_of_receipt: "",
                    }));
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      place_of_receipt: val,
                    }));
                  }
                }}
                error={errors?.place_of_receipt}
              />

              {/* <div>
                <label className="block text-sm font-medium mb-1">
                  Place of Delivery
                </label>
                <Input
                  name="place_of_delivery"
                  value={formData.place_of_delivery}
                  onChange={handleChange}
                  placeholder="Enter Place of Delivery"
                />
              </div> */}
              <PortSelectField
                isRequired={false}
                name="place_of_delivery"
                label="Place of Delivery"
                query={placeOfDeliveryQuery}
                setQuery={setPlaceOfDeliveryQuery}
                open={placeOfDeliveryOpen}
                setOpen={setPlaceOfDeliveryOpen}
                shownValue={shownPlaceOfDeliveryValue}
                setShownValue={setShownPlaceOfDeliveryValue}
                data={placeOfDeliveryData}
                isFetching={isPlaceOfDeliveryFetching}
                value={formData.place_of_delivery || null}
                onChange={(val) => {
                  if (val === null) {
                    setFormData((prev) => ({
                      ...prev,
                      place_of_delivery: "",
                    }));
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      place_of_delivery: val,
                    }));
                  }
                }}
                error={errors?.place_of_delivery}
              />

              {/* <div>
                <label className="block text-sm font-medium mb-1">
                  Port of Load <span className="text-red-500">*</span>
                </label>
                <Input
                  name="port_of_load"
                  value={formData.port_of_load}
                  onChange={handleChange}
                  placeholder="Enter Port of Load"
                  required
                />
              </div> */}
              <PortSelectField
                warning2={warning2PortOfLoad}
                setLocation={setPortOfLoadLocation}
                location={portOfLoadLocation}
                isRequired={true}
                name="port_of_load"
                label="Port of Load"
                query={portOfLoadQuery}
                setQuery={setPortOfLoadQuery}
                open={portOfLoadOpen}
                setOpen={setPortOfLoadOpen}
                shownValue={shownPortOfLoadValue}
                setShownValue={setShownPortOfLoadValue}
                data={portOfLoadData}
                isFetching={isPortOfLoadFetching}
                value={formData.port_of_load || null}
                onChange={(val) => {
                  if (val === null) {
                    setFormData((prev) => ({
                      ...prev,
                      port_of_load: "",
                    }));
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      port_of_load: val,
                    }));
                  }
                }}
                error={errors.port_of_load}
              />

              {/* <div>
                <label className="block text-sm font-medium mb-1">
                  Port of Discharge<span className="text-red-500">*</span>
                </label>
                <Input
                  name="port_of_discharge"
                  value={formData.port_of_discharge}
                  onChange={handleChange}
                  placeholder="Enter Port of Discharge"
                  required
                />
              </div> */}
              <PortSelectField
                warning2={warning2PortOfDischarge}
                setLocation={setPortOfDischargeLocation}
                location={portOfDischargeLocation}
                isRequired={true}
                name="port_of_discharge"
                label="Port of Discharge"
                query={portOfDischargeQuery}
                setQuery={setPortOfDischargeQuery}
                open={portOfDischargeOpen}
                setOpen={setPortOfDischargeOpen}
                shownValue={shownPortOfDischargeValue}
                setShownValue={setShownPortOfDischargeValue}
                data={portOfDischargeData}
                isFetching={isPortOfDischargeFetching}
                value={formData.port_of_discharge || null}
                onChange={(val) => {
                  if (val === null) {
                    setFormData((prev) => ({
                      ...prev,
                      port_of_discharge: "",
                    }));
                  } else {
                    setFormData((prev) => ({
                      ...prev,
                      port_of_discharge: val,
                    }));
                  }
                }}
                error={errors.port_of_discharge}
              />

              <div>
                <label className="block text-sm font-medium mb-1">
                  Letter of Credit Ref
                </label>
                <Input
                  name="letter_of_credit_number"
                  value={formData.letter_of_credit_number}
                  onChange={handleChange}
                  placeholder="Enter Letter of Credit Ref"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Export License Issue Date
                </label>
                <Input
                  type="date"
                  name="export_license_issue"
                  value={formData.export_license_issue}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Export License Expiry Date
                </label>
                <Input
                  type="date"
                  name="export_license_expiry"
                  value={formData.export_license_expiry}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Equipments <span className="text-red-500">*</span>
                </label>
                <Input
                  name="total_equipment"
                  value={formData.total_equipment}
                  onChange={handleChange}
                  placeholder="Enter Total Equipments"
                  required
                />
                <p className="text-sm text-red-500 mt-1">
                  {errors?.total_equipment}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Gross Weight <span className="text-red-500">*</span>
                </label>
                <Input
                  name="total_gross_weight"
                  value={formData.total_gross_weight}
                  onChange={handleChange}
                  placeholder="Enter Total Gross Weight"
                  required
                />
                <p className="text-sm text-red-500 mt-1">
                  {errors?.total_gross_weight}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Gross Volume
                </label>
                <Input
                  name="total_gross_volume"
                  value={formData.total_gross_volume}
                  onChange={handleChange}
                  placeholder="Enter Total Gross Volume"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Total Packages
                </label>
                <Input
                  name="total_packages"
                  value={formData.total_packages}
                  onChange={handleChange}
                  placeholder="Enter Total Packages"
                />
              </div>
            </div>
          </Tabs.Content>
          <Tabs.Content value="additional">
            {/* <div className="mt-4">
        <button
          type="button"
          className="text-blue-600 text-sm underline"
          onClick={() => setShowAdditional(!showAdditional)}
        >
          {showAdditional ? "Hide Additional Fields" : "Show Additional Fields"}
        </button>
      </div> */}

            {/* Additional Fields Section */}
            <div className="mt-4 bg-gray-50">
              {/* <h3 className="font-semibold mb-3">Additional Fields</h3> */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Version
                  </label>
                  <Input
                    name="document_version"
                    value={formData.document_version}
                    onChange={handleChange}
                    placeholder="Enter Document Version"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Shipment ID
                  </label>
                  <Input
                    name="shipment_id"
                    value={formData.shipment_id}
                    onChange={handleChange}
                    placeholder="Enter Shipment ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Contract Number
                  </label>
                  <Input
                    name="contract_number"
                    value={formData.contract_number}
                    onChange={handleChange}
                    placeholder="Enter Contract Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="document_date"
                    value={formData.document_date}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Shipped on Board Date
                  </label>
                  <Input
                    type="datetime-local"
                    name="shipped_on_board_date"
                    value={formData.shipped_on_board_date}
                    onChange={handleChange}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Service Type
                  </label>
                  <Input
                    name="service_type"
                    value={formData.service_type}
                    onChange={handleChange}
                    placeholder="Enter Service Type"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    BOL Release Date
                  </label>
                  <Input
                    type="date"
                    name="bol_release_date"
                    value={formData.bol_release_date}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    BOL Release Location
                  </label>
                  <Input
                    name="bol_release_location"
                    value={formData.bol_release_location}
                    onChange={handleChange}
                    placeholder="Enter BOL Release Location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Number
                  </label>
                  <Input
                    name="document_number"
                    value={formData.document_number}
                    onChange={handleChange}
                    placeholder="Enter Document Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Create Date Time
                  </label>
                  <Input
                    type="datetime-local"
                    name="create_date_time"
                    value={formData.create_date_time}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Rated Indicator
                  </label>
                  <Input
                    name="rated_indicator"
                    value={formData.rated_indicator}
                    onChange={handleChange}
                    placeholder="Enter Rated Indicator"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Copy Indicator
                  </label>
                  <Input
                    name="copy_indicator"
                    value={formData.copy_indicator}
                    onChange={handleChange}
                    placeholder="Enter Copy Indicator"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Lloyds Code
                  </label>
                  <Input
                    name="lloyds_code"
                    value={formData.lloyds_code}
                    onChange={handleChange}
                    placeholder="Enter Lloyds Code"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Stock Required
                  </label>
                  <Input
                    name="stock_required"
                    value={formData.stock_required}
                    onChange={handleChange}
                    placeholder="Enter Stock Required"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Stage
                  </label>
                  <Input
                    name="transport_stage"
                    value={formData.transport_stage}
                    onChange={handleChange}
                    placeholder="Enter Transport Stage"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Means Type
                  </label>
                  <Input
                    name="transport_means_type"
                    value={formData.transport_means_type}
                    onChange={handleChange}
                    placeholder="Enter Transport Means Type"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Transport Means
                  </label>
                  <Input
                    name="transport_means"
                    value={formData.transport_means}
                    onChange={handleChange}
                    placeholder="Enter Transport Means"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Declared Amount
                  </label>
                  <Input
                    name="shipment_declared_amount"
                    value={formData.shipment_declared_amount}
                    onChange={handleChange}
                    placeholder="Enter Declared Amount"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Declared Currency
                  </label>
                  <Input
                    name="shipment_declared_currency"
                    value={formData.shipment_declared_currency}
                    onChange={handleChange}
                    placeholder="Enter Declared Currency"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Export License Number
                  </label>
                  <Input
                    name="export_license_number"
                    value={formData.export_license_number}
                    onChange={handleChange}
                    placeholder="Enter Export License Number"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Received For Shipment
                  </label>
                  <Input
                    type="datetime-local"
                    name="received_for_shipment"
                    value={formData.received_for_shipment}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Document Identifier
                  </label>
                  <Input
                    name="document_identifier"
                    value={formData.document_identifier}
                    onChange={handleChange}
                    placeholder="Enter Document Identifier"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Freight Payment Location
                  </label>
                  <Input
                    name="freight_payment_location"
                    value={formData.freight_payment_location}
                    onChange={handleChange}
                    placeholder="Enter Freight Payment Location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Freight Payment Date
                  </label>
                  <Input
                    type="date"
                    name="freight_payment_date"
                    value={formData.freight_payment_date}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>

      {/* Parties Section */}
      <div className="p-4 border rounded-lg">
        <h2 className="font-semibold mb-2">Parties</h2>
        <div className="grid grid-cols-3 gap-4">
          <div>
            {/* <label className="block text-sm font-medium mb-1">
            Carrier <span className="text-red-500">*</span>
          </label> */}
            {/* <Input
            name="carrier"
            value={formData.carrier}
            onChange={handleChange}
            placeholder="Enter Carrier"
            required
          /> */}

            <BLComboBox
              error={errors?.carrier}
              label="Carriers"
              required={true}
              value={formData.carrier}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  carrier: value,
                }));
              }}
              options={
                initialData?.message?.data?.carriers?.map((c: any) => ({
                  label: c.partyname1,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Carrier"
            />
          </div>

          <div>
            {/* <label className="block text-sm font-medium mb-1">
              Shipper <span className="text-red-500">*</span>
            </label>
            <Input
              name="shipper"
              value={formData.shipper}
              onChange={handleChange}
              placeholder="Enter Shipper"
              required
            /> */}
            <BLComboBox
              error={errors?.shipper}
              label="Shipper"
              required={true}
              value={formData.shipper}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  shipper: value,
                }));
              }}
              options={
                initialData?.message?.data?.shippers?.map((c: any) => ({
                  label: c.shipper_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Shipper"
            />
          </div>

          <div>
            <BLComboBox
              error={errors?.consignee}
              label="Consignee"
              required={true}
              value={formData.consignee}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  consignee: value,
                }));
              }}
              options={
                initialData?.message?.data?.customers?.map((c: any) => ({
                  label: c.company_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Consignee"
            />
          </div>
          <div>
            <BLComboBox
              label="Notify Party"
              required={false}
              value={formData.notifyparty}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  notifyparty: value,
                }));
              }}
              options={
                initialData?.message?.data?.customers?.map((c: any) => ({
                  label: c.company_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Notify Party"
            />
          </div>
          <div>
            <BLComboBox
              label="Contract Party"
              required={false}
              value={formData.contractParty}
              onChange={(value) => {
                setFormData((prev) => ({
                  ...prev,
                  contractParty: value,
                }));
              }}
              options={
                contractPartyData?.message?.data?.map((c: any) => ({
                  label: c.party_name,
                  value: c.name,
                })) ?? []
              }
              placeholder="Select Contract Party"
            />
          </div>
        </div>
      </div>
      {/* cargo section */}
      <CargoDetails
        cargo={cargo}
        cargoErrors={cargoErrors}
        handleCargoChange={handleCargoChange}
        addCargo={addCargo}
        removeCargo={removeCargo}
        packageTypes={packageTypes ?? []}
        setCargo={setCargo}
        hsCodes={hsCodes ?? []}
        setHsSearch={setHsSearch}
        hsSearch={hsSearch}
        setHasTouchedCargo={setHasTouchedCargo}
      />

      {/* Container Details */}
      <section className="mt-6">
        <ContainerDetails
          containers={containers}
          containerTypes={containerTypes ?? []}
          containerErrors={containerErrors}
          handleContainerChange={handleContainerChange}
          handleCargoChange={handleCargoChange}
          setContainers={setContainers}
          addContainer={addContainer}
          removeContainer={removeContainer}
          isEditMode={false}
          packageTypes={packageTypes ?? []}
          setPackageSearch={setPackageSearch}
          packageSearch={packageSearch}
          setHasTouchedContainers={setHasTouchedContainers}
        />
      </section>
      {/* File Upload Section */}
      <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Upload b/l</h2>

        {attachments.map((file, index) => (
          <div key={index} className="flex w-1/2 items-center space-x-2 mb-3">
            <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
              <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                <Image className="w-5 h-5 text-gray-600" />
                <span className="font-medium">CHOOSE FILE</span>
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => handleAttachmentChange(e, index)}
                />
              </label>
              <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                <span className="truncate text-gray-700">
                  {file?.name || "Upload Attach File Here...."}
                </span>
                {file && (
                  <button
                    type="button"
                    onClick={() => {
                      const updated = [...attachments];
                      updated[index] = undefined as unknown as File;
                      setAttachments(updated);
                    }}
                    className="text-gray-500 hover:text-red-600 ml-2"
                    title="Remove file"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {attachments.length > 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setAttachments(attachments.filter((_, i) => i !== index))
                }
              >
                <Minus />
              </Button>
            )}

            {index === attachments.length - 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={handleAddAttachment}
              >
                <PlusCircle />
              </Button>
            )}
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => navigate(-1)}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Uploading..." : "Upload BL"}
        </Button>
      </div>
      {/* ocr popup */}
      <Dialog open={ocrOpen} onOpenChange={setOcrOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Upload File <span className="text-red-500">*</span>
            </DialogTitle>
            <DialogDescription>(Only PDF files are allowed)</DialogDescription>
          </DialogHeader>

          <div className="flex items-center gap-3">
            <label className="flex items-center gap-2 px-3 py-2 border rounded-md cursor-pointer bg-white">
              <Image className="w-5 h-5 text-gray-600" />
              <span className="text-sm">Choose File</span>
              <input
                type="file"
                accept="application/pdf"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    if (file.type !== "application/pdf") {
                      toast.error("Only PDF files are allowed");
                      e.target.value = "";
                      return;
                    }
                    setOcrFile(file);
                  }
                }}
              />
            </label>
            <span className="truncate text-sm text-gray-700">
              {ocrFile?.name || "No file chosen"}
            </span>
            {ocrFile && (
              <button
                type="button"
                onClick={() => setOcrFile(null)}
                className="text-gray-500 hover:text-red-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setOcrOpen(false)}
              disabled={ocrLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleOcrUpload} disabled={ocrLoading}>
              {ocrLoading ? (
                <div className="flex items-center gap-2">
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    />
                  </svg>
                  <span>Processing...</span>
                </div>
              ) : (
                "OK"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BillOfLadingCreate;
