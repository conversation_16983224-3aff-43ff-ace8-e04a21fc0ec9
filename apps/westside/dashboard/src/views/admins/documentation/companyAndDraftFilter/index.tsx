import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { ComboBox } from "@/views/admins/documentation/shippingInstruction/ComboBox";
import { Input } from "@/components/ui/input";
import { PanelTop, Trash2, ChevronLeft, ChevronRight, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/typography";
import { deleteSIRequestTemplate, fetchSITemplateListData } from "@/services/admin/shippingInstruction";
import { useMutation, useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { Link, useNavigate } from "react-router-dom";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

const CompanyAndDraftFilter = () => {
  const navigate = useNavigate();
  const [selectedRow, setSelectedRow] = useState<string>();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);

  // Reset to first page when search term or page size changes
  React.useEffect(() => {
    setCurrentPage(0);
  }, [searchTerm, pageSize]);

  // fetching SI Template Listing data.
  const {
    data: siTempData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["fetchSITemplateData", {
      page: currentPage,
      limit_page_length: pageSize,
      template_name: searchTerm.trim() || undefined
    }],
    queryFn: fetchSITemplateListData,
    refetchOnWindowFocus: false,
  });

  const useShippingTemplate = () => {
    if (selectedRow) {
      navigate(`/dashboard/booking/my-booking/create-si?template_id=${selectedRow}`);
    } else {
      alert("Select a Row");
    }
  };

  // Get data directly from API (server-side filtering and pagination)
  const templateData = siTempData?.message?.message?.data || [];
  const totalCount = siTempData?.message?.message?.total_count || 0;
  const hasNext = siTempData?.message?.message?.has_next || false;

  // Delete template data.
  const { mutate, isPending } = useMutation({
    mutationFn: deleteSIRequestTemplate,
    onSuccess: (data) => {
      if (data?.message?.status === "error") {
        toast.error(data?.message?.message);
      } else {
        toast.success("Template deleted successfully!");
        refetch();
      }
    },
    onError: () => {
      toast.error("Failed to delete Template. Please try again.");
    },
  });

  return (
    <div>
      {/* <div className="bg-[#FFFFFF] border-x border-b border-[#D3DAE7] h-20 flex align-middle px-4 items-center justify-between">
                <div className="grid grid-cols-3 gap-4">
                    <div className="relative flex w-full">
                        <div className="flex items-center bg-[#E5E8EF] border px-3 min-w-24">
                            <Label htmlFor="picture">Show</Label>
                        </div>
                        <div className="w-full">
                            <Select>
                                <SelectTrigger className="w-full border-[#D3DAE7] rounded-none">
                                    <SelectValue
                                        className="w-full text-[#191C36]"
                                        placeholder="My Template"
                                    />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectLabel>My Template</SelectLabel>
                                        <SelectItem value="apple">Apple</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="relative flex w-full">
                        <div className="flex items-center bg-[#E5E8EF] border px-3 min-w-24">
                            <Label htmlFor="picture">Filter by</Label>
                        </div>
                        <div className="w-full">
                            <Select>
                                <SelectTrigger className="w-full border-[#D3DAE7] rounded-none">
                                    <SelectValue className="w-full" placeholder="Carrier" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectLabel>Carrier</SelectLabel>
                                        <SelectItem value="apple">Apple</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="w-full">
                        <Select>
                            <SelectTrigger className="w-full border-[#D3DAE7] rounded-none">
                                <SelectValue className="w-full" placeholder="CMA CGM" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectLabel>Carrier</SelectLabel>
                                    <SelectItem value="apple">Apple</SelectItem>
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="flex justify-end gap-1.5 items-center p-2">
                    <Button
                        size={"lg"}
                        variant={"outline"}
                        className="border-primary text-primary hover:bg-primary hover:text-white hover:primary"
                    >
                        <span>Clear Filter</span>
                    </Button>
                </div>
            </div> */}
      <div className="flex items-center justify-between gap-4 py-6 align-middle">
        <div className="flex flex-row gap-3">
          <Button
            type="button"
            onClick={useShippingTemplate}
            variant={"outline"}
            className="border-[#D3DAE7] flex gap-2 p-5"
          >
            <PanelTop />
            <span className="">Open Template</span>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger>
              <Button disabled={isPending} variant={"outline"} className="border-[#D3DAE7] flex gap-2 p-5">
                <Trash2 /> <span className="">Delete</span>
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete your template.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Not sure</AlertDialogCancel>
                <AlertDialogAction
                  className="bg-red-600 hover:bg-red-600/90"
                  onClick={() => {
                    if (!selectedRow) {
                      return toast.error("Select One Template");
                    }
                    mutate(selectedRow || "");
                  }}
                >
                  Sure, Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* <Button variant={"outline"} className="border-[#D3DAE7] flex gap-2 p-5">
                        <Plus />
                        <span className="">New</span>
                    </Button> */}
        </div>

        {/* Page Size Selector and Search Box */}
        <div className="flex items-center gap-4">
          {/* Page Size Selector */}
          <div className="flex items-center gap-2">
            <Typography variant="small" className="whitespace-nowrap">Show:</Typography>
            <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
              <SelectTrigger className="w-20 border-[#D3DAE7]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Search Box */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-64 border-[#D3DAE7]"
            />
          </div>
        </div>
      </div>
      {/* <div className="flex items-center justify-between gap-4 py-3 align-middle">
                <div className="grid grid-cols-1 gap-3">
                    <div className="relative flex w-full">
                        <div className="flex items-center px-3">
                            <Typography className="" variant={"small"}>
                                Show
                            </Typography>
                        </div>
                        <div className="w-full">
                            <Select>
                                <SelectTrigger className="w-full border-[#D3DAE7] rounded-none">
                                    <SelectValue
                                        className="w-full text-[#191C36]"
                                        placeholder="10"
                                    />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectLabel>10</SelectLabel>
                                        <SelectItem value="20">20</SelectItem>
                                        <SelectItem value="50">50</SelectItem>
                                        <SelectItem value="100">100</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>
                <div className="">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
                            <div className="w-full">
                                <FormField
                                    control={form.control}
                                    name="filterResults"
                                    render={({ field }) => (
                                        <FormItem>
                                            <div className="flex items-center w-full gap-3">
                                                <FormLabel className="whitespace-nowrap">
                                                    Filter Results
                                                </FormLabel>
                                                <FormControl>
                                                    <Input
                                                        placeholder="Enter Filter Value"
                                                        {...field}
                                                    />
                                                </FormControl>
                                            </div>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </form>
                    </Form>
                </div>
            </div> */}
      <div className="mt-2 overflow-x-auto">
        <RadioGroup value={selectedRow} onValueChange={setSelectedRow}>
          <Table className="border-1 border-[#D3DAE7]">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                <TableHead className="p-1">
                  <ComboBox headTitle="" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="TEMPLATE NAME" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="CREATION/MODIFIED DATE(GMT)" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="CARRIER" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="VESSEL" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="VOYAGE" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="POD" />
                </TableHead>
                <TableHead className="p-3">
                  <ComboBox headTitle="CREATED BY" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <Typography className="text-gray-500">
                      Loading templates...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : templateData?.length ? (
                templateData?.map((item: any, ind: number) => (
                  <TableRow key={ind}>
                    <TableCell className="pl-6">
                      <RadioGroupItem value={item?.name} id={`radio-${ind}`} />
                    </TableCell>
                    <TableCell className="pl-6">
                      <Link to={`/dashboard/booking/my-booking/create-si?template_id=${item?.name}`}>
                        <Typography className="underline text-primary" variant={"small"}>
                          {item?.template_name}
                        </Typography>
                      </Link>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{dayjs(item?.modified)?.format("MMM-DD-YYYY")}</Typography>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{item.carrier}</Typography>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{item.vessel}</Typography>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{item.voyage}</Typography>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{item.pod}</Typography>
                    </TableCell>
                    <TableCell className="pl-6">
                      <Typography>{item.owner}</Typography>
                    </TableCell>
                  </TableRow>
                ))
              ) : searchTerm ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <Typography className="text-gray-500">
                      No templates found matching "{searchTerm}"
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <Typography className="text-gray-500">
                      No templates available
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </RadioGroup>
      </div>

      <div className="flex items-center justify-between mt-8 ml-3">
        <Typography className="text-[#929FB8]">
          Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalCount)} of {totalCount} entries
          {searchTerm && ` (filtered)`}
        </Typography>
        <div className="flex gap-3">
          <Button
            variant={"outline"}
            disabled={currentPage === 0}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            <ChevronLeft className="text-[#929FB8]" />
            <span className="text-[#929FB8]">Previous</span>
          </Button>
          <Button
            variant={"outline"}
            disabled={!hasNext}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            <span className="text-[#929FB8]">Next</span>
            <ChevronRight className="text-[#929FB8]" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CompanyAndDraftFilter;
