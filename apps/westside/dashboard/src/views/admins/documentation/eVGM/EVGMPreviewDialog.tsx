import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChevronRight } from "lucide-react";
import dayjs from "dayjs";

type Props = {
  open: boolean;
  onClose: () => void;
  previewData: any;
  onConfirm: () => void;
  submitting?: boolean;
};

const EVGMPreviewDialog = ({
  open,
  onClose,
  previewData,
  onConfirm,
  submitting,
}: Props) => {

  const massUnit = (unit: string) => {
    switch (unit) {
      case "KGM":
        return "Kg";
      case "LBR":
        return "Lb";
      default:
        return unit;
    }
  }
  const formatWeight = (val: any) => {
  if (val === undefined || val === null) return "";
  const num = Number(val);
  return Number.isInteger(num) ? `${num}.00` : num.toString();
};

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="w-screen h-screen max-w-none rounded-none md:max-w-4xl lg:max-w-5xl">
        <DialogHeader>
          <DialogTitle>
            Please Review the details of eVGM before submission
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 max-h-[65vh] overflow-y-auto px-2">
          {/* Step 1: Parties */}
          <section>
            <h2 className="text-lg font-semibold mb-3 flex items-center">
              eVGM Parties
            </h2>
            <Card>
              <CardContent className="space-y-2 py-4">
                <p>
                  <strong>Acting As:</strong> {previewData?.acting_as}
                </p>
                <p>
                  <strong>Booking #:</strong> {previewData?.booking_number}
                </p>
                {/* If you want, map all additional parties here */}
                {previewData?.acting_as === "Responsible Party" && (
                  <p>
                    <strong>Responsible Party:</strong>{" "}
                    {previewData?.responsible_party?.data}
                  </p>
                )}
                {previewData?.acting_as !== "Responsible Party" && (
                  <>
                    <p>
                      <strong>Responsible Party:</strong>{" "}
                      {previewData?.responsible_party?.data}
                    </p>
                    <p>
                      <strong>Authorized Party:</strong>{" "}
                      {previewData?.authorized_party?.data}
                    </p>
                  </>
                )}
                <p>
                  <strong>Shipper:</strong> {previewData?.shipper?.data}
                </p>

                <p>
                  <strong>Carrier:</strong> {previewData?.carrier?.data}
                </p>
                <p>
                  <strong>Forwarder:</strong> {previewData?.forwarder?.data}
                </p>
                <p>
                  <strong>Terminal Operator:</strong>{" "}
                  {previewData?.terminalOperator?.data}
                </p>
              </CardContent>
            </Card>
          </section>

          {/* Step 2: Containers */}
          {/* Step 2: Containers */}
          <section>
            <h2 className="text-lg font-semibold mb-3 flex items-center">
              Container Details
            </h2>

            <div className="overflow-x-auto border rounded-lg">
              <table className="w-full border-collapse">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="border px-3 py-2 text-left">Container #</th>
                    <th className="border px-3 py-2 text-left">Type</th>
                    <th className="border px-3 py-2 text-left">Weight Determination Date</th>
                    <th className="border px-3 py-2 text-left">
                      Cargo Gross Weight
                    </th>
                    <th className="border px-3 py-2 text-left">Tare Weight</th>
                    <th className="border px-3 py-2 text-left">
                      Verified Gross Mass
                    </th>
                    <th className="border px-3 py-2 text-left">Seal #</th>
                    <th className="border px-3 py-2 text-left">
                      Weight Determination Method
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {previewData?.equipment_details?.map(
                    (c: any, idx: number) => (
                      <tr key={idx} className="odd:bg-white even:bg-gray-50">
                        <td className="border px-3 py-2">
                          {c.equipment_number}
                        </td>
                        <td className="border px-3 py-2">{c.equipment_type}</td>
                        <td className="border px-3 py-2">
                          {c.weight_determination_date_time
                            ? dayjs(c.weight_determination_date_time).format(
                                "MMM-DD-YYYY hh:mm A"
                              )
                            : ""}
                        </td>
                        <td className="border px-3 py-2">
                          {formatWeight(c.equiment_gross_weight)}
                        </td>
                        <td className="border px-3 py-2">
                          {formatWeight(c.equiment_tare_weight)}
                        </td>
                        <td className="border px-3 py-2">
                          {formatWeight(c.verified_gross_mass)}{" "}
                          {c.verified_gross_mass && massUnit(c.verified_gross_mass_unit)}
                        </td>
                        <td className="border px-3 py-2">
                          {c.shipper_seal_number}
                        </td>
                        <td className="border px-3 py-2">
                          {c.equipment_number && c.weight_determination_method}
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>
          </section>

          {/* Step 3: Approval */}
          <section>
            <h2 className="text-lg font-semibold mb-3 flex items-center">
              Approval & Notifications
            </h2>
            <Card>
              <CardContent className="py-4 space-y-2">
                <p>
                  <strong>Approval Signature:</strong>{" "}
                  {previewData?.approval_signature}
                </p>
                <p>
                  <strong>Approval Date:</strong>{" "}
                  {previewData?.approval_datetime ? dayjs(previewData?.approval_datetime).format(
                                "MMM-DD-YYYY hh:mm A"
                              )
                            : ""}
                </p>
                <p>
                  <strong>Notification Emails:</strong>{" "}
                  {previewData?.partner_notification_emails || "-"}
                </p>
                <p>
                  <strong>Notify Me:</strong>{" "}
                  {previewData?.notify_status_update ? "Yes" : "No"}
                </p>
              </CardContent>
            </Card>
          </section>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="secondary" onClick={onClose}>
            Back & Edit
          </Button>
          <Button
            type="button"
            className="bg-orange-500 text-white"
            onClick={onConfirm}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <svg
                  className="animate-spin h-4 w-4 mr-2 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                  ></path>
                </svg>
                Submitting...
              </>
            ) : (
              <>
                Confirm & Submit <ChevronRight className="ml-1 w-4 h-4" />
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EVGMPreviewDialog;
