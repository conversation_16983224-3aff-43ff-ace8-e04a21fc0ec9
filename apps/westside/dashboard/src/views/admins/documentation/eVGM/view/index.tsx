import { Button } from "@/components/ui/button";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { Pen, Trash } from "lucide-react";
import { getEvgmDetails, cancelEvgm } from "@/services/admin/evgm";
import { useQuery } from "@tanstack/react-query";
import { Typography } from "@/components/typography";
import dayjs from "dayjs";
import { useState } from "react";
import { toast } from "sonner";

export default function EVGMDetailViewPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    data: fetchEvgmDetails,
    error: fetchEvgmDetailsError,
    isFetching: fetchEvgmDetailsFetching,
    refetch: fetchEvgmDetailsRefetch,
  } = useQuery({
    queryKey: ["getEvgmDetails", id],
    queryFn: () => getEvgmDetails(id as string),
  });

  const [confirmOpen, setConfirmOpen] = useState(false);
  const [selectedEquipmentId, setSelectedEquipmentId] = useState<string | null>(
    null
  );
  const [cancelling, setCancelling] = useState(false);

  const handleCancelEvgm = async (equipment_id: string) => {
    try {
      if (!equipment_id) return;
      setCancelling(true);

      const res = await cancelEvgm(equipment_id);

      if (res?.message?.status_code === 200) {
        toast.success("eVGM cancelled successfully");
        navigate("/dashboard/documentation/eVGM-workspace");
      } else {
        toast.error(
          res?.message?.message || "Failed to cancel eVGM. Please try again."
        );
      }
    } catch (error) {
      console.error("Error cancelling eVGM:", error);
      toast.error("An error occurred while cancelling eVGM. Please try again.");
    } finally {
      setCancelling(false);
      setConfirmOpen(false);
    }
  };

  console.log("fetchEvgmDetails", fetchEvgmDetails);
  const massUnit = (unit: string) => {
    switch (unit) {
      case "KGM":
        return "Kg";
      case "LBR":
        return "Lb";
      default:
        return unit;
    }
  };
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="pt-6 space-y-1">
          {/* <div className="text-md text-blue-600 cursor-pointer">
            Printer-Friendly
          </div> */}
          <div className="mt-2 text-md">
            INTTRA eVGM ID: {fetchEvgmDetails?.message?.data?.inttra_evgmid}
          </div>

          <div className="mt-2 text-md">
            eVGM State: {fetchEvgmDetails?.message?.data?.state}
          </div>
          {/* <div className="text-md">Version: 1</div> */}
          <div className="mt-2">
            <span className="font-semibold">eVGM Status:</span>{" "}
            {fetchEvgmDetails?.message?.data?.carrier_status ||
              (fetchEvgmDetails?.message?.data?.inttra_status && (
                <span className="bg-gray-200 text-gray-800 text-sm px-2 py-1 rounded">
                  {fetchEvgmDetails?.message?.data?.carrier_status
                    ? fetchEvgmDetails?.message?.data?.carrier_status
                    : fetchEvgmDetails?.message?.data?.inttra_status
                    ? fetchEvgmDetails?.message?.data?.inttra_status
                    : ""}
                </span>
              ))}{" "}
            {fetchEvgmDetails?.message?.data?.message_date_time_carrier ||
              (fetchEvgmDetails?.message?.data?.message_date_time_inttra && (
                <span className="text-md">
                  on{" "}
                  {fetchEvgmDetails?.message?.data?.message_date_time_carrier
                    ? dayjs(
                        fetchEvgmDetails?.message?.data
                          ?.message_date_time_carrier
                      ).format("MMM-DD-YYYY h:mm:ss A")
                    : fetchEvgmDetails?.message?.data?.message_date_time_inttra
                    ? dayjs(
                        fetchEvgmDetails?.message?.data
                          ?.message_date_time_inttra
                      ).format("MMM-DD-YYYY h:mm:ss A")
                    : ""}
                </span>
              ))}
          </div>
        </div>

        <div className="flex gap-2">
          {fetchEvgmDetails?.message?.data?.state !== "Amend" &&
            fetchEvgmDetails?.message?.data?.state !== "Cancel" && (
              <Button
                variant="secondary"
                onClick={() =>
                  navigate("/dashboard/documentation/amend-eVGM/" + id)
                }
                className="text-white"
              >
                <Pen /> Amend
              </Button>
            )}
          {fetchEvgmDetails?.message?.data?.state !== "Cancel" && (
            <Button
              variant="default"
              onClick={() => {
                setSelectedEquipmentId(
                  fetchEvgmDetails?.message?.data?.equipment_id
                ); // pass the equipment_id here dynamically
                setConfirmOpen(true);
              }}
              className="bg-orange-500 text-white hover:bg-orange-400 focus:ring-2 focus:ring-orange-500"
            >
              <Trash /> Cancel
            </Button>
          )}
          <button
            onClick={(e) => {
              e.preventDefault();
              navigate(-1);
            }}
            className=" mb-2 px-4 py-2 bg-gray-200 text-sm rounded hover:bg-gray-300 transition"
          >
            ← Back
          </button>
        </div>
      </div>

      {/* Parties Section */}
      <div>
        <h2 className="font-semibold text-lg mb-2">eVGM Parties</h2>
        <div className="overflow-x-auto mt-3">
          <table className="w-full border border-gray-300 text-md table-fixed">
            <thead className="bg-[#D3DAE7]">
              <tr>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  eVGM Submitter
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Responsible Party
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Authorized Party
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Carrier
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                {/* eVGM Submitter */}
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    INTTRA ID:
                    {fetchEvgmDetails?.message?.data?.evgm_shipper
                      ?.inttra_company_id ?? "--"}{" "}
                  </Typography>
                  <Typography>
                    Name:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_shipper
                      ?.shipper_name ?? "--"}{" "}
                  </Typography>
                </td>

                {/* Responsible Party */}
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    INTTRA ID:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_responsible_party
                      ?.inttra_company_id ?? "--"}
                  </Typography>
                  <Typography>
                    Name:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_responsible_party
                      ?.customer_name ?? "--"}
                  </Typography>

                  {(fetchEvgmDetails?.message?.data?.evgm_responsible_party
                    ?.email_id ||
                    fetchEvgmDetails?.message?.data?.evgm_responsible_party
                      ?.phone) && (
                    <div className="mt-2">
                      {fetchEvgmDetails?.message?.data?.evgm_responsible_party
                        ?.email_id && (
                        <Typography>
                          Email:{" "}
                          {
                            fetchEvgmDetails?.message?.data
                              ?.evgm_responsible_party?.email_id
                          }
                        </Typography>
                      )}
                      {fetchEvgmDetails?.message?.data?.evgm_responsible_party
                        ?.phone && (
                        <Typography>
                          Phone:{" "}
                          {
                            fetchEvgmDetails?.message?.data
                              ?.evgm_responsible_party?.phone
                          }
                        </Typography>
                      )}
                    </div>
                  )}
                </td>

                {/* Authorized Party */}
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  {fetchEvgmDetails?.message?.data?.evgm_authorized_party && (
                    <>
                      <Typography>
                        INTTRA ID:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_authorized_party
                          ?.inttra_company_id ?? "--"}
                      </Typography>
                      <Typography>
                        Name:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_authorized_party
                          ?.customer_name ?? "--"}
                      </Typography>

                      {(fetchEvgmDetails?.message?.data?.evgm_authorized_party
                        ?.email_id ||
                        fetchEvgmDetails?.message?.data?.evgm_authorized_party
                          ?.phone) && (
                        <div className="mt-2">
                          {fetchEvgmDetails?.message?.data
                            ?.evgm_authorized_party?.email_id && (
                            <Typography>
                              Email:{" "}
                              {
                                fetchEvgmDetails?.message?.data
                                  ?.evgm_authorized_party?.email_id
                              }
                            </Typography>
                          )}
                          {fetchEvgmDetails?.message?.data
                            ?.evgm_authorized_party?.phone && (
                            <Typography>
                              Phone:{" "}
                              {
                                fetchEvgmDetails?.message?.data
                                  ?.evgm_authorized_party?.phone
                              }
                            </Typography>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </td>

                {/* Carrier */}
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    INTTRA ID:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_carrier?.inttra_id ??
                      "--"}
                  </Typography>
                  <Typography>
                    Name:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_carrier
                      ?.partyname1 ?? "--"}
                  </Typography>
                </td>
              </tr>
            </tbody>
          </table>

          <table className="w-full border border-gray-300 text-md table-fixed">
            <thead className="bg-[#D3DAE7]">
              <tr>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Shipper
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Forwarder
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top">
                  Terminal Operator
                </th>
                <th className="w-1/4 border p-2 whitespace-pre-wrap break-words text-left align-top"></th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    INTTRA ID:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_shipper
                      ?.inttra_company_id ?? "--"}{" "}
                  </Typography>
                  <Typography>
                    Name:{" "}
                    {fetchEvgmDetails?.message?.data?.evgm_shipper
                      ?.shipper_name ?? "--"}
                  </Typography>

                  {(fetchEvgmDetails?.message?.data?.evgm_shipper?.email ||
                    fetchEvgmDetails?.message?.data?.evgm_shipper?.phone) && (
                    <div className="mt-2">
                      {fetchEvgmDetails?.message?.data?.evgm_shipper?.email && (
                        <Typography>
                          Email:{" "}
                          {fetchEvgmDetails?.message?.data?.evgm_shipper?.email}
                        </Typography>
                      )}

                      {fetchEvgmDetails?.message?.data?.evgm_shipper?.phone && (
                        <Typography>
                          Phone:{" "}
                          {fetchEvgmDetails?.message?.data?.evgm_shipper?.phone}
                        </Typography>
                      )}
                    </div>
                  )}
                </td>
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  {fetchEvgmDetails?.message?.data?.evgm_forwarder && (
                    <>
                      <Typography>
                        INTTRA ID:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_forwarder
                          ?.inttra_company_id ?? "--"}{" "}
                      </Typography>
                      <Typography>
                        Name:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_forwarder
                          ?.customer_name ?? "--"}
                      </Typography>

                      {(fetchEvgmDetails?.message?.data?.evgm_forwarder
                        ?.email_id ||
                        fetchEvgmDetails?.message?.data?.evgm_forwarder
                          ?.phone) && (
                        <div className="mt-2">
                          {fetchEvgmDetails?.message?.data?.evgm_forwarder
                            ?.email_id && (
                            <Typography>
                              Email:{" "}
                              {
                                fetchEvgmDetails?.message?.data?.evgm_forwarder
                                  ?.email_id
                              }
                            </Typography>
                          )}
                          {fetchEvgmDetails?.message?.data?.evgm_forwarder
                            ?.email_id && (
                            <Typography>
                              Email:{" "}
                              {
                                fetchEvgmDetails?.message?.data?.evgm_forwarder
                                  ?.email_id
                              }
                            </Typography>
                          )}
                          {fetchEvgmDetails?.message?.data?.evgm_forwarder
                            ?.email_id && (
                            <Typography>
                              Email:{" "}
                              {
                                fetchEvgmDetails?.message?.data?.evgm_forwarder
                                  ?.email_id
                              }
                            </Typography>
                          )}
                          {fetchEvgmDetails?.message?.data?.evgm_forwarder
                            ?.phone && (
                            <Typography>
                              Phone:{" "}
                              {
                                fetchEvgmDetails?.message?.data?.evgm_forwarder
                                  ?.phone
                              }
                            </Typography>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </td>
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top">
                  {fetchEvgmDetails?.message?.data?.evgm_terminal_operator && (
                    <>
                      <Typography>
                        INTTRA ID:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_terminal_operator
                          ?.inttra_company_id ?? "--"}{" "}
                      </Typography>
                      <Typography>
                        Name:{" "}
                        {fetchEvgmDetails?.message?.data?.evgm_terminal_operator
                          ?.customer_name ?? "--"}
                      </Typography>

                      {(fetchEvgmDetails?.message?.data?.evgm_terminal_operator
                        ?.email_id ||
                        fetchEvgmDetails?.message?.data?.evgm_terminal_operator
                          ?.phone) && (
                        <div className="mt-2">
                          {fetchEvgmDetails?.message?.data
                            ?.evgm_terminal_operator?.email_id && (
                            <Typography>
                              Email:{" "}
                              {
                                fetchEvgmDetails?.message?.data
                                  ?.evgm_terminal_operator?.email_id
                              }
                            </Typography>
                          )}
                          {fetchEvgmDetails?.message?.data
                            ?.evgm_terminal_operator?.phone && (
                            <Typography>
                              Phone:{" "}
                              {
                                fetchEvgmDetails?.message?.data
                                  ?.evgm_terminal_operator?.phone
                              }
                            </Typography>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </td>
                <td className="w-1/4 border p-2 whitespace-pre-wrap break-words align-top"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Container Details Section */}
      <div>
        <h2 className="font-semibold text-lg mb-2">Container Details</h2>
        <div className="overflow-x-auto mt-3">
          <table className="w-full border border-gray-300 text-md">
            <thead className="bg-[#D3DAE7]">
              <tr>
                <th className="w-1/5 border p-2 whitespace-pre-wrap break-words text-left ">
                  Container Details
                </th>
                <th className="w-1/5 border p-2 whitespace-pre-wrap break-words text-left ">
                  Verified Gross Mass
                </th>
                <th className="w-1/5 border p-2 whitespace-pre-wrap break-words text-left ">
                  References
                </th>
                <th className="w-1/5 border p-2 whitespace-pre-wrap break-words text-left">
                  Approval
                </th>
                <th className="w-1/5 border p-2 whitespace-pre-wrap break-words text-left">
                  Weighing Details
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="w-1/5 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography className="font-semibold">
                    {fetchEvgmDetails?.message?.data?.equipment_name ?? "--"}
                  </Typography>
                  <Typography>(Carrier Supplied)</Typography>
                  <Typography>Seal(s)</Typography>
                </td>
                <td className=" w-1/5 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography className="font-semibold">
                    {fetchEvgmDetails?.message?.data?.verified_gross_mass
                      ? fetchEvgmDetails.message.data.verified_gross_mass
                      : "--"}{" "}
                    {""}
                    {massUnit(
                      fetchEvgmDetails?.message?.data?.verified_gross_mass_unit
                    ) ?? ""}
                  </Typography>
                  <Typography>
                    Determination Date/Time: <br />
                    {fetchEvgmDetails?.message?.data
                      ?.weight_determination_date_time
                      ? dayjs(
                          fetchEvgmDetails?.message?.data
                            ?.weight_determination_date_time
                        ).format("MMM-DD-YYYY hh:mm A")
                      : "--"}
                  </Typography>
                </td>
                <td className="w-1/5 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    Submitter’s Reference: <br />{" "}
                    {fetchEvgmDetails?.message?.data?.submitter_reference ??
                      "--"}
                  </Typography>
                  <Typography>
                    Carrier Booking Number: <br />
                    {fetchEvgmDetails?.message?.data?.booking_number ?? "--"}
                  </Typography>
                </td>
                <td className="w-1/5 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>
                    Signature:{" "}
                    <span className="font-semibold">
                      {fetchEvgmDetails?.message?.data?.approval_signature ??
                        "--"}
                    </span>
                  </Typography>
                  <Typography>
                    Date/Time:{" "}
                    <span className="font-semibold">
                      {fetchEvgmDetails?.message?.data?.approval_datetime
                        ? dayjs(
                            fetchEvgmDetails?.message?.data?.approval_datetime
                          ).format("MMM-DD-YYYY hh:mm A")
                        : "--"}
                    </span>
                  </Typography>
                </td>
                <td className="w-1/5 border p-2 whitespace-pre-wrap break-words align-top">
                  <Typography>--</Typography>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {confirmOpen && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-[400px]">
            <h2 className="text-lg font-semibold mb-4">Confirm Cancel</h2>
            <p className="mb-6">Are you sure you want to cancel this eVGM?</p>
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => setConfirmOpen(false)}
                disabled={cancelling}
              >
                No
              </Button>
              <Button
                type="button"
                className="bg-red-500 text-white hover:bg-red-600"
                onClick={() =>
                  selectedEquipmentId && handleCancelEvgm(selectedEquipmentId)
                }
                disabled={cancelling}
              >
                {cancelling ? "Cancelling..." : "Yes, Cancel"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
