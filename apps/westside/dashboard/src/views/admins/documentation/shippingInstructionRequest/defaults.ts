import { ShippingInstructionDetailsType } from "./../../../../types/shippingInstructionConfirmation";
import {
  CreateShippingInstructionInitialDataType,
  MainCarriageSIType,
  SIEquipmentTypes,
} from "@/types/shippingInstructions";
import { SIRequestFormType } from "./schema";
import dayjs from "dayjs";

function extractCountryCodes(main_carriage: MainCarriageSIType[]): string[] {
  if (!main_carriage || main_carriage.length <= 1) return [];

  const result: string[] = [];

  for (let i = 0; i < main_carriage.length - 1; i++) {
    const dischargeCode = main_carriage[i].port_of_discharge?.country_code;
    if (dischargeCode) {
      result.push(dischargeCode);
    }
  }

  return result.filter(Boolean);
}

export const getSIRequestPopulatedData = (
  bookingData: CreateShippingInstructionInitialDataType,
  equipmentData?: SIEquipmentTypes[]
): SIRequestFormType => {
  const containerNumber =
    bookingData?.containers?.reduce((acc, item) => {
      return acc + Number(item?.number_of_containers);
    }, 0) || 1;

  const containerDataMapped = Array.from({ length: containerNumber })?.map(
    (_, idx) => {
      return {
        containerNumber: "",
        containerType: {
          name: "",
          type: "",
        },
        containerSupplier: "",
        containerTareWeight: {
          weight: "",
          unit: "KG",
        },
        woodDeclaration: "",
        carrierSealNumber: "",
        shipperSealNumber: "",
        // cargo details.
        cargoPackageCount: "",
        cargoPackageType: {
          xmlCode: "",
          shortName: "",
          name: "",
        },
        cargoPackageBLas: "",
        hsCode: "",
        scheduleBNumber: "",
        cargoDescription: "",
        ncmCodes: "",
        markAndNumbers: "",
        cusCodes: "",
        cargoGrossWeight: "",
        cargoGrossWeightUnit: "KG",
        cargoGrossVolume: "",
        cargoGrossVolumeUnit: "CBM",
      };
    }
  );

  // Map equipmentData (if present)
  const equipmentDataMapped =
    equipmentData?.map((item) => {
      let cargoData;
      if (item?.cargo?.length) {
        cargoData = item?.cargo[0];
      }
      return {
        containerNumber: item?.equipment_name || "",
        containerType: {
          name: item?.container_type_id || "",
          type: item?.description || "",
        },
        containerSupplier: item?.supplier_type || "",
        containerTareWeight: {
          weight: item?.tare_weight?.toString() || "",
          unit: "KG",
        },
        woodDeclaration: item?.wood_declaration || "",
        carrierSealNumber: item?.carrier_seal_number || "",
        shipperSealNumber: item?.shipper_seal_number || "",
        cargoPackageCount: cargoData?.package_count || "",
        cargoPackageType: {
          xmlCode: cargoData?.package_counttype_outermost?.xmlcode || "",
          shortName: cargoData?.package_counttype_outermost?.short_name || "",
          name: cargoData?.package_counttype_outermost?.name || "",
        },
        cargoPackageBLas: cargoData?.print_on_bl_as || "",
        hsCode: cargoData?.hs_code || "",
        scheduleBNumber: cargoData?.schedule_b_number || "",
        cargoDescription: cargoData?.cargo_description || "",
        ncmCodes: cargoData?.ncm_codes || "",
        markAndNumbers: "",
        cusCodes: cargoData?.cus_code || "",
        cargoGrossWeight: item?.gross_weight?.toString() || "",
        cargoGrossWeightUnit: cargoData?.weight_type || "KG",
        cargoGrossVolume: cargoData?.gross_volume?.toString() || "",
        cargoGrossVolumeUnit: cargoData?.gross_volume_unit || "CBM",
      };
    }) || [];

  let finalResult = [];

  if (equipmentDataMapped.length >= containerNumber) {
    finalResult = equipmentDataMapped;
  } else {
    const remaining = containerNumber - equipmentDataMapped?.length;
    const fallbackData = containerDataMapped?.slice(-remaining);
    finalResult = [...equipmentDataMapped, ...fallbackData];
  }

  return {
    bookingNumber: bookingData?.inttra_reference || "",
    creationDate: bookingData?.creationdate || "",
    bookingRequestId: bookingData?.name || "",

    // top parties general details.
    shipper: bookingData?.shipper?.name || "",
    customShipperName: bookingData?.shipper?.shipper_name || "",
    shipperEmail: bookingData?.shipper?.email || "",
    shipperMobileNumber: bookingData?.shipper?.phone || "",
    shipperAddress: bookingData?.shipper?.custom_address || "",
    forwarder: bookingData?.forwarder || "",
    forwarderName: "",
    forwarderAddress: "",

    // bottom partier general details.
    consignee: bookingData?.consignee?.name || "",
    consigneeName: bookingData?.consignee?.customer_name || "",
    consigneeAddress: bookingData?.consignee?.customer_address || "",
    notifyParty: bookingData?.notify_parties?.name || "",
    notifyPartyName: bookingData?.notify_parties?.customer_name || "",
    notifyPartyAddress: bookingData?.notify_parties?.customer_address || "",

    // references.
    shipperReferenceNumber: bookingData?.shippers_reference_numbers || "",
    forwarderReferenceNumber: bookingData?.forwarders_reference_numbers || "",
    transactionReferenceNumber: "",
    uniqueConsignmentReferenceNumber: "",
    purchaseOrderNumber: bookingData?.purchase_order_numbers || "",
    contractReferenceNumber:
      bookingData?.contract_party_reference_numbers || "",
    BLReferenceNumber: bookingData?.bl_reference_numbers || "",
    exportersReferenceNumbers: "",
    consigneeOrderNumber: "",
    invoiceReferenceNumber: "",
    letterOfCreditReferenceNumber: "",
    letterOfCreditIssueDate: "",
    letterOfCreditExpiryDate: "",
    customsHouseBrokerReferenceNumber: "",
    govtReferenceOrFMCNumber: "",
    exportLicenseNumber: "",
    exportLicenseIssueDate: "",
    exportLicenseExpiryDate: "",

    // carrier Tab section.
    carrier: bookingData?.carrier?.name || "",
    carrierBookingNumber: bookingData?.carrier_booking_number || "",
    UCACarrier: "",
    UCAEmail: "",
    UCACarrierBookingNumber: "",

    // additional Parties.
    additionalNotifyParty1: bookingData?.notify_party_1?.name || "",
    additionalNotifyParty1Name:
      bookingData?.notify_party_1?.customer_name || "",
    additionalNotifyParty1Address:
      bookingData?.notify_party_1?.customer_address || "",
    additionalNotifyParty2: bookingData?.notify_party_2?.name || "",
    additionalNotifyParty2Name:
      bookingData?.notify_party_2?.customer_name || "",
    additionalNotifyParty2Address:
      bookingData?.notify_party_2?.customer_address || "",
    contractParty: "",
    contractPartyName: "",
    contractPartyAddress: "",
    freightPayer: "",
    freightPayerAddress: "",
    supplier: "",
    supplierAddress: "",
    consolidator: "",
    consolidatorAddress: "",
    importer: "",
    importerAddress: "",
    warehouseKeeper: "",
    warehouseKeeperAddress: "",

    // transport.
    vessel: bookingData?.vessel || "",
    voyage: bookingData?.main_carriage?.length
      ? bookingData?.main_carriage[0]?.voyage
      : "",
    IMONumber: "",
    placeOfCarrierReceipt: {
      name: bookingData?.place_of_carrier_receipt?.name || "",
      location: `${bookingData?.place_of_carrier_receipt?.location_name}, ${
        bookingData?.place_of_carrier_receipt?.sub_division
          ? `${bookingData?.place_of_carrier_receipt?.sub_division},`
          : ""
      } ${bookingData?.place_of_carrier_receipt?.country} (${
        bookingData?.place_of_carrier_receipt?.locode
      })`,
      locode: bookingData?.place_of_carrier_receipt?.locode || "",
    },
    placeOfCarrierReceiptBLas: `${
      bookingData?.place_of_carrier_receipt?.location_name
    }, ${
      bookingData?.place_of_carrier_receipt?.sub_division
        ? `${bookingData?.place_of_carrier_receipt?.sub_division},`
        : ""
    } ${bookingData?.place_of_carrier_receipt?.country}`,
    portOfLoad: {
      name: bookingData?.port_of_load?.name || "",
      location: `${bookingData?.port_of_load?.location_name}, ${
        bookingData?.port_of_load?.sub_division
          ? `${bookingData?.port_of_load?.sub_division},`
          : ""
      } ${bookingData?.port_of_load?.country} (${
        bookingData?.port_of_load?.locode
      })`,
      locode: bookingData?.port_of_load?.locode || "",
    },
    portOfLoadBLas: `${bookingData?.port_of_load?.location_name}, ${
      bookingData?.port_of_load?.sub_division
        ? `${bookingData?.port_of_load?.sub_division},`
        : ""
    } ${bookingData?.port_of_load?.country}`,
    portOfDischarge: {
      name: bookingData?.port_of_discharge?.name || "",
      location: `${bookingData?.port_of_discharge?.location_name}, ${
        bookingData?.port_of_discharge?.sub_division
          ? `${bookingData?.port_of_discharge?.sub_division},`
          : ""
      } ${bookingData?.port_of_discharge?.country} (${
        bookingData?.port_of_discharge?.locode
      })`,
      locode: bookingData?.port_of_discharge?.locode || "",
    },
    portOfDischargeBLas: `${bookingData?.port_of_discharge?.location_name}, ${
      bookingData?.port_of_discharge?.sub_division
        ? `${bookingData?.port_of_discharge?.sub_division},`
        : ""
    } ${bookingData?.port_of_discharge?.country}`,
    placeOfCarrierDelivery: {
      name: bookingData?.place_of_carrier_delivery?.name || "",
      location: `${bookingData?.place_of_carrier_delivery?.location_name}, ${
        bookingData?.place_of_carrier_delivery?.sub_division
          ? `${bookingData?.place_of_carrier_delivery?.sub_division},`
          : ""
      } ${bookingData?.place_of_carrier_delivery?.country} (${
        bookingData?.place_of_carrier_delivery?.locode
      })`,
      locode: bookingData?.place_of_carrier_delivery?.locode || "",
    },
    placeOfCarrierDeliveryBLas: `${
      bookingData?.place_of_carrier_delivery?.location_name
    }, ${
      bookingData?.place_of_carrier_delivery?.sub_division
        ? `${bookingData?.place_of_carrier_delivery?.sub_division},`
        : ""
    } ${bookingData?.place_of_carrier_delivery?.country}`,
    moveType: bookingData?.move_type || "",
    shipmentType: "",

    // Customs Compliance.
    shipperTaxId: "",
    consigneeTaxId: "",
    notifyPartyTaxId: "",
    printTaxIdOnBillOfLaiding: "1",

    // ICS2 Summary Declaration.
    goodsDeliveriedCountry: "",
    typeOfDeclaration: "",
    shipmentHouseBillStatus: "",
    pcin: "",
    csn: "",
    acidNumber: "",
    eori: "",
    // House bill modal
    housebillOfLadingNumber: "",
    placeOfAcceptance: {
      name: bookingData?.port_of_load?.name || "",
      location: `${bookingData?.port_of_load?.location_name}, ${
        bookingData?.port_of_load?.sub_division
          ? `${bookingData?.port_of_load?.sub_division},`
          : ""
      } ${bookingData?.port_of_load?.country} (${
        bookingData?.port_of_load?.locode
      })`,
      locode: bookingData?.port_of_load?.locode || "",
    },
    placeOfFinalDelivery: {
      name: bookingData?.port_of_discharge?.name || "",
      location: `${bookingData?.port_of_discharge?.location_name}, ${
        bookingData?.port_of_discharge?.sub_division
          ? `${bookingData?.port_of_discharge?.sub_division},`
          : ""
      } ${bookingData?.port_of_discharge?.country} (${
        bookingData?.port_of_discharge?.locode
      })`,
      locode: bookingData?.port_of_discharge?.locode || "",
    },
    firstCountry: bookingData?.port_of_load?.country || "",
    countriesVisitedInBetween: extractCountryCodes(
      bookingData?.main_carriage
    ) || [""],
    lastCountry: bookingData?.port_of_discharge?.country || "",
    methodOfPayment: "",
    shipperPartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },
    consigneePartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },
    notifyPartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },

    // particulars.
    isShippingSingleCargoOnly: true,
    // container details.
    particularContainers: finalResult || [],

    // Control totals.
    totalNumberOfContainers: "",
    totalNumberOfPackages: "",
    totalShipmentWeight: "",
    totalShipmentVolume: "",

    // Shippers Declared value.
    currencyType: {
      currencyCode: "",
      currencyName: "",
      currencyType: "",
      name: "",
    },
    shippersDeclaredValue: "",

    // Freight Charges.
    typeOfFreightCharge: "allCharges",
    freightTerm: "",
    payer: "",
    paymentLocation: {
      name: "",
      location: "",
      locode: "",
    },
    individualCharges: [
      {
        chargeType: "Basic Freight",
        freightTerm: "",
        payer: "",
        paymentLocation: {
          name: "",
          location: "",
          locode: "",
        },
      },
    ],

    // Documentation.
    clauses: [""],
    userDefinedClauses: "",
    BLReleaseOffice: {
      name: "",
      location: "",
      locode: "",
    },
    printBLReleaseOffice: "",
    requestedDateOfIssue: "",

    // BL Print Instructions.
    blPrintingInstructionType: "express",
    freighted: "",
    unfreighted: "",
    nonNegotiableFreighted: "",
    nonNegotiableUnfreighted: "",
    houseBillNumber: "",
    blComments: "",

    // Notificarions.
    sIRequestorEmails: "",
    partnerNotificationEmails: "",
  };
};

export const getSIRequestAmendPopulateData = (
  amendData: ShippingInstructionDetailsType
) => {
  function transformFreightCharges(
    freight_charges: ShippingInstructionDetailsType["freight_charges"]
  ) {
    const initial = {
      typeOfFreightCharge: "",
      freightTerm: "",
      payer: "",
      paymentLocation: {
        name: "",
        location: "",
        locode: "",
      },
      individualCharges: [
        {
          chargeType: "Basic Freight",
          freightTerm: "",
          payer: "",
          paymentLocation: {
            name: "",
            location: "",
            locode: "",
          },
        },
      ],
    };

    if (!freight_charges || freight_charges?.length === 0) return initial;

    const first = freight_charges[0];

    if (first?.type === "All Charges") {
      return {
        typeOfFreightCharge: "allCharges",
        freightTerm: first.freight_term || "",
        payer: first.payer || "",
        paymentLocation: {
          name: first.payment_location_data?.name || "",
          location: first.payment_location_data?.location_name || "",
          locode: first.payment_location_data?.locode || "",
        },
        individualCharges: [
          {
            chargeType: "Basic Freight",
            freightTerm: "",
            payer: "",
            paymentLocation: {
              name: "",
              location: "",
              locode: "",
            },
          },
        ],
      };
    }

    if (first?.type === "Individual Charges") {
      return {
        ...initial,
        typeOfFreightCharge: "individualCharges",
        individualCharges: freight_charges.map((charge) => ({
          chargeType: charge.charge_type || "",
          freightTerm: charge.freight_term || "",
          payer: charge.payer || "",
          paymentLocation: {
            name: charge.payment_location_data?.name || "",
            location: charge.payment_location_data?.location_name || "",
            locode: charge.payment_location_data?.locode || "",
          },
        })),
      };
    }

    return initial;
  }
  const shipper = amendData?.ics2_parties?.find(
    (item) => item?.party_type === "Shipper"
  );
  const consignee = amendData?.ics2_parties?.find(
    (item) => item?.party_type === "Consignee"
  );

  const amendPayload = {
    bookingNumber: amendData?.bookingnumber || "",
    creationDate: amendData?.creationdate || "",
    bookingRequestId: amendData?.booking_request_id || "",

    // top parties general details.
    shipper: amendData?.shipper_data?.name || "",
    customShipperName: amendData?.shipper_data?.shipper_name || "",
    shipperEmail: amendData?.shipper_data?.email || "",
    shipperMobileNumber: amendData?.shipper_data?.phone || "",
    shipperAddress: amendData?.shipper_data?.custom_address || "",
    forwarder: amendData?.forwarder || "",
    forwarderName: "",
    forwarderAddress: amendData?.forwarder_address || "",

    // bottom partier general details.
    consignee: amendData?.consignee_data?.name || "",
    consigneeName: amendData?.consignee_data?.customer_name || "",
    consigneeAddress: amendData?.consignee_data?.customer_address || "",
    notifyParty: amendData?.notify_party_data?.name || "",
    notifyPartyName: amendData?.notify_party_data?.customer_name || "",
    notifyPartyAddress: amendData?.notify_party_data?.customer_address || "",

    // references.
    shipperReferenceNumber: amendData?.shipper_reference_number || "",
    forwarderReferenceNumber: amendData?.forwarder_reference_number || "",
    transactionReferenceNumber: amendData?.transaction_number || "",
    uniqueConsignmentReferenceNumber:
      amendData?.unique_consignment_reference || "",
    purchaseOrderNumber: amendData?.purchase_order_number || "",
    contractReferenceNumber: amendData?.contract_reference_number || "",
    BLReferenceNumber: amendData?.bl_reference_number || "",
    exportersReferenceNumbers: amendData?.exporter_reference_number || "",
    consigneeOrderNumber: amendData?.consignee_order_number || "",
    invoiceReferenceNumber: amendData?.invoice_reference_number || "",
    letterOfCreditReferenceNumber: amendData?.letter_of_credit_reference || "",
    letterOfCreditIssueDate: amendData?.lcr_issue_date
      ? dayjs(amendData?.lcr_issue_date)?.format("YYYY-MM-DD")
      : "",
    letterOfCreditExpiryDate: amendData?.lcr_expiry_date
      ? dayjs(amendData?.lcr_expiry_date)?.format("YYYY-MM-DD")
      : "",
    customsHouseBrokerReferenceNumber:
      amendData?.customs_house_broker_reference || "",
    govtReferenceOrFMCNumber:
      amendData?.government_reference_or_fmc_number || "",
    exportLicenseNumber: amendData?.export_license_number || "",
    exportLicenseIssueDate: amendData?.eln_issue_date
      ? dayjs(amendData?.eln_issue_date)?.format("YYYY-MM-DD")
      : "",
    exportLicenseExpiryDate: amendData?.eln_expiry_date
      ? dayjs(amendData?.eln_expiry_date)?.format("YYYY-MM-DD")
      : "",

    // carrier Tab section.
    carrier: amendData?.carrier_data?.name || "",
    carrierBookingNumber: amendData?.carrier_booking_number || "",
    UCACarrier: "",
    UCAEmail: amendData?.carrier_email || "",
    UCACarrierBookingNumber: "",

    // additional Parties.
    additionalNotifyParty1:
      amendData?.additional_notify_party_1_data?.name || "",
    additionalNotifyParty1Name:
      amendData?.additional_notify_party_1_data?.customer_name || "",
    additionalNotifyParty1Address:
      amendData?.additional_notify_party_1_data?.customer_address || "",
    additionalNotifyParty2:
      amendData?.additional_notify_party_2_data?.name || "",
    additionalNotifyParty2Name:
      amendData?.additional_notify_party_2_data?.customer_name || "",
    additionalNotifyParty2Address:
      amendData?.additional_notify_party_2_data?.customer_address || "",
    contractParty: "",
    contractPartyName: "",
    contractPartyAddress: "",
    freightPayer: "",
    freightPayerAddress: "",
    supplier: "",
    supplierAddress: "",
    consolidator: "",
    consolidatorAddress: "",
    importer: "",
    importerAddress: "",
    warehouseKeeper: "",
    warehouseKeeperAddress: "",

    // transport.
    vessel: amendData?.vessel || "",
    voyage: amendData?.voyage || "",
    IMONumber: amendData?.imo_number || "",
    placeOfCarrierReceipt: {
      name: amendData?.origin_place_of_carrier_receipt_data?.name || "",
      location: `${
        amendData?.origin_place_of_carrier_receipt_data?.location_name
      }, ${
        amendData?.origin_place_of_carrier_receipt_data?.sub_division
          ? `${amendData?.origin_place_of_carrier_receipt_data?.sub_division},`
          : ""
      } ${amendData?.origin_place_of_carrier_receipt_data?.country} (${
        amendData?.origin_place_of_carrier_receipt_data?.locode
      })`,
      locode: amendData?.origin_place_of_carrier_receipt_data?.locode || "",
    },
    placeOfCarrierReceiptBLas: `${
      amendData?.origin_place_of_carrier_receipt_data?.location_name
    }, ${
      amendData?.origin_place_of_carrier_receipt_data?.sub_division
        ? `${amendData?.origin_place_of_carrier_receipt_data?.sub_division},`
        : ""
    } ${amendData?.origin_place_of_carrier_receipt_data?.country}`,
    portOfLoad: {
      name: amendData?.port_of_load_data?.name || "",
      location: `${amendData?.port_of_load_data?.location_name}, ${
        amendData?.port_of_load_data?.sub_division
          ? `${amendData?.port_of_load_data?.sub_division},`
          : ""
      } ${amendData?.port_of_load_data?.country} (${
        amendData?.port_of_load_data?.locode
      })`,
      locode: amendData?.port_of_load_data?.locode || "",
    },
    portOfLoadBLas: `${amendData?.port_of_load_data?.location_name}, ${
      amendData?.port_of_load_data?.sub_division
        ? `${amendData?.port_of_load_data?.sub_division},`
        : ""
    } ${amendData?.port_of_load_data?.country}`,
    portOfDischarge: {
      name: amendData?.port_of_discharge_data?.name || "",
      location: `${amendData?.port_of_discharge_data?.location_name}, ${
        amendData?.port_of_discharge_data?.sub_division
          ? `${amendData?.port_of_discharge_data?.sub_division},`
          : ""
      } ${amendData?.port_of_discharge_data?.country} (${
        amendData?.port_of_discharge_data?.locode
      })`,
      locode: amendData?.port_of_discharge_data?.locode || "",
    },
    portOfDischargeBLas: `${
      amendData?.port_of_discharge_data?.location_name
    }, ${
      amendData?.port_of_discharge_data?.sub_division
        ? `${amendData?.port_of_discharge_data?.sub_division},`
        : ""
    } ${amendData?.port_of_discharge_data?.country}`,
    placeOfCarrierDelivery: {
      name: amendData?.destination_place_of_carrier_delivery_data?.name || "",
      location: amendData?.destination_place_of_carrier_delivery_data?.name
        ? `${
            amendData?.destination_place_of_carrier_delivery_data?.location_name
          }, ${
            amendData?.destination_place_of_carrier_delivery_data?.sub_division
              ? `${amendData?.destination_place_of_carrier_delivery_data?.sub_division},`
              : ""
          } ${
            amendData?.destination_place_of_carrier_delivery_data?.country
          } (${amendData?.destination_place_of_carrier_delivery_data?.locode})`
        : "",
      locode:
        amendData?.destination_place_of_carrier_delivery_data?.locode || "",
    },
    placeOfCarrierDeliveryBLas: amendData
      ?.destination_place_of_carrier_delivery_data?.name
      ? `${
          amendData?.destination_place_of_carrier_delivery_data?.location_name
        }, ${
          amendData?.destination_place_of_carrier_delivery_data?.sub_division
            ? `${amendData?.destination_place_of_carrier_delivery_data?.sub_division},`
            : ""
        } ${amendData?.destination_place_of_carrier_delivery_data?.country}`
      : "",
    moveType: amendData?.move_type || "",
    shipmentType: amendData?.shipment_type || "",

    // Customs Compliance.
    shipperTaxId: amendData?.shipper_tax_id || "",
    consigneeTaxId: amendData?.consignee_tax_id || "",
    notifyPartyTaxId: amendData?.notify_party_tax_id || "",
    printTaxIdOnBillOfLaiding: "1",

    // ICS2 Summary Declaration.
    goodsDeliveriedCountry:
      amendData?.are_the_goods_going_to_be_delivered_to_these_countries || "",
    typeOfDeclaration:
      amendData?.do_you_prefer_the_carrier_to_declare_the_entry_summary_ens ||
      "",
    shipmentHouseBillStatus:
      amendData?.have_house_bills_been_issued_for_this_shipment || "",
    pcin: amendData?.pcin || "",
    csn: amendData?.csn || "",
    acidNumber: amendData?.acid_number_mcin || "",
    eori: amendData?.eori || "",

    // House bill modal
    housebillOfLadingNumber: amendData?.house_bill_number || "",
    placeOfAcceptance: {
      name: amendData?.place_of_acceptance_origin_of_goods_data?.name || "",
      location: `${
        amendData?.place_of_acceptance_origin_of_goods_data?.location_name
      }, ${
        amendData?.place_of_acceptance_origin_of_goods_data?.sub_division
          ? `${amendData?.place_of_acceptance_origin_of_goods_data?.sub_division},`
          : ""
      } ${amendData?.place_of_acceptance_origin_of_goods_data?.country} (${
        amendData?.place_of_acceptance_origin_of_goods_data?.locode
      })`,
      locode: amendData?.place_of_acceptance_origin_of_goods_data?.locode || "",
    },
    placeOfFinalDelivery: {
      name: amendData?.place_of_final_delivery_data?.name || "",
      location: `${amendData?.place_of_final_delivery_data?.location_name}, ${
        amendData?.place_of_final_delivery_data?.sub_division
          ? `${amendData?.place_of_final_delivery_data?.sub_division},`
          : ""
      } ${amendData?.place_of_final_delivery_data?.country} (${
        amendData?.place_of_final_delivery_data?.locode
      })`,
      locode: amendData?.place_of_final_delivery_data?.locode || "",
    },
    firstCountry:
      amendData?.place_of_acceptance_origin_of_goods_data?.country || "",
    countriesVisitedInBetween: amendData?.countries_visited_in_between?.split(
      "-"
    ) || [""],
    lastCountry: amendData?.place_of_final_delivery_data?.country || "",
    methodOfPayment: amendData?.method_of_payment || "",
    shipperPartyInvolved: {
      actualPartyInformationSameAs: shipper?.party_type || "",
      partyId: shipper?.party_id || "",
      actualParty: shipper?.party_name || "",
      streetNumber: shipper?.street_number || "",
      poBOX: shipper?.po_box || "",
      streetName: shipper?.street_name || "",
      contactName: shipper?.contact_name || "",
      state: shipper?.state || "",
      city: shipper?.city || "",
      country: shipper?.country || "",
      postalCode: shipper?.postal_code || "",
      taxID: shipper?.tax_id_eori || "",
      eori: "",
    },
    consigneePartyInvolved: {
      actualPartyInformationSameAs: consignee?.party_type || "",
      partyId: consignee?.party_id || "",
      actualParty: consignee?.party_name || "",
      streetNumber: consignee?.street_number || "",
      poBOX: consignee?.po_box || "",
      streetName: consignee?.street_name || "",
      contactName: consignee?.contact_name || "",
      state: consignee?.state || "",
      city: consignee?.city || "",
      country: consignee?.country || "",
      postalCode: consignee?.postal_code || "",
      taxID: consignee?.tax_id_eori || "",
      eori: "",
    },
    notifyPartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },

    // particulars.
    isShippingSingleCargoOnly: true,
    particularContainers: amendData?.equipment_list?.length
      ? amendData?.equipment_list?.map((item) => {
          let cargoData;
          if (item?.cargo?.length) {
            cargoData = item?.cargo[0];
          }
          return {
            containerNumber: item?.equipment_name || "",
            containerType: {
              name: item?.container_type_id_data?.name?.toString() || "",
              type: item?.container_type_id_data?.shortdescription || "",
            },
            containerSupplier: item?.supplier_type || "",
            containerTareWeight: {
              weight: item?.tare_weight?.toString() || "",
              unit: "KG",
            },
            woodDeclaration: item?.wood_declaration || "",
            carrierSealNumber: item?.carrier_seal_number || "",
            shipperSealNumber: item?.shipper_seal_number || "",
            // cargo details.
            cargoPackageCount: cargoData?.package_count || "",
            cargoPackageType: {
              xmlCode:
                cargoData?.package_counttype_outermost_data?.xmlcode || "",
              shortName:
                cargoData?.package_counttype_outermost_data?.short_name || "",
              name: cargoData?.package_counttype_outermost_data?.name || "",
            },
            cargoPackageBLas: cargoData?.print_on_bl_as || "",
            hsCode: cargoData?.hs_code || "",
            scheduleBNumber: cargoData?.schedule_b_number || "",
            cargoDescription: cargoData?.cargo_description || "",
            ncmCodes: cargoData?.ncm_codes || "",
            markAndNumbers: "",
            cusCodes: cargoData?.cus_code || "",
            cargoGrossWeight: cargoData?.net_weight
              ? cargoData?.net_weight?.toString()
              : item?.gross_weight
              ? item?.gross_weight?.toString()
              : item?.cargo_weight?.toString() || "",
            cargoGrossWeightUnit: cargoData?.net_weight_unit || "KG",
            cargoGrossVolume: cargoData?.gross_volume?.toString() || "",
            cargoGrossVolumeUnit: cargoData?.gross_volume_unit || "CBM",
          };
        })
      : [
          {
            // container details.
            containerNumber: "",
            containerType: {
              name: "",
              type: "",
            },
            containerSupplier: "",
            containerTareWeight: {
              weight: "",
              unit: "KG",
            },
            woodDeclaration: "",
            carrierSealNumber: "",
            shipperSealNumber: "",
            containerTerminalOperatorSealNumber: "",
            containerVeterinarySealNumber: "",
            containerCustomsSealNumber: "",
            containerCustomsReleaseCode: "",
            containerStuffingLocation: "",
            containerComments: "",
            // cargo details.
            cargoPackageCount: "",
            cargoPackageType: {
              xmlCode: "",
              shortName: "",
              name: "",
            },
            cargoPackageBLas: "",
            hsCode: "",
            scheduleBNumber: "",
            cargoDescription: "",
            ncmCodes: "",
            markAndNumbers: "",
            cusCodes: "",
            cargoGrossWeight: "",
            cargoGrossWeightUnit: "KG",
            cargoGrossVolume: "",
            cargoGrossVolumeUnit: "CBM",
          },
        ],

    // Control totals.
    totalNumberOfContainers: amendData?.total_number_of_containers || "",
    totalNumberOfPackages: amendData?.total_number_of_packages || "",
    totalShipmentWeight: amendData?.total_shipment_weight || "",
    totalShipmentVolume: amendData?.total_shipment_volume || "",

    // Shippers Declared value.
    currencyType: {
      currencyCode: amendData?.currency_type_data?.alphabetic_code || "",
      currencyType: amendData?.currency_type_data?.currency_type || "",
      currencyName: amendData?.currency_type_data?.country || "",
      name: amendData?.currency_type_data?.name || "",
    },
    shippersDeclaredValue: amendData?.shippers__declared_value || "",

    // Freight Charges.
    ...transformFreightCharges(amendData?.freight_charges),

    // Documentation.
    clauses: [""],
    userDefinedClauses: "",
    BLReleaseOffice: {
      name: amendData?.bl_release_office__location_data?.name || "",
      location: `${
        amendData?.bl_release_office__location_data?.location_name
      }, ${
        amendData?.bl_release_office__location_data?.sub_division
          ? `${amendData?.bl_release_office__location_data?.sub_division},`
          : ""
      } ${amendData?.bl_release_office__location_data?.country} (${
        amendData?.bl_release_office__location_data?.locode
      })`,
      locode: amendData?.bl_release_office__location_data?.locode || "",
    },
    printBLReleaseOffice: amendData?.print_on_bl_aslocation_data?.location_name
      ? `${amendData?.print_on_bl_aslocation_data?.location_name}, ${
          amendData?.print_on_bl_aslocation_data?.sub_division
            ? `${amendData?.print_on_bl_aslocation_data?.sub_division},`
            : ""
        } ${amendData?.print_on_bl_aslocation_data?.country} (${
          amendData?.print_on_bl_aslocation_data?.locode
        })`
      : "",
    requestedDateOfIssue: amendData?.requested_date_of_issue
      ? dayjs(amendData?.requested_date_of_issue)?.format("YYYY-MM-DD")
      : "",

    // BL Print Instructions.
    blPrintingInstructionType: (amendData?.bill_type === "Seaway/Express"
      ? "express"
      : amendData?.bill_type === "Original"
      ? "original"
      : amendData?.bill_type === "House Bill"
      ? "houseBill"
      : "express") as "express" | "original" | "houseBill",
    freighted: amendData?.freightedno_of_documents || "",
    unfreighted: amendData?.unfreightedno_of_documents || "",
    nonNegotiableFreighted:
      amendData?.non_negotiable_freightedno_of_copies || "",
    nonNegotiableUnfreighted:
      amendData?.non_negotiable_unfreighted_no_of_copies || "",
    houseBillNumber: amendData?.house_bill_number || "",
    blComments: amendData?.bl_comments || "",

    // Notificarions.
    sIRequestorEmails: amendData?.si_requestor_emails || "",
    partnerNotificationEmails: amendData?.partner_notification_emails || "",
  };

  return amendPayload;
};

export const generateSubmitPayload = (formData: SIRequestFormType): any => {
  return {
    bookingnumber: formData?.bookingNumber,
    creationdate: formData?.creationDate,
    booking_request_id: formData?.bookingRequestId,

    // General Detail Section.
    shipper: formData?.shipper,
    custom_shipper_name: formData?.customShipperName,
    shipper_email: formData?.shipperEmail,
    custom_shipper_mobile_no: formData?.shipperMobileNumber,
    shipper_address: formData?.shipperAddress,
    forwarder: formData?.forwarder,
    forwarder_address: formData?.forwarderAddress,
    consignee: formData?.consignee,
    consignee__address: formData?.consigneeAddress,
    si_customer_name_entry: formData?.consigneeName,
    si_customer_address_entry: formData?.consigneeAddress,
    notify_party: formData?.notifyParty,
    notify_party_address: formData?.notifyPartyAddress,
    si_notify_party_name_entry: formData?.notifyPartyName,
    si_notify_party_address_entry: formData?.notifyPartyAddress,
    // Carrier Tab Section.
    carrier: formData?.carrier,
    carrier_booking_number: formData?.carrierBookingNumber,
    uca_carrier: formData?.UCACarrier,
    uca_carrier_email: formData?.UCAEmail,
    uca_carrier_booking_number: formData?.UCACarrierBookingNumber,

    // Additional parties.
    additional_party_one: formData?.additionalNotifyParty1,
    additional_party_one_name_entry: formData?.additionalNotifyParty1Name,
    additional_party_one_address: formData?.additionalNotifyParty1Address,
    additional_party_two: formData?.additionalNotifyParty2,
    additional_party_two_name_entry: formData?.additionalNotifyParty2Name,
    additional_party_two_address: formData?.additionalNotifyParty2Address,
    contract_party: formData?.contractParty,
    contract_party_name_entry: formData?.contractPartyName,
    contract_party_address: formData?.contractPartyAddress,
    freight_payer: formData?.freightPayer,
    freight_payer_address: formData?.freightPayerAddress,
    manufacturersupplier: formData?.supplier,
    manufacturersupplier_address: formData?.supplierAddress,
    consolidatorstuffer: formData?.consolidator,
    consolidatorstuffer_address: formData?.consolidatorAddress,
    warehouse_keeper: formData?.warehouseKeeper,
    warehouse_keeper_address: formData?.warehouseKeeperAddress,
    importer: formData?.importer,
    importer_address: formData?.importerAddress,

    // references Section.
    shipper_reference_number: formData?.shipperReferenceNumber,
    forwarder_reference_number: formData?.forwarderReferenceNumber,
    transaction_number: formData?.transactionReferenceNumber,
    unique_consignment_reference: formData?.uniqueConsignmentReferenceNumber,
    purchase_order_number: formData?.purchaseOrderNumber,
    contract_reference_number: formData?.contractReferenceNumber,
    bl_reference_number: formData?.BLReferenceNumber,
    exporter_reference_number: formData?.exportersReferenceNumbers,
    consignee_order_number: formData?.consigneeOrderNumber,
    invoice_reference_number: formData?.invoiceReferenceNumber,
    letter_of_credit_reference: formData?.letterOfCreditReferenceNumber,
    lcr_issue_date: formData?.letterOfCreditIssueDate,
    lcr_expiry_date: formData?.letterOfCreditExpiryDate,
    customs_house_broker_reference: formData?.customsHouseBrokerReferenceNumber,
    government_reference_or_fmc_number: formData?.govtReferenceOrFMCNumber,
    export_license_number: formData?.exportLicenseNumber,
    export_license_issue_date: formData?.exportLicenseIssueDate,
    export_license_expiry_date: formData?.exportLicenseExpiryDate,

    // Transport section.
    vessel: formData?.vessel,
    imo_number: formData?.IMONumber,
    voyage: formData?.voyage,
    origin_place_of_carrier_receipt: formData?.placeOfCarrierReceipt?.name,
    origin_name: formData?.placeOfCarrierReceipt?.location,
    port_of_load: formData?.portOfLoad?.name,
    port_of_load_name: formData?.portOfLoad?.location,
    port_of_discharge: formData?.portOfDischarge?.name,
    port_of_discharge_name: formData?.portOfDischarge?.location,
    destination_place_of_carrier_delivery:
      formData?.placeOfCarrierDelivery?.name,
    destination_name: formData?.placeOfCarrierDelivery?.location,
    print_on_bl_asorigin: formData?.placeOfCarrierReceipt?.name,
    print_on_bl_aspol: formData?.portOfLoad?.name,
    print_on_bl_aspod: formData?.portOfDischarge?.name,
    print_on_bl_asdestination: formData?.placeOfCarrierDelivery?.name,
    shipment_type: formData?.shipmentType,
    move_type: formData?.moveType,

    // Custom Compliance Section.
    shipper_tax_id: formData?.shipperTaxId,
    notify_party_tax_id: formData?.notifyPartyTaxId,
    consignee_tax_id: formData?.consigneeTaxId,
    print_tax_id_on_bill_of_laiding: Boolean(
      formData?.printTaxIdOnBillOfLaiding
    ),

    // ICS2 Summary Declaration.
    pcin: formData?.pcin,
    csn: formData?.csn,
    acid_number_mcin: formData?.acidNumber,
    are_the_goods_going_to_be_delivered_to_these_countries:
      formData?.goodsDeliveriedCountry,
    do_you_prefer_the_carrier_to_declare_the_entry_summary_ens:
      formData?.typeOfDeclaration,
    have_house_bills_been_issued_for_this_shipment:
      formData?.shipmentHouseBillStatus,
    eori: formData?.eori,

    first_country: "",
    countries_visited_in_between: formData?.countriesVisitedInBetween?.length
      ? formData?.countriesVisitedInBetween?.join("-")
      : "",
    last_country: "",
    house_bill_number_ics2: formData?.housebillOfLadingNumber,
    place_of_acceptance_origin_of_goods: formData?.placeOfAcceptance?.name,
    place_of_final_delivery: formData?.placeOfFinalDelivery?.name,
    method_of_payment: formData?.methodOfPayment,
    ics2_parties: [
      {
        party_type: "Shipper",
        selected_party: formData?.shipperPartyInvolved?.partyId,
        party_name: formData?.shipperPartyInvolved?.actualParty,
        street_number: formData?.shipperPartyInvolved?.streetNumber,
        street_name: formData?.shipperPartyInvolved?.streetName,
        contact_name: formData?.shipperPartyInvolved?.contactName,
        po_box: formData?.shipperPartyInvolved?.poBOX,
        state: formData?.shipperPartyInvolved?.state,
        city: formData?.shipperPartyInvolved?.city,
        country: formData?.shipperPartyInvolved?.country,
        postal_code: formData?.shipperPartyInvolved?.postalCode,
        tax_id_eori: formData?.shipperPartyInvolved?.taxID,
        telephone_number: "",
      },
      {
        party_type: "Consignee",
        selected_party: formData?.consigneePartyInvolved?.partyId,
        party_name: formData?.consigneePartyInvolved?.actualParty,
        street_number: formData?.consigneePartyInvolved?.streetNumber,
        street_name: formData?.consigneePartyInvolved?.streetName,
        contact_name: formData?.consigneePartyInvolved?.contactName,
        po_box: formData?.consigneePartyInvolved?.poBOX,
        state: formData?.consigneePartyInvolved?.state,
        city: formData?.consigneePartyInvolved?.city,
        country: formData?.consigneePartyInvolved?.country,
        postal_code: formData?.consigneePartyInvolved?.postalCode,
        tax_id_eori: formData?.consigneePartyInvolved?.taxID,
        telephone_number: "",
      },
      // {
      //   party_type: "Notify Party",
      //   selected_party: formData?.notifyPartyInvolved?.partyId,
      //   party_name: formData?.notifyPartyInvolved?.actualParty,
      //   street_number: formData?.notifyPartyInvolved?.streetNumber,
      //   street_name: formData?.notifyPartyInvolved?.streetName,
      //   contact_name: formData?.notifyPartyInvolved?.contactName,
      //   po_box: formData?.notifyPartyInvolved?.poBOX,
      //   state: formData?.notifyPartyInvolved?.state,
      //   city: formData?.notifyPartyInvolved?.city,
      //   country: formData?.notifyPartyInvolved?.country,
      //   postal_code: formData?.notifyPartyInvolved?.postalCode,
      //   tax_id_eori: formData?.notifyPartyInvolved?.taxID,
      //   telephone_number: "",
      // },
    ],

    // Particulars.
    i_am_shipping_only_one_cargo_in_this_shipment:
      formData?.isShippingSingleCargoOnly,
    container: formData?.particularContainers?.length
      ? formData?.particularContainers?.map((item) => {
          return {
            container_number: item?.containerNumber,
            container_type: item?.containerType?.name,
            container_supplier: item?.containerSupplier,
            container_tare_weight: item?.containerTareWeight?.weight,
            wood_declaration: item?.woodDeclaration,
            carrier_seal_numbers: item?.carrierSealNumber,
            shipper_seal_numbers: item?.shipperSealNumber,
            cargo_weight: item?.cargoGrossWeight || "",
            gross_weight:
              Number(item?.containerTareWeight?.weight) +
                Number(item?.cargoGrossWeight) || "",
            weight_value: "",
            tare_weight: item?.containerTareWeight?.weight,
            weight_type: item?.containerTareWeight?.unit,
            cargo: [
              {
                package_counttype_outermost: item?.cargoPackageType?.name,
                package_count: item?.cargoPackageCount,
                print_on_bl_as: item?.cargoPackageType?.name || "",
                hs_code: item?.hsCode || "",
                origin_of_goods: "", // ! Not in UI.
                cargo_description: item?.cargoDescription || "",
                schedule_b_number: item?.scheduleBNumber || "",
                cus_code: item?.cusCodes || "",
                ncm_codes: item?.ncmCodes || "",
                marks_and_numbers: item?.markAndNumbers || "",
                net_weight: item?.cargoGrossWeight || "",
                cargo_gross_weight: item?.cargoGrossWeight || "",
                net_weight_unit: item?.cargoGrossWeightUnit || "",
                gross_volume: item?.cargoGrossVolume || "",
                gross_volume_unit: item?.cargoGrossVolumeUnit || "",
              },
            ],
          };
        })
      : [],

    // Control Totals.
    total_number_of_containers: formData?.totalNumberOfContainers,
    total_number_of_packages: formData?.totalNumberOfPackages,
    total_shipment_weight: formData?.totalShipmentWeight,
    total_shipment_volume: formData?.totalShipmentVolume,

    // Shippers Declared value.
    currency_type: formData?.currencyType?.name,
    shippers_declared_value: formData?.shippersDeclaredValue,

    // Freight Charges.
    freight_charges:
      formData?.typeOfFreightCharge === "individualCharges" &&
      formData?.individualCharges?.length
        ? formData?.individualCharges?.map((item) => {
            return {
              charge_type: item?.chargeType,
              freight_term: item?.freightTerm,
              payer: item?.payer,
              payment_location: item?.paymentLocation?.name,
              type: "Individual Charges",
            };
          })
        : formData?.typeOfFreightCharge === "allCharges"
        ? [
            {
              freight_term: formData?.freightTerm,
              payer: formData?.payer,
              payment_location: formData?.paymentLocation?.name,
              type: "All Charges",
            },
          ]
        : null,

    // Documentation Section.
    bl_release_office_location: formData?.BLReleaseOffice?.name,
    print_on_bl_aslocation: formData?.printBLReleaseOffice,
    requested_date_of_issue: formData?.requestedDateOfIssue,

    // BL Print Instructions.
    bl_print_instruction_type: formData?.blPrintingInstructionType,
    freightedno_of_documents: formData?.freighted,
    unfreightedno_of_documents: formData?.unfreighted,
    non_negotiable_freightedno_of_copies: formData?.nonNegotiableFreighted,
    non_negotiable_unfreighted_no_of_copies: formData?.nonNegotiableUnfreighted,
    house_bill_number: formData?.houseBillNumber,
    bl_comments: formData?.blComments,

    // Notifications.
    si_requestor_emails: formData?.sIRequestorEmails,
    partner_notification_emails: formData?.partnerNotificationEmails,

    si_name: "", // ! Doubt.
    eln_issue_date: "", // !Doubt.
    eln_expiry_date: "", // !Doubt.
    bl_no: "", // ! Doubt.
    door_delivery_address: "", // TODO: Door Delivery address Button.
    shipper_iec: "", // ! Doubt.
    shipper_pan_no: "", // ! Doubt.
    contact_name: "", // ! Not in UI.
  };
};

export const populateForEmptyCarrierBookingNumberData = (
  payload: CreateShippingInstructionInitialDataType
): any => {
  const containerNumber =
    payload?.containers?.reduce((acc, item) => {
      return acc + Number(item?.number_of_containers);
    }, 0) || 1;

  const containerDataMapped = Array.from({ length: containerNumber })?.map(
    (_, idx) => {
      return {
        containerNumber: "",
        containerType: {
          name: "",
          type: "",
        },
        containerSupplier: "",
        containerTareWeight: {
          weight: "",
          unit: "KG",
        },
        woodDeclaration: "",
        carrierSealNumber: "",
        shipperSealNumber: "",
        // cargo details.
        cargoPackageCount: "",
        cargoPackageType: {
          xmlCode: "",
          shortName: "",
          name: "",
        },
        cargoPackageBLas: "",
        hsCode: "",
        scheduleBNumber: "",
        cargoDescription: "",
        ncmCodes: "",
        markAndNumbers: "",
        cusCodes: "",
        cargoGrossWeight: "",
        cargoGrossWeightUnit: "KG",
        cargoGrossVolume: "",
        cargoGrossVolumeUnit: "CBM",
      };
    }
  );

  // Map equipmentData (if present)
  const equipmentDataMapped =
    payload?.equipments?.map((item) => {
      let cargoData;
      if (item?.cargo?.length) {
        cargoData = item?.cargo[0];
      }
      return {
        containerNumber: item?.equipment_name || "",
        containerType: {
          name: item?.container_type_id?.name?.toString() || "",
          type: item?.container_type_id?.shortdescription || "",
        },
        containerSupplier: item?.supplier_type || "",
        containerTareWeight: {
          weight: item?.tare_weight?.toString() || "",
          unit: "KG",
        },
        woodDeclaration: item?.wood_declaration || "",
        carrierSealNumber: item?.carrier_seal_number || "",
        shipperSealNumber: item?.shipper_seal_number || "",
        cargoPackageCount: cargoData?.package_count || "",
        cargoPackageType: {
          xmlCode: cargoData?.package_counttype_outermost?.xmlcode || "",
          shortName: cargoData?.package_counttype_outermost?.short_name || "",
          name: cargoData?.package_counttype_outermost?.name || "",
        },
        cargoPackageBLas: cargoData?.print_on_bl_as || "",
        hsCode: cargoData?.hs_code?.hs_code || "",
        scheduleBNumber: cargoData?.schedule_b_number || "",
        cargoDescription: cargoData?.hs_code?.hs_code_description || "",
        ncmCodes: cargoData?.ncm_codes || "",
        markAndNumbers: item?.marks_and_numbers || "",
        cusCodes: cargoData?.cus_code || "",
        cargoGrossWeight: cargoData?.net_weight
          ? cargoData?.net_weight?.toString()
          : item?.gross_weight
          ? item?.gross_weight?.toString()
          : item?.cargo_weight?.toString() || "",
        cargoGrossWeightUnit: cargoData?.weight_type || "KG",
        cargoGrossVolume: cargoData?.gross_volume?.toString() || "",
        cargoGrossVolumeUnit: cargoData?.gross_volume_unit || "CBM",
      };
    }) || [];

  let finalResult = [];

  if (equipmentDataMapped.length >= containerNumber) {
    finalResult = equipmentDataMapped;
  } else {
    const remaining = containerNumber - equipmentDataMapped?.length;
    const fallbackData = containerDataMapped?.slice(-remaining);
    finalResult = [...equipmentDataMapped, ...fallbackData];
  }
  return {
    bookingNumber: payload?.inttra_reference || "",
    creationDate: payload?.creationdate || "",
    bookingRequestId: payload?.name || "",

    // top parties general details.
    shipper: payload?.shipper?.name || "",
    customShipperName: payload?.shipper?.shipper_name || "",
    shipperEmail: payload?.shipper?.email || "",
    shipperMobileNumber: payload?.shipper?.phone || "",
    shipperAddress: payload?.shipper?.custom_address || "",
    forwarder: payload?.forwarder || "",
    forwarderName: "",
    forwarderAddress: "",

    // bottom partier general details.
    consignee: payload?.consignee?.name || "",
    consigneeName: payload?.consignee?.customer_name || "",
    consigneeAddress: payload?.consignee?.customer_address || "",
    notifyParty: payload?.notify_parties?.name || "",
    notifyPartyName: payload?.notify_parties?.customer_name || "",
    notifyPartyAddress: payload?.notify_parties?.customer_address || "",

    // references.
    shipperReferenceNumber: payload?.shippers_reference_numbers || "",
    forwarderReferenceNumber: payload?.forwarders_reference_numbers || "",
    transactionReferenceNumber: "",
    uniqueConsignmentReferenceNumber: "",
    purchaseOrderNumber: payload?.purchase_order_numbers || "",
    contractReferenceNumber: payload?.contract_party_reference_numbers || "",
    BLReferenceNumber: payload?.bl_reference_numbers || "",
    exportersReferenceNumbers: "",
    consigneeOrderNumber: "",
    invoiceReferenceNumber: "",
    letterOfCreditReferenceNumber: "",
    letterOfCreditIssueDate: "",
    letterOfCreditExpiryDate: "",
    customsHouseBrokerReferenceNumber: "",
    govtReferenceOrFMCNumber: "",
    exportLicenseNumber: "",
    exportLicenseIssueDate: "",
    exportLicenseExpiryDate: "",

    // carrier Tab section.
    carrier: payload?.carrier?.name || "",
    carrierBookingNumber: payload?.carrier_booking_number || "",
    UCACarrier: "",
    UCAEmail: "",
    UCACarrierBookingNumber: "",

    // additional Parties.
    additionalNotifyParty1: "",
    additionalNotifyParty1Name: "",
    additionalNotifyParty1Address: "",
    additionalNotifyParty2: "",
    additionalNotifyParty2Name: "",
    additionalNotifyParty2Address: "",
    contractParty: "",
    contractPartyName: "",
    contractPartyAddress: "",
    freightPayer: "",
    freightPayerAddress: "",
    supplier: "",
    supplierAddress: "",
    consolidator: "",
    consolidatorAddress: "",
    importer: "",
    importerAddress: "",
    warehouseKeeper: "",
    warehouseKeeperAddress: "",

    // transport.
    vessel: payload?.vessel || "",
    voyage: payload?.main_carriage?.length
      ? payload?.main_carriage[0]?.voyage
      : "",
    IMONumber: "",
    placeOfCarrierReceipt: {
      name: payload?.place_of_carrier_receipt?.name || "",
      location: `${payload?.place_of_carrier_receipt?.location_name}, ${
        payload?.place_of_carrier_receipt?.sub_division
          ? `${payload?.place_of_carrier_receipt?.sub_division},`
          : ""
      } ${payload?.place_of_carrier_receipt?.country} (${
        payload?.place_of_carrier_receipt?.locode
      })`,
      locode: payload?.place_of_carrier_receipt?.locode || "",
    },
    placeOfCarrierReceiptBLas: `${
      payload?.place_of_carrier_receipt?.location_name
    }, ${
      payload?.place_of_carrier_receipt?.sub_division
        ? `${payload?.place_of_carrier_receipt?.sub_division},`
        : ""
    } ${payload?.place_of_carrier_receipt?.country}`,
    portOfLoad: {
      name: payload?.port_of_load?.name || "",
      location: `${payload?.port_of_load?.location_name}, ${
        payload?.port_of_load?.sub_division
          ? `${payload?.port_of_load?.sub_division},`
          : ""
      } ${payload?.port_of_load?.country} (${payload?.port_of_load?.locode})`,
      locode: payload?.port_of_load?.locode || "",
    },
    portOfLoadBLas: `${payload?.port_of_load?.location_name}, ${
      payload?.port_of_load?.sub_division
        ? `${payload?.port_of_load?.sub_division},`
        : ""
    } ${payload?.port_of_load?.country}`,
    portOfDischarge: {
      name: payload?.port_of_discharge?.name || "",
      location: `${payload?.port_of_discharge?.location_name}, ${
        payload?.port_of_discharge?.sub_division
          ? `${payload?.port_of_discharge?.sub_division},`
          : ""
      } ${payload?.port_of_discharge?.country} (${
        payload?.port_of_discharge?.locode
      })`,
      locode: payload?.port_of_discharge?.locode || "",
    },
    portOfDischargeBLas: `${payload?.port_of_discharge?.location_name}, ${
      payload?.port_of_discharge?.sub_division
        ? `${payload?.port_of_discharge?.sub_division},`
        : ""
    } ${payload?.port_of_discharge?.country}`,
    placeOfCarrierDelivery: {
      name: payload?.place_of_carrier_delivery?.name || "",
      location: `${payload?.place_of_carrier_delivery?.location_name}, ${
        payload?.place_of_carrier_delivery?.sub_division
          ? `${payload?.place_of_carrier_delivery?.sub_division},`
          : ""
      } ${payload?.place_of_carrier_delivery?.country} (${
        payload?.place_of_carrier_delivery?.locode
      })`,
      locode: payload?.place_of_carrier_delivery?.locode || "",
    },
    placeOfCarrierDeliveryBLas: `${
      payload?.place_of_carrier_delivery?.location_name
    }, ${
      payload?.place_of_carrier_delivery?.sub_division
        ? `${payload?.place_of_carrier_delivery?.sub_division},`
        : ""
    } ${payload?.place_of_carrier_delivery?.country}`,
    moveType: payload?.move_type || "",
    shipmentType: "",

    // Customs Compliance.
    shipperTaxId: "",
    consigneeTaxId: "",
    notifyPartyTaxId: "",
    printTaxIdOnBillOfLaiding: "1",

    // ICS2 Summary Declaration.
    goodsDeliveriedCountry: "",
    typeOfDeclaration: "",
    shipmentHouseBillStatus: "",
    pcin: "",
    csn: "",
    acidNumber: "",
    eori: "",
    // House bill modal
    housebillOfLadingNumber: "",
    placeOfAcceptance: {
      name: payload?.port_of_load?.name || "",
      location: `${payload?.port_of_load?.location_name}, ${
        payload?.port_of_load?.sub_division
          ? `${payload?.port_of_load?.sub_division},`
          : ""
      } ${payload?.port_of_load?.country} (${payload?.port_of_load?.locode})`,
      locode: payload?.port_of_load?.locode || "",
    },
    placeOfFinalDelivery: {
      name: payload?.port_of_discharge?.name || "",
      location: `${payload?.port_of_discharge?.location_name}, ${
        payload?.port_of_discharge?.sub_division
          ? `${payload?.port_of_discharge?.sub_division},`
          : ""
      } ${payload?.port_of_discharge?.country} (${
        payload?.port_of_discharge?.locode
      })`,
      locode: payload?.port_of_discharge?.locode || "",
    },
    firstCountry: payload?.port_of_load?.country || "",
    countriesVisitedInBetween: extractCountryCodes(payload?.main_carriage) || [
      "",
    ],
    lastCountry: payload?.port_of_discharge?.country || "",
    methodOfPayment: "",
    shipperPartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },
    consigneePartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },
    notifyPartyInvolved: {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    },

    // particulars.
    isShippingSingleCargoOnly: true,
    // container details.
    particularContainers: finalResult || [],

    // Control totals.
    totalNumberOfContainers: "",
    totalNumberOfPackages: "",
    totalShipmentWeight: "",
    totalShipmentVolume: "",

    // Shippers Declared value.
    currencyType: {
      currencyCode: "",
      currencyName: "",
      currencyType: "",
      name: "",
    },
    shippersDeclaredValue: "",

    // Freight Charges.
    typeOfFreightCharge: "allCharges",
    freightTerm: "",
    payer: "",
    paymentLocation: {
      name: "",
      location: "",
      locode: "",
    },
    individualCharges: [
      {
        chargeType: "Basic Freight",
        freightTerm: "",
        payer: "",
        paymentLocation: {
          name: "",
          location: "",
          locode: "",
        },
      },
    ],

    // Documentation.
    clauses: [""],
    userDefinedClauses: "",
    BLReleaseOffice: {
      name: "",
      location: "",
      locode: "",
    },
    printBLReleaseOffice: "",
    requestedDateOfIssue: "",

    // BL Print Instructions.
    blPrintingInstructionType: "express",
    freighted: "",
    unfreighted: "",
    nonNegotiableFreighted: "",
    nonNegotiableUnfreighted: "",
    houseBillNumber: "",
    blComments: "",

    // Notificarions.
    sIRequestorEmails: "",
    partnerNotificationEmails: "",
  };
};

export const populateForTempCarrierBookingNumberData = (
  payload: CreateShippingInstructionInitialDataType,
  previousValues: SIRequestFormType
): any => {
  const containerNumber =
    payload?.containers?.reduce((acc, item) => {
      return acc + Number(item?.number_of_containers);
    }, 0) || 1;

  const containerDataMapped = Array.from({ length: containerNumber })?.map(
    (_, idx) => {
      return {
        containerNumber: "",
        containerType: {
          name: "",
          type: "",
        },
        containerSupplier: "",
        containerTareWeight: {
          weight: "",
          unit: "KG",
        },
        woodDeclaration: "",
        carrierSealNumber: "",
        shipperSealNumber: "",
        // cargo details.
        cargoPackageCount: "",
        cargoPackageType: {
          xmlCode: "",
          shortName: "",
          name: "",
        },
        cargoPackageBLas: "",
        hsCode: "",
        scheduleBNumber: "",
        cargoDescription: "",
        ncmCodes: "",
        markAndNumbers: "",
        cusCodes: "",
        cargoGrossWeight: "",
        cargoGrossWeightUnit: "KG",
        cargoGrossVolume: "",
        cargoGrossVolumeUnit: "CBM",
      };
    }
  );

  // fetching One container data from template.
  const templateContainer = previousValues?.particularContainers?.length
    ? previousValues?.particularContainers[0]
    : null;

  // Map equipmentData (if present)
  const equipmentDataMapped =
    payload?.equipments?.map((item) => {
      let cargoData;
      if (item?.cargo?.length) {
        cargoData = item?.cargo[0];
      }
      return {
        containerNumber: item?.equipment_name || "",
        containerType: {
          name: templateContainer?.containerType?.name || "",
          type: templateContainer?.containerType?.type || "",
        },
        containerSupplier: item?.supplier_type || "",
        containerTareWeight: {
          weight: item?.tare_weight?.toString() || "",
          unit: "KG",
        },
        woodDeclaration: item?.wood_declaration || "",
        carrierSealNumber: item?.carrier_seal_number || "",
        shipperSealNumber: item?.shipper_seal_number || "",
        cargoPackageCount: templateContainer?.cargoPackageCount || "",
        cargoPackageType: {
          xmlCode: templateContainer?.cargoPackageType?.xmlCode || "",
          shortName: templateContainer?.cargoPackageType?.shortName || "",
          name: templateContainer?.cargoPackageType?.name || "",
        },
        cargoPackageBLas: cargoData?.print_on_bl_as || "",
        hsCode: templateContainer?.hsCode || "",
        scheduleBNumber: cargoData?.schedule_b_number || "",
        cargoDescription: templateContainer?.cargoDescription || "",
        ncmCodes: cargoData?.ncm_codes || "",
        markAndNumbers: item?.marks_and_numbers || "",
        cusCodes: cargoData?.cus_code || "",
        cargoGrossWeight: cargoData?.net_weight
          ? cargoData?.net_weight?.toString()
          : item?.gross_weight
          ? item?.gross_weight?.toString()
          : item?.cargo_weight?.toString() || "",
        cargoGrossWeightUnit: cargoData?.weight_type || "KG",
        cargoGrossVolume: templateContainer?.cargoGrossVolume?.toString() || "",
        cargoGrossVolumeUnit: templateContainer?.cargoGrossVolumeUnit || "CBM",
      };
    }) || [];

  let finalResult = [];

  if (equipmentDataMapped.length >= containerNumber) {
    finalResult = equipmentDataMapped;
  } else {
    const remaining = containerNumber - equipmentDataMapped?.length;
    const fallbackData = containerDataMapped?.slice(-remaining);
    finalResult = [...equipmentDataMapped, ...fallbackData];
  }
  return {
    bookingRequestId: payload?.name || "",

    //references
    transactionReferenceNumber: "",
    // transport.
    vessel: payload?.vessel || "",
    voyage: payload?.main_carriage?.length
      ? payload?.main_carriage[0]?.voyage
      : "",
    IMONumber: "",
    placeOfCarrierReceipt: {
      name: payload?.place_of_carrier_receipt?.name || "",
      location: `${payload?.place_of_carrier_receipt?.location_name}, ${
        payload?.place_of_carrier_receipt?.sub_division
          ? `${payload?.place_of_carrier_receipt?.sub_division},`
          : ""
      } ${payload?.place_of_carrier_receipt?.country} (${
        payload?.place_of_carrier_receipt?.locode
      })`,
      locode: payload?.place_of_carrier_receipt?.locode || "",
    },
    placeOfCarrierReceiptBLas: `${
      payload?.place_of_carrier_receipt?.location_name
    }, ${
      payload?.place_of_carrier_receipt?.sub_division
        ? `${payload?.place_of_carrier_receipt?.sub_division},`
        : ""
    } ${payload?.place_of_carrier_receipt?.country}`,
    portOfLoad: {
      name: payload?.port_of_load?.name || "",
      location: `${payload?.port_of_load?.location_name}, ${
        payload?.port_of_load?.sub_division
          ? `${payload?.port_of_load?.sub_division},`
          : ""
      } ${payload?.port_of_load?.country} (${payload?.port_of_load?.locode})`,
      locode: payload?.port_of_load?.locode || "",
    },
    portOfLoadBLas: `${payload?.port_of_load?.location_name}, ${
      payload?.port_of_load?.sub_division
        ? `${payload?.port_of_load?.sub_division},`
        : ""
    } ${payload?.port_of_load?.country}`,
    portOfDischarge: {
      name: payload?.port_of_discharge?.name || "",
      location: `${payload?.port_of_discharge?.location_name}, ${
        payload?.port_of_discharge?.sub_division
          ? `${payload?.port_of_discharge?.sub_division},`
          : ""
      } ${payload?.port_of_discharge?.country} (${
        payload?.port_of_discharge?.locode
      })`,
      locode: payload?.port_of_discharge?.locode || "",
    },
    portOfDischargeBLas: `${payload?.port_of_discharge?.location_name}, ${
      payload?.port_of_discharge?.sub_division
        ? `${payload?.port_of_discharge?.sub_division},`
        : ""
    } ${payload?.port_of_discharge?.country}`,
    placeOfCarrierDelivery: {
      name: payload?.place_of_carrier_delivery?.name || "",
      location: `${payload?.place_of_carrier_delivery?.location_name}, ${
        payload?.place_of_carrier_delivery?.sub_division
          ? `${payload?.place_of_carrier_delivery?.sub_division},`
          : ""
      } ${payload?.place_of_carrier_delivery?.country} (${
        payload?.place_of_carrier_delivery?.locode
      })`,
      locode: payload?.place_of_carrier_delivery?.locode || "",
    },
    placeOfCarrierDeliveryBLas: `${
      payload?.place_of_carrier_delivery?.location_name
    }, ${
      payload?.place_of_carrier_delivery?.sub_division
        ? `${payload?.place_of_carrier_delivery?.sub_division},`
        : ""
    } ${payload?.place_of_carrier_delivery?.country}`,
    moveType: payload?.move_type || "",
    shipmentType: "",

    // Particulars.
    isShippingSingleCargoOnly: true,
    particularContainers: finalResult || [],

    // House bill modal
    placeOfAcceptance: {
      name: payload?.port_of_load?.name || "",
      location: `${payload?.port_of_load?.location_name}, ${
        payload?.port_of_load?.sub_division
          ? `${payload?.port_of_load?.sub_division},`
          : ""
      } ${payload?.port_of_load?.country} (${payload?.port_of_load?.locode})`,
      locode: payload?.port_of_load?.locode || "",
    },
    placeOfFinalDelivery: {
      name: payload?.port_of_discharge?.name || "",
      location: `${payload?.port_of_discharge?.location_name}, ${
        payload?.port_of_discharge?.sub_division
          ? `${payload?.port_of_discharge?.sub_division},`
          : ""
      } ${payload?.port_of_discharge?.country} (${
        payload?.port_of_discharge?.locode
      })`,
      locode: payload?.port_of_discharge?.locode || "",
    },
    firstCountry: payload?.port_of_load?.country || "",
    countriesVisitedInBetween: extractCountryCodes(payload?.main_carriage) || [
      "",
    ],
    lastCountry: payload?.port_of_discharge?.country || "",
  };
};
