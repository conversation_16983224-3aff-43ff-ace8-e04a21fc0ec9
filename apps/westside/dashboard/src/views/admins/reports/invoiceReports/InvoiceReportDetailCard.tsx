import { Typography } from "@/components/typography";
import dayjs from "dayjs";
import { FC } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { InvoiceResponseType } from "@/types/InvoiceReportType";

interface InvoiceReportDetailTableProps {
  reports: InvoiceResponseType[];
}

const formatDate = (dateString: string) => {
  if (!dateString || dateString === "N/A") return "N/A";

  try {
    const date = new Date(dateString);
    const month = date.toLocaleString("default", { month: "short" });
    const day = date.getDate().toString().padStart(2, "0");
    const year = date.getFullYear();

    return `${month}-${day}-${year}`;
  } catch (e) {
    // Fallback to original behavior if parsing fails
    return dateString.split(" ")[0];
  }
};

const InvoiceReportDetailTable: FC<InvoiceReportDetailTableProps> = ({
  reports,
}) => {
  // Function to determine status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-800";
      case "Pending Payment":
        return "bg-yellow-100 text-yellow-800";
      case "Overdue":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white border rounded-lg p-5 shadow-sm">
      {/* Header Information */}
      <div className="mb-4">
        <Typography variant="h4" weight="semibold" className="text-[#191C36]">
          Invoice Reports
        </Typography>
      </div>

      {/* Table Layout */}
      <Table className="w-full border-separate border-spacing-y-2">
        <TableHeader>
          <TableRow className="bg-[#D3DAE7] h-[55px]">
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Carrier Booking #
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              BOL #
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Invoice #
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Customer Name
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Invoice Date
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Product Group
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Quantity
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Unit Price
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Invoice Amount
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Status
            </TableHead>
            <TableHead className="p-2 text-left font-bold text-[#191C36]">
              Overdue Days
            </TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {reports.length > 0 ? (
            reports.map((report, reportIndex) => {
              const invoiceItems = report?.items || [];

              // If no items, still show the invoice with empty item columns
              if (invoiceItems.length === 0) {
                return (
                  <TableRow
                    key={`${reportIndex}-no-items`}
                    className="border border-[#D3DAE7] shadow-sm"
                  >
                    <TableCell className="p-2">
                      {report?.carrier_booking_number || "N/A"}
                    </TableCell>
                    <TableCell className="p-2">
                      {report?.bol || "N/A"}
                    </TableCell>
                    <TableCell className="p-2">
                      {report?.invoice_number || "N/A"}
                    </TableCell>
                    <TableCell className="p-2">
                      {report?.customer_name ||
                        report?.quickbooks_customer_name ||
                        "N/A"}
                    </TableCell>
                    <TableCell className="p-2">
                      {report?.invoice_date
                        ? formatDate(
                            dayjs(report.invoice_date).format("YYYY-MM-DD")
                          )
                        : "N/A"}
                    </TableCell>
                    <TableCell className="p-2">-</TableCell>
                    <TableCell className="p-2">-</TableCell>
                    <TableCell className="p-2">-</TableCell>
                    <TableCell className="p-2 text-blue-600 font-bold">
                      {/* ${report?.total_amount?.toFixed(2) || "0.00"} */}
                      {report?.total_amount !== undefined &&
                      report?.total_amount !== null
                        ? new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD",
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(report.total_amount)
                        : "$0.00"}
                    </TableCell>
                    <TableCell className="p-2">
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                          report?.status || ""
                        )}`}
                      >
                        {report?.status || "N/A"}
                      </span>
                    </TableCell>
                    <TableCell className="p-2">
                      {report?.days_overdue ?? 0} days
                    </TableCell>
                  </TableRow>
                );
              }

              // Create a row for each invoice item
              return (
                <TableRow
                  key={`${reportIndex}`}
                  className="border border-[#D3DAE7] shadow-sm"
                >
                  {/* Only show invoice details on the first item row */}

                  <TableCell className="p-2">
                    {report?.carrier_booking_number || "N/A"}
                  </TableCell>
                  <TableCell className="p-2">{report?.bol || "N/A"}</TableCell>
                  <TableCell className="p-2">
                    {report?.invoice_number || "N/A"}
                  </TableCell>
                  <TableCell className="p-2">
                    {report?.customer_name ||
                      report?.quickbooks_customer_name ||
                      "N/A"}
                  </TableCell>
                  <TableCell className="p-2">
                    {report?.invoice_date
                      ? formatDate(
                          dayjs(report.invoice_date).format("YYYY-MM-DD")
                        )
                      : "N/A"}
                  </TableCell>
                  {/* Stacked product items */}
                  <TableCell className="p-2 align-top">
                    {invoiceItems.map((item, idx) => (
                      <div key={idx}>
                        {invoiceItems.length > 1 ? `${idx + 1}. ` : ""}
                        {item.category_name || "N/A"}
                      </div>
                    ))}
                  </TableCell>

                  <TableCell className="p-2 align-top text-right">
                    {invoiceItems.map((item, idx) => (
                      <div key={idx}>{item.quantity?.toFixed(2) || "0.00"}</div>
                    ))}
                  </TableCell>

                  <TableCell className="p-2 align-top text-right">
                    {invoiceItems.map((item, idx) => (
                      <div key={idx}>
                        {item.rate !== undefined && item.rate !== null
                          ? new Intl.NumberFormat("en-US", {
                              style: "currency",
                              currency: "USD",
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(item.rate)
                          : "$0.00"}
                      </div>
                    ))}
                  </TableCell>
                  <TableCell className="p-2 text-blue-600 font-bold text-right">
                    {/* ${report?.total_amount?.toFixed(2) || "0.00"} */}
                    {report?.total_amount !== undefined &&
                    report?.total_amount !== null
                      ? new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency: "USD",
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(report.total_amount)
                      : "$0.00"}
                  </TableCell>
                  <TableCell className="p-2">
                    <span
                      className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                        report?.status || ""
                      )}`}
                    >
                      {report?.status || "N/A"}
                    </span>
                  </TableCell>
                  <TableCell className="p-2">
                    {report?.days_overdue ?? 0} days
                  </TableCell>
                </TableRow>
              );
            })
          ) : (
            <TableRow>
              <TableCell colSpan={11} className="p-4 text-center text-gray-500">
                No Reports Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default InvoiceReportDetailTable;
