import { Fragment, useEffect, useRef, useState } from "react";
// react router dom
import { useNavigate, useSearchParams } from "react-router-dom";
// shadcn
import { Typography } from "@/components/typography";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Check,
  ChevronsUpDown,
  Eye,
  Plus,
  RefreshCcw,
  Repeat2,
  SquarePen,
} from "lucide-react";
// reactQuery
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { BookingStatusEnums } from "@/types/booking";
import Loader from "@/components/Loader";
import { fetchCarrierList } from "@/services/admin/common";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  <PERSON>Header,
  TableRow,
} from "@/components/ui/table";
import { fetchDraftedSI } from "@/services/admin/upcomingTask";
import dayjs from "dayjs";

import customParseFormat from "dayjs/plugin/customParseFormat.js";
import { FormProvider, useForm } from "react-hook-form";
import {
  fetchListingShippingIns,
  getAllSiStatuses,
  filterConsignee,
  filterCarrier,
} from "@/services/admin/shippingInstructionConfirmation";
import { fetchBookingLocations } from "@/services/admin/booking";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { BLComboBox } from "../../documentation/billOfLadding/blComboBox";

const DraftSI = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const loaderRef = useRef<HTMLDivElement | null>(null);

  const carrier = searchParams.get("carrier") || "";
  const bookingStatus = searchParams.get("bookingStatus") || "";
  const search = searchParams.get("search") || "";
  const sortBy = searchParams.get("sortBy") || "";
  dayjs.extend(customParseFormat);
  // const {
  //   data: carrierData,
  //   isFetching: isCarrierFetching,
  //   isError: isCarrierError,
  // } = useQuery({
  //   queryKey: ["fetchCarrierList"],
  //   queryFn: fetchCarrierList,
  // });

  // const updateParam = (key: string, value: string) => {
  //   const params = new URLSearchParams(searchParams);
  //   if (value) {
  //     params.set(key, value);
  //   } else {
  //     params.delete(key);
  //   }
  //   setSearchParams(params);
  // };

  // const [localSearch, setLocalSearch] = useState(search);

  // // Update search param with debounce
  // useEffect(() => {
  //   const delay = setTimeout(() => {
  //     const params = new URLSearchParams(searchParams);
  //     if (localSearch) {
  //       params.set("search", localSearch);
  //     } else {
  //       params.delete("search");
  //     }
  //     setSearchParams(params);
  //   }, 500); // 500ms debounce

  //   return () => clearTimeout(delay); // Cleanup on change
  // }, [localSearch, searchParams, setSearchParams]);
  const methods = useForm();

  const initialConsigneeQuery = searchParams.get("consignee") || "";
  const initialCarrierQuery = searchParams.get("carrier") || "";
  const initialSearchQuery = searchParams.get("searchText") || "";
  const initialPortOfLoad = {
    name: searchParams.get("pol_name") || "",
    location: searchParams.get("pol_location") || "",
    locode: searchParams.get("pol_locode") || "",
  };
  const [portOfLoadQuery, setPortOfLoadQuery] = useState("");
  const [portOfLoadOpen, setPortOfLoadOpen] = useState(false);
  const [shownValue, setShownValue] = useState(
    initialPortOfLoad.location || ""
  );

  const searchQuery = searchParams.get("searchText") || "";

  const [consigneeQuery, setConsigneeQuery] = useState(initialConsigneeQuery);
  const [carrierQuery, setCarrierQuery] = useState(initialCarrierQuery);
  const [searchText, setSearchText] = useState(initialSearchQuery);
  const portOfLoadValue = methods.watch("portOfLoad");
  const [filtersCleared, setFiltersCleared] = useState(false);

  useEffect(() => {
    const hasInitialValue =
      initialPortOfLoad.name ||
      initialPortOfLoad.location ||
      initialPortOfLoad.locode;

    if (hasInitialValue && !portOfLoadValue?.locode && !filtersCleared) {
      methods.setValue("portOfLoad", initialPortOfLoad);
    }
  }, [initialPortOfLoad, portOfLoadValue, methods, filtersCleared]);

  const { data: consigneeListData } = useQuery({
    queryKey: ["fetchConsigneeList"],
    queryFn: () => filterConsignee(),
  });

  const { data: carrierListData } = useQuery({
    queryKey: ["fetchCarrierList"],
    queryFn: () => filterCarrier(),
  });

  const { data: portOfLoadData, isFetching: isPortOfLoadFetching } = useQuery({
    queryKey: ["fetchBookingLocation", { search: portOfLoadQuery }],
    queryFn: fetchBookingLocations,
  });

  const handleSearch = (val: string) => {
    console.log("Search value:", val);
    setPortOfLoadQuery(val);
  };

  const {
    data: listShippingInstData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: [
      "fetchDraftedSI",
      portOfLoadValue?.locode,
      carrierQuery,
      consigneeQuery,
      searchQuery,
    ],
    queryFn: ({ pageParam = 1 }) =>
      fetchDraftedSI({
        pageParam,
        portOfLoad: portOfLoadValue?.name,
        carrier: carrierQuery,
        consignee: consigneeQuery,
        searchText: searchQuery,
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages, pageCount) => {
      const currentPage = lastPage?.message?.page;
      const totalPages = lastPage?.message?.total_count ?? 1;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
  });
  const {
    data: _siStatuses,
    isLoading: _isStatusesLoading,
    isError: _isStatusesError,
  } = useQuery({
    queryKey: ["siStatuses"],
    queryFn: () => getAllSiStatuses(),
  });
  useEffect(() => {
    const loader = loaderRef.current;
    if (!loader) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      {
        rootMargin: "0px 0px 200px 0px",
      }
    );

    observer.observe(loader);

    return () => {
      if (loader) observer.unobserve(loader);
      observer.disconnect();
    };
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);
  useEffect(() => {
    refetch();
  }, [portOfLoadValue?.locode, carrierQuery, consigneeQuery]);

  console.log(listShippingInstData, "log");

  return (
    <div className="">
      <>
        {/* Table here */}
        <div className="flex flex-col gap-4 mt-6 lg:flex-row lg:justify-end lg:items-center">
          <div className="flex gap-3">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 w-full sm:w-auto">
              {/* Port of Load filter */}
              <div className="min-w-[100px] max-w-[250px]">
                <FormProvider {...methods}>
                  <FormField
                    control={methods.control}
                    name="portOfLoad"
                    render={({ field }) => {
                      const { value, onChange } = field;
                      return (
                        <FormItem className="w-full space-y-0">
                          <FormLabel className="flex justify-between items-center">
                            <span className="text-sm text-black font-medium">
                              Port of Load
                            </span>
                          </FormLabel>
                          <FormControl>
                            <Popover
                              open={portOfLoadOpen}
                              onOpenChange={setPortOfLoadOpen}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="justify-between w-full overflow-hidden h-11"
                                >
                                  {value?.name ? (
                                    shownValue
                                  ) : (
                                    <span className="text-gray-400">
                                      Select Location...
                                    </span>
                                  )}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-full p-0">
                                <Command>
                                  <CommandInput
                                    placeholder="Search Location..."
                                    value={portOfLoadQuery}
                                    onValueChange={(value) =>
                                      handleSearch(value)
                                    }
                                    className="h-9"
                                  />
                                  <CommandList>
                                    <CommandEmpty>
                                      {isPortOfLoadFetching ? (
                                        <div className="flex justify-center w-full">
                                          <SpinnerLoader />
                                        </div>
                                      ) : (
                                        "No Location Found."
                                      )}
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {portOfLoadData?.message?.results?.map(
                                        (location) => {
                                          const locationStr = `${location?.location_name}, ${location.country} (${location.locode})`;
                                          return (
                                            <CommandItem
                                              key={location.name}
                                              value={locationStr}
                                              onSelect={() => {
                                                onChange({
                                                  name: String(location.name),
                                                  location: locationStr,
                                                  locode: location.locode,
                                                });
                                                methods.setValue(
                                                  "portOfLoadBLas",
                                                  `${location.location_name}, ${
                                                    location.sub_division
                                                      ? `${location.sub_division}, `
                                                      : ""
                                                  }${location.country}`
                                                );
                                                setShownValue(locationStr);
                                                setPortOfLoadOpen(false);
                                                setSearchParams((prev) => {
                                                  const newParams =
                                                    new URLSearchParams(prev);
                                                  newParams.set(
                                                    "pol_name",
                                                    location.name
                                                  );
                                                  newParams.set(
                                                    "pol_location",
                                                    locationStr
                                                  );
                                                  newParams.set(
                                                    "pol_locode",
                                                    location.locode
                                                  );
                                                  return newParams;
                                                });
                                              }}
                                            >
                                              {locationStr}
                                              <Check
                                                className={`ml-auto ${
                                                  value?.name === location.name
                                                    ? "opacity-100"
                                                    : "opacity-0"
                                                }`}
                                              />
                                            </CommandItem>
                                          );
                                        }
                                      )}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </FormProvider>
              </div>

              {/* Carrier filter */}
              <div className="min-w-[100px]">
                <BLComboBox
                  label="Carriers"
                  required={false}
                  value={carrierQuery}
                  onChange={(value) => {
                    setCarrierQuery(value);
                    // setCurrentPage(1);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("carrier", value);
                      return newParams;
                    });
                  }}
                  options={carrierListData?.message?.carriers.map((c: any) => ({
                    label: c.partyname1,
                    value: c.name,
                  }))}
                  placeholder="Select Carrier"
                />
              </div>
              {/* consignee */}
              {/* <div className=" min-w-[100px]">
                <BLComboBox
                  label="Consignee"
                  required={false}
                  value={consigneeQuery}
                  onChange={(value) => {
                    setConsigneeQuery(value);
                    // setCurrentPage(1);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("consignee", value);
                      return newParams;
                    });
                  }}
                  options={consigneeListData?.message?.consignee.map(
                    (c: any) => ({
                      label: c.customer_name,
                      value: c.name,
                    })
                  )}
                  placeholder="Select Consignee"
                />
              </div> */}
              <div className="min-w-[100px] max-w-[150px] mt-5 relative">
                <Input
                  className="pr-10"
                  placeholder="Search here"
                  value={searchText}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSearchText(value);
                    // setCurrentPage(1);

                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);

                      if (value.length >= 4) {
                        newParams.set("searchText", value);
                        newParams.set("page", "1");
                      } else {
                        // Clear search if less than 4 characters
                        newParams.delete("searchText");
                        newParams.set("page", "1");
                      }

                      return newParams;
                    });
                  }}
                />
                <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
              {/* Clear Filters button */}
              {(searchText ||
                portOfLoadQuery ||
                carrierQuery ||
                consigneeQuery ||
                (portOfLoadValue && portOfLoadValue.locode !== "")) && (
                <div className="mt-5">
                  <Button
                    variant="outline"
                    className="h-11 w-full sm:w-auto"
                    title={"Clear Filter"}
                    onClick={() => {
                      setFiltersCleared(true);
                      setPortOfLoadQuery("");
                      setCarrierQuery("");
                      setConsigneeQuery("");
                      setSearchText("");
                      // setCurrentPage(1);
                      methods.reset({
                        portOfLoad: { name: "", location: "", locode: "" },
                      });
                      setShownValue("");
                      setSearchParams({});
                    }}
                  >
                    <RefreshCcw />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="mt-10">
          <Table className="border-1 border-[#D3DAE7]">
            <TableHeader>
              <TableRow className="bg-[#E5E8EF]">
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  SI #
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Submitted
                  <br /> Status
                </TableHead>
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  INTTRA SI
                </TableHead> */}
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Carrier
                </TableHead>
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Acknowledgment
                  <br /> Date (GMT)
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Acknowledgment
                  <br /> Status
                </TableHead> */}
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  SI INTTRA Status Date (GMT)
                </TableHead> */}
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  SI INTTRA
                  <br /> Status
                </TableHead> */}
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Carrier Bkg #
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Consignee Name
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  B/L Number
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Vessel
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  POL
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  POD
                </TableHead>
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">Shipper Company</TableHead> */}
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Requestor
                  <br /> Shipment ID
                </TableHead> */}
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Created By
                </TableHead>
                <TableHead className="p-3 w-[8%] font-bold text-[#191C36]">
                  Status <br />
                  Date (GMT)
                </TableHead>
                {/* <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Product
                  <br /> Channel
                </TableHead> */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {listShippingInstData?.pages?.map((page, i) => (
                <Fragment key={i}>
                  {page?.message?.data?.map((item, index) => {
                    const shipping = item?.shipping_instruction || {};
                    const draft = item.draft_inttra || {};
                    const value = shipping.name ?? `item-${index}`;
                    const matched = _siStatuses?.message?.data?.find(
                      (item: any) => item.inttra_si === shipping.name
                    );
                    return (
                      <TableRow key={index}>
                        <TableCell
                          className="p-3 cursor-pointer hover:underline text-blue-600"
                          onClick={() =>
                            navigate(
                              `/dashboard/booking/my-booking/create-si/${shipping.booking_request_id}&draft_id=${shipping.name}`
                            )
                          }
                        >
                          <Typography className="text-blue-600">
                            {shipping.name}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>{draft.submitted_status}</Typography>
                        </TableCell>
                        {/* <TableCell className="p-3">
                          <Typography>{draft.inttra_si}</Typography>
                        </TableCell> */}
                        <TableCell className="p-3">
                          <Typography>
                            {shipping.carrier_part_name || "—"}
                          </Typography>
                        </TableCell>
                        {/* <TableCell className="p-3">
                          {" "}
                          <Typography className="flex flex-col leading-tight">
                            {" "}
                            {matched?.si_carrier_status_date ? (
                              <>
                                <span>
                                  {" "}
                                  {dayjs(
                                    matched.si_carrier_status_date,
                                    "DD-MM-YYYY HH:mm"
                                  ).format("MMM-DD-YYYY")}{" "}
                                </span>{" "}
                                <span className="text-gray-500 text-xs">
                                  {" "}
                                  {dayjs(
                                    matched.si_carrier_status_date,
                                    "DD-MM-YYYY HH:mm"
                                  ).format("hh:mm A")}{" "}
                                </span>{" "}
                              </>
                            ) : (
                              "—"
                            )}{" "}
                          </Typography>{" "}
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>{matched?.si_carrier_status}</Typography>
                        </TableCell> */}
                        {/* <TableCell className="p-3">
                          <Typography>
                            {matched?.si_inttra_status_date
                              ? dayjs(
                                  matched?.si_inttra_status_date,
                                  "DD-MM-YYYY HH:mm"
                                ).format("MMM-DD-YYYY, hh:mm A")
                              : "—"}
                          </Typography>
                        </TableCell>*/}
                        {/* <TableCell className="p-3">
                          <Typography>{matched?.si_inttra_status}</Typography>
                        </TableCell> */}
                        <TableCell className="p-3">
                          <Typography>
                            {shipping.carrier_booking_number || "—"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {shipping?.consignee_name ? (
                              <span title={shipping.consignee_name}>
                                {shipping.consignee_name.length > 10
                                  ? `${shipping.consignee_name.slice(0, 10)}...`
                                  : shipping.consignee_name}
                              </span>
                            ) : (
                              "—"
                            )}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>{shipping.bl_no || "—"}</Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>{shipping.vessel || "—"}</Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {shipping.port_of_load_name || "—"}
                          </Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography>
                            {shipping.port_of_discharge_name || "—"}
                          </Typography>
                        </TableCell>
                        {/* <TableCell className="p-3">
                          <Typography>{shipping.custom_shipper_name || "—"}</Typography>
                        </TableCell> */}
                        {/* <TableCell className="p-3">
                          <Typography>{shipping.name}</Typography>
                        </TableCell> */}
                        <TableCell className="p-3">
                          <Typography>{shipping.owner}</Typography>
                        </TableCell>
                        <TableCell className="p-3">
                          <Typography className="flex flex-col leading-tight">
                            {shipping.creation ? (
                              <>
                                <span>
                                  {dayjs(shipping.creation).format(
                                    "MMM-DD-YYYY"
                                  )}
                                </span>
                                <span className="text-gray-500 text-xs">
                                  {dayjs(shipping.creation).format("hh:mm A")}
                                </span>
                              </>
                            ) : (
                              "—"
                            )}
                          </Typography>
                        </TableCell>
                        {/* <TableCell className="p-3">
                          <Typography>{draft.product_channel}</Typography>
                        </TableCell> */}
                      </TableRow>
                    );
                  })}
                </Fragment>
              ))}
              <TableCell colSpan={10} className="">
                {error ? (
                  <div className="flex justify-center py-20">
                    {/* @ts-ignore */}
                    {error?.status === 500 ? (
                      <Typography variant={"muted"} weight={"normal"}>
                        Something went wrong!
                      </Typography>
                    ) : (
                      <Typography variant={"muted"} weight={"normal"}>
                        {/* @ts-ignore */}
                        {error?.response?.data?.message?.error}
                      </Typography>
                    )}
                  </div>
                ) : (
                  <div
                    ref={loaderRef}
                    className="w-full h-[50dvh] flex justify-center items-center"
                  >
                    {isFetchingNextPage ? (
                      <div className="w-full flex justify-center">
                        <Loader />
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                )}
              </TableCell>
            </TableBody>
          </Table>
        </div>
      </>
      {error ? (
        <div className="flex justify-center py-20">
          {/* @ts-ignore */}
          {error?.status === 500 ? (
            <Typography variant={"muted"} weight={"normal"}>
              Something went wrong!
            </Typography>
          ) : (
            <Typography variant={"muted"} weight={"normal"}>
              {/* @ts-ignore */}
              {error?.response?.data?.message?.error}
            </Typography>
          )}
        </div>
      ) : (
        <div
          ref={loaderRef}
          className="w-full h-[50dvh] flex justify-center items-center"
        >
          {isFetchingNextPage ? (
            <div className="w-full flex justify-center">
              <Loader />
            </div>
          ) : (
            ""
          )}
        </div>
      )}
    </div>
  );
};

export default DraftSI;
