import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  CalendarIcon,
  DeleteIcon,
  PencilIcon,
  TrashIcon,
  X,
} from "lucide-react";
import { format } from "date-fns";
import { Card, CardContent } from "@/components/ui/card";
import cmdulogo from "@/assets/img/cmdu_logo.svg";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import {
  fetchDocketCreationDetails,
  createDocket,
  fetchDocketbasedContainers,
  updateFetchedListOfContainers,
  makeEquipmentInactive,
} from "@/services/admin/Dockets/create-docket";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import Loader from "@/components/Loader";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FormData {
  carrier_booking_number: string;
  booking: string;
  customer: string;
  customerDisplay: string;
  shipper: string;
  shipper_name: string;
  consignee: string;
  material: string;
  consignee_id: string;
  shipper_id: string;
  shippingDate: Date;
  invoice: string;
  hs_code: string;
  origin: string;
  originDisplay: string;
  destination: string;
  destinationDisplay: string;
  blno: string;
  destination_contact: string;
  telephone: string;
  terms: string;
  origin_of_goods: string;
  originOfGoodsDisplay: string;
  weight: string;
  contact: string;
  containers: string;
  shipline: string;
  country_of_import_export?: string;
  package_type?: string;
  package_count?: string;
  category?: string;
}

interface ContainerData {
  containerNo: string;
  sealNo: string;

  netWeight: string;
  equipmentId: string;
}

const validateContainerNumber = (containerNo: string): boolean => {
  if (containerNo.length === 0) return true;
  const regex = /^[A-Za-z]{4}\d{6,7}$/;
  return regex.test(containerNo);
};

export default function CreateDocketForm() {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    data: apiData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["DocketCreationDetails", id],
    queryFn: () =>
      fetchDocketCreationDetails({ carrier_booking_number: String(id) }),
    enabled: !!id,
  });

  useEffect(() => {
    if (apiData?.message?.status == 400) {
      toast.error(`No Booking Request found with Carrier Booking Number:${id}`);
    }
  }, [apiData]);

  interface DocketData {
    carrier_booking_number?: string;
    booking_id?: string;
    customer?: string;
    shipper?: string;
    shipper_id?: string;
    consignee_id?: string;
    shipper_name?: string;
    consignee_address?: string;
    material?: string;
    shipping_date?: string;
    hs_code?: string;
    origin?: string;
    destination?: string;
    destination_contact?: string;
    telephone?: string;
    origin_of_goods?: string;
    weight?: string | number;
    contact?: string;
    containers?: string;
    shipline?: string;
    bill_no?: string;
    invoice?: string;
    country_of_import_export?: string;
    package_type?: string;
    package_count?: string;
    category?: string;
  }

  const data: DocketData = apiData?.message?.data || {};

  const {
    data: containerListData,
    isLoading: isContainerLoading,
    isError: isContainerError,
    error: containerError,
    refetch: refetchContainers,
  } = useQuery({
    queryKey: ["ContainerListDetails", id],
    queryFn: () =>
      fetchDocketbasedContainers({ carrier_booking_number: String(id) }),
    enabled: !!id,
  });

  const containerList = containerListData?.message?.data?.equipments || [];

  const totalNetWeight = containerListData?.message?.net_weight || "";

  const [formData, setFormData] = useState<FormData>({
    carrier_booking_number: id || "",
    booking: "",
    consignee_id: "",
    shipper_id: "",
    customer: "",
    customerDisplay: "",
    shipper: "",
    shipper_name: "",
    consignee: "",
    material: "",
    shippingDate: new Date(),
    invoice: "",
    hs_code: "",
    origin: "",
    originDisplay: "",
    destination: "",
    destinationDisplay: "",
    blno: "",
    destination_contact: "",
    telephone: "",
    terms: "",
    origin_of_goods: "",
    originOfGoodsDisplay: "",
    weight: "",
    contact: "",
    containers: "",
    shipline: "",
    country_of_import_export: "",
    package_type: "",
    package_count: "",
    category: "",
  });

  const [containersData, setContainersData] = useState<ContainerData[]>([]);
  const [isEditingContainer, setIsEditingContainer] = useState(false);
  const [editContainerIndex, setEditContainerIndex] = useState<number | null>(
    null
  );
  const [editContainerData, setEditContainerData] = useState<ContainerData>({
    containerNo: "",
    sealNo: "",

    netWeight: "",
    equipmentId: "",
  });
  const [confirmDeleteContainer, setConfirmDeleteContainer] = useState(false);
  const [deleteContainerIndex, setDeleteContainerIndex] = useState<
    number | null
  >(null);

  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      const newFormData = {
        carrier_booking_number: data?.carrier_booking_number || id || "",
        booking: data?.booking_id || "",
        customer: data?.customer || "",
        customerDisplay: data?.customer || "",
        consignee_id: data?.consignee_id || "",
        shipper_id: data?.shipper_id || "",
        shipper: data?.shipper || "",
        shipper_name: data?.shipper_name || "",
        consignee: data?.consignee_address || "",
        material: data?.material || "",
        shippingDate: data?.shipping_date
          ? new Date(data.shipping_date)
          : new Date(),
        hs_code: data?.hs_code || "",
        origin: data?.origin || "",
        originDisplay: data?.origin || "",
        destination: data?.destination || "",
        destinationDisplay: data?.destination || "",
        destination_contact: data?.destination_contact || "",
        telephone: data?.telephone || "",
        origin_of_goods: data?.origin_of_goods || "",
        originOfGoodsDisplay: data?.origin_of_goods || "",
        weight: data?.weight || "",
        contact: data?.contact || "",
        containers: data?.containers || "",
        shipline: data?.shipline || "",
        blno: data?.bill_no || "",
        invoice: data?.invoice || "",
        country_of_import_export: data?.country_of_import_export || "",
        package_type: data?.package_type || "",
        package_count: data?.package_count || "",
        category: data?.category || "",
      };

      setFormData(newFormData);
    }
  }, [data, totalNetWeight, id]);

  useEffect(() => {
    if (containerList.length > 0) {
      const mappedContainers = containerList.map((container: any) => ({
        containerNo: container.equipment_name || "",
        sealNo: container.shipper_seal_number || "",

        netWeight: container.net_weight ? String(container.net_weight) : "",
        equipmentId: container.name || "",
      }));
      setContainersData(mappedContainers);
    }
  }, [containerList]);

  const createDocketMutation = useMutation({
    mutationFn: createDocket,
    onSuccess: (response) => {
      if (response?.data?.message?.status === 400) {
        toast.error(
          `No Shipping Instructions found with Carrier Booking Number:${id}`
        );
        return;
      }
      if (response?.data?.message?.status === 500) {
        toast.error(
          response?.data?.message?.message || "Failed to create docket"
        );
        return;
      }
      navigate("/dashboard/customers/customer-docket-list");
      toast.success("Docket created successfully");
    },
    onError: (error: Error) => {
      toast.error(`Error creating docket: ${error.message}`);
    },
  });

  const updateContainerMutation = useMutation({
    mutationFn: updateFetchedListOfContainers,
    onSuccess: (response) => {
      if (response.message?.status_code === 200) {
        toast.success("Container updated successfully");
        refetchContainers();
      } else {
        toast.error(response.message?.message || "Failed to update container");
      }
      setIsEditingContainer(false);
      setEditContainerIndex(null);
    },
    onError: (error: Error) => {
      toast.error(`Error updating container: ${error.message}`);
      setIsEditingContainer(false);
      setEditContainerIndex(null);
    },
  });

  const deleteContainerMutation = useMutation({
    mutationFn: makeEquipmentInactive,
    onSuccess: (response) => {
      if (response.message?.status_code === 200) {
        toast.success("Container deleted successfully");
        refetchContainers();
        refetch();
      } else {
        toast.error(response.message?.message || "Failed to delete container");
      }
      setConfirmDeleteContainer(false);
      setDeleteContainerIndex(null);
    },
    onError: (error: Error) => {
      toast.error(`Error deleting container: ${error.message}`);
      setConfirmDeleteContainer(false);
      setDeleteContainerIndex(null);
    },
  });

  const handleChange = (field: keyof FormData, value: string | Date) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleContainerEdit = (index: number) => {
    setEditContainerData(containersData[index]);
    setEditContainerIndex(index);
    setIsEditingContainer(true);
  };

  const handleContainerSave = () => {
    if (editContainerIndex === null) return;

    if (!validateContainerNumber(editContainerData.containerNo)) {
      return;
    }

    toast.custom((t) => (
      <div className="bg-white p-4 rounded-lg shadow-lg border">
        <p className="font-medium mb-4">Are you sure to update this data?</p>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" size="sm" onClick={() => toast.dismiss(t)}>
            No
          </Button>
          <Button
            size="sm"
            onClick={() => {
              const updatePayload = {
                equipment_id:
                  editContainerData.equipmentId ||
                  containersData[editContainerIndex].equipmentId,
                container_number:
                  editContainerData.containerNo ||
                  containersData[editContainerIndex].containerNo,
                seal_number:
                  editContainerData.sealNo ||
                  containersData[editContainerIndex].sealNo,
                net_weight: Number(
                  editContainerData.netWeight ||
                    containersData[editContainerIndex].netWeight
                ),

                booking_id: id || "",
              };

              updateContainerMutation.mutate(updatePayload);
              toast.dismiss(t);
            }}
          >
            Yes
          </Button>
        </div>
      </div>
    ));
  };

  const handleContainerCancel = () => {
    if (editContainerIndex === null) return;

    const hasChanges =
      editContainerData.containerNo !==
        containersData[editContainerIndex].containerNo ||
      editContainerData.sealNo !== containersData[editContainerIndex].sealNo ||
      editContainerData.netWeight !==
        containersData[editContainerIndex].netWeight ||
      editContainerData.equipmentId !==
        containersData[editContainerIndex].equipmentId;

    if (!hasChanges) {
      setIsEditingContainer(false);
      setEditContainerIndex(null);
      return;
    }

    toast.custom((t) => (
      <div className="bg-white p-4 rounded-lg shadow-lg border">
        <p className="font-medium mb-4">
          Are you sure to discard the entered data?
        </p>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" size="sm" onClick={() => toast.dismiss(t)}>
            No
          </Button>
          <Button
            size="sm"
            onClick={() => {
              setIsEditingContainer(false);
              setEditContainerIndex(null);
              toast.dismiss(t);
            }}
          >
            Yes
          </Button>
        </div>
      </div>
    ));
  };

  const handleContainerFieldChange = (
    field: keyof ContainerData,
    value: string
  ) => {
    setEditContainerData((prev) => ({ ...prev, [field]: value }));
  };

  const handleDeleteContainer = (index: number) => {
    setDeleteContainerIndex(index);
    setConfirmDeleteContainer(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // if (!formData.booking || !formData.customer) {
    //   toast.error("Please fill in all required fields");
    //   return;
    // }

    const longDescriptions =
      (formData?.package_type && formData.package_type.length > 140) || false;

    if (longDescriptions) {
      toast.error(
        `"Package Type" descriptions exceed 140 characters. Please shorten them before submitting.`
      );
      return;
    }

    toast.custom((t) => (
      <div className="bg-white p-4 rounded-lg shadow-lg border">
        <p className="font-medium mb-4">
          Are you sure to submit this entered data?
        </p>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" size="sm" onClick={() => toast.dismiss(t)}>
            No
          </Button>
          <Button
            size="sm"
            onClick={() => {
              const submitData = {
                carrier_booking_number: formData.carrier_booking_number,
                booking: formData.booking,
                consignee_id: formData.consignee_id,
                shipper_id: formData.shipper_id,
                customer: formData.customer,
                shipper: formData.shipper,
                shipper_name: formData.shipper_name,
                consignee: formData.consignee,
                material: formData.material,
                shipping_date: format(formData.shippingDate, "yyyy-MM-dd"),
                invoice: formData.invoice,
                hs_code: formData.hs_code,
                origin: formData.origin,
                destination: formData.destination,
                blno: formData.blno,
                destination_contact: formData.destination_contact,
                telephone: formData.telephone,
                terms: formData.terms,
                origin_of_goods: formData.origin_of_goods,
                contact: formData.contact,
                containers: formData.containers,
                weight: formData.weight,
                shipline: formData.shipline,
                booking_id: id || "",
                country_of_import_export: formData.country_of_import_export,
                package_type: formData.package_type,
                package_count: formData.package_count,
                category: formData.category,
              };

              createDocketMutation.mutate({ data: submitData });
              toast.dismiss(t);
            }}
          >
            Yes
          </Button>
        </div>
      </div>
    ));
  };

  if (isLoading || isContainerLoading) {
    return <Loader />;
  }

  if (isError || isContainerError) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : containerError instanceof Error
        ? containerError.message
        : "Unknown error";
    toast.error(`Error loading data: ${errorMessage}`);
    return (
      <div className="pt-6">
        <div className="border p-4 rounded-xs shadow-sm bg-white">
          <div className="text-center text-red-500">
            Error loading data: {errorMessage}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-6 overflow-y-auto">
      <div className="border p-4 rounded-xs shadow-sm bg-white">
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">File Information</h2>
          <div className="border-b border-gray-200" />
        </div>
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              label={
                <>
                  Booking <span className="text-red-600">*</span>
                </>
              }
              value={formData.carrier_booking_number}
              onChange={(v) => handleChange("carrier_booking_number", v)}
              required
              readOnly
            />
            <InputField
              label={
                <>
                  Customers <span className="text-red-600">*</span>
                </>
              }
              value={formData.customerDisplay}
              onChange={(v) => handleChange("customerDisplay", v)}
              readOnly
              required
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextareaField
              label="Shipper"
              value={`${formData.shipper_name || ""}${
                formData.shipper ? "\n" + formData.shipper : ""
              }`}
              onChange={(v) => handleChange("shipper", v)}
              disabled
            />

            <TextareaField
              label="Consignee"
              value={formData.consignee}
              onChange={(v) => handleChange("consignee", v)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <InputField
              label="Material"
              value={formData.material}
              onChange={(v) => handleChange("material", v)}
            />
            <InputField
              label="HS Code"
              value={formData.hs_code}
              onChange={(v) => handleChange("hs_code", v)}
            />
            <DateField
              label="Shipping Date"
              date={formData.shippingDate}
              onChange={(date) => handleChange("shippingDate", date)}
              style={{ height: "20px" }}
            />
            <InputField
              label="Invoice"
              value={formData.invoice}
              onChange={(v) => handleChange("invoice", v)}
              readOnly
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputField
              label="Destination"
              value={formData.destinationDisplay}
              onChange={(v) => handleChange("destinationDisplay", v)}
              readOnly
            />

            <InputField
              label="Origin"
              value={formData.originDisplay}
              onChange={(v) => handleChange("originDisplay", v)}
              readOnly
            />
            <InputField
              label="BL No"
              value={formData.blno}
              onChange={(v) => handleChange("blno", v)}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputField
              label="Destination Contact"
              value={formData.destination_contact}
              onChange={(v) => handleChange("destination_contact", v)}
            />
            <InputField
              label="Telephone"
              value={formData.telephone}
              onChange={(v) => handleChange("telephone", v)}
            />
            <div>
              <label className="block mb-1 text-sm font-medium">Terms</label>
              <Select
                value={formData.terms}
                onValueChange={(value) => handleChange("terms", value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select terms" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="FOB">FOB</SelectItem>
                  <SelectItem value="CNF">CNF</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputField
              label="Origin Of Goods"
              value={formData.originOfGoodsDisplay}
              onChange={(v) => handleChange("origin_of_goods", v)}
              readOnly
            />
            <InputField
              label="Country of Import/Export"
              value={formData.country_of_import_export ?? ""}
              onChange={(v) => handleChange("country_of_import_export", v)}
            />
            <InputField
              label="Contact"
              value={formData.contact}
              onChange={(v) => handleChange("contact", v)}
            />
            <InputField
              label="Package Type"
              value={formData.package_type ?? ""}
              onChange={(v) => handleChange("package_type", v)}
            />
            <InputField
              label="Package Count"
              value={formData.package_count ?? ""}
              onChange={(v) => handleChange("package_count", v)}
            />
            <InputField
              label="Weight"
              value={formData.weight ?? ""}
              onChange={(v) => handleChange("weight", v)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputField
              label="Containers"
              value={formData.containers}
              onChange={(v) => handleChange("containers", v)}
              readOnly
            />
            <InputField
              label="Shipline"
              value={formData.shipline}
              onChange={(v) => handleChange("shipline", v)}
              readOnly
            />
            <InputField
              label="Category"
              value={formData.category ?? ""}
              onChange={(v) => handleChange("category", v)}
              readOnly
            />
          </div>
          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-foreground text-white"
              disabled={createDocketMutation.isPending}
            >
              {createDocketMutation.isPending ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      </div>
      <div className="mt-6 text-lg font-semibold">Containers List</div>
      {containersData.map((container, index) => (
        <Card key={index} className="p-5 mt-6">
          <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-6 gap-2 p-2 text-xs items-center">
            <div className="flex flex-col items-center space-y-0 text-center">
              <p className="text-xs sm:text-sm text-gray-500 pb-2">
                Container No
              </p>
              {isEditingContainer && editContainerIndex === index ? (
                <div className="w-full">
                  <Input
                    className="h-8 text-xs"
                    value={editContainerData.containerNo}
                    onChange={(e) => {
                      const value = e.target.value.toUpperCase();
                      handleContainerFieldChange("containerNo", value);
                    }}
                    placeholder={container.containerNo}
                    maxLength={11}
                  />
                  {editContainerData.containerNo &&
                    !validateContainerNumber(editContainerData.containerNo) && (
                      <p className="text-red-500 text-xs mt-1">
                        Invalid format. Must be 4 letters followed by 6-7 digits
                        (e.g., ABCD1234567)
                      </p>
                    )}
                </div>
              ) : (
                <p className="font-semibold text-xs break-all">
                  {container.containerNo}
                </p>
              )}
            </div>

            <div className="flex flex-col items-center space-y-0 text-center">
              <p className="text-xs sm:text-sm text-gray-500 pb-2">Seal No</p>
              {isEditingContainer && editContainerIndex === index ? (
                <Input
                  className="h-8 text-xs"
                  value={editContainerData.sealNo}
                  onChange={(e) =>
                    handleContainerFieldChange("sealNo", e.target.value)
                  }
                  placeholder={container.sealNo}
                />
              ) : (
                <p className="font-semibold text-sm">
                  {container.sealNo ? container.sealNo : "-"}
                </p>
              )}
            </div>

            <div className="flex flex-col items-center space-y-0 text-center">
              <p className="text-xs sm:text-sm text-gray-500 pb-2">
                Net Weight
              </p>
              {isEditingContainer && editContainerIndex === index ? (
                <Input
                  className="h-8 text-xs"
                  value={editContainerData.netWeight}
                  onChange={(e) =>
                    handleContainerFieldChange("netWeight", e.target.value)
                  }
                  placeholder={container.netWeight}
                />
              ) : (
                <p className="font-semibold text-sm">
                  {container.netWeight ? container.netWeight : "-"}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2 mt-5">
              {isEditingContainer && editContainerIndex === index ? (
                <>
                  <button
                    className={`w-[156px] h-[43px] border border-[#D1D5DB] rounded-[3px] text-xs font-medium flex items-center justify-center sm:text-sm ${
                      !validateContainerNumber(editContainerData.containerNo) ||
                      editContainerData.containerNo.length === 0
                        ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                        : "bg-[#F9FAFC] text-black hover:bg-[#191C36] hover:text-white"
                    }`}
                    onClick={handleContainerSave}
                    disabled={
                      !validateContainerNumber(editContainerData.containerNo) ||
                      editContainerData.containerNo.length === 0
                    }
                  >
                    Save
                  </button>
                  <button
                    className="h-[43px] w-[43px] bg-[#F9FAFC] text-black border border-[#D1D5DB] rounded-[3px] hover:bg-red-500 hover:text-white transition-colors duration-200 flex items-center justify-center"
                    onClick={handleContainerCancel}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </>
              ) : (
                <button
                  className="w-[156px] h-[43px] bg-[#F9FAFC] text-black border border-[#D1D5DB] rounded-[3px] hover:bg-[#191C36] hover:text-white transition-colors duration-200 text-xs font-medium flex items-center justify-center sm:text-sm"
                  onClick={() => handleContainerEdit(index)}
                >
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </button>
              )}
              <button
                className="w-[156px] h-[43px] bg-orange-50 text-orange-600 border border-orange-300 rounded-[3px] hover:bg-orange-500 hover:text-white transition-colors duration-200 text-xs font-medium flex items-center justify-center sm:text-sm"
                onClick={() => handleDeleteContainer(index)}
                disabled={deleteContainerMutation.isPending}
              >
                <TrashIcon className="mr-2 h-4 w-4" />
                {deleteContainerMutation.isPending ? "Deleting..." : "Delete"}
              </button>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Delete Confirmation Dialog */}
      {confirmDeleteContainer && deleteContainerIndex !== null && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg max-w-sm shadow-md">
            <p className="text-gray-800 font-semibold mb-4">
              Are you sure you want to delete this container?
            </p>
            <p className="text-gray-600 text-sm mb-4">
              Container: {containersData[deleteContainerIndex]?.containerNo}
            </p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setConfirmDeleteContainer(false);
                  setDeleteContainerIndex(null);
                }}
                disabled={deleteContainerMutation.isPending}
              >
                No
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  const equipmentId =
                    containersData[deleteContainerIndex].equipmentId;
                  deleteContainerMutation.mutate(equipmentId);
                }}
                disabled={deleteContainerMutation.isPending}
                className="bg-orange-500 hover:bg-orange-400"
              >
                {deleteContainerMutation.isPending
                  ? "Deleting..."
                  : "Yes, Delete"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function InputField({
  label,
  value,
  onChange,
  type = "text",
  required = false,
  readOnly = false,
}: {
  label: React.ReactNode;
  value: string;
  onChange: (val: string) => void;
  type?: string;
  required?: boolean;
  readOnly?: boolean;
}) {
  return (
    <div>
      <label className="block mb-1 text-sm font-medium">{label}</label>
      <Input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required={required}
        readOnly={readOnly}
      />
    </div>
  );
}

function TextareaField({
  label,
  value,
  onChange,
  disabled = false,
}: {
  label: string;
  value: string;
  onChange: (val: string) => void;
  disabled?: boolean;
}) {
  return (
    <div>
      <label className="block mb-1 text-sm font-medium">{label}</label>
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
      />
    </div>
  );
}

function DateField({
  label,
  date,
  onChange,
}: {
  label: string;
  date: Date;
  onChange: (date: Date) => void;
}) {
  return (
    <div className="flex flex-col gap-1">
      <label className="text-sm font-medium">{label}</label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal h-11",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "dd-MM-yyyy") : <span>Pick a date</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date}
            onSelect={(d) => d && onChange(d)}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
