import { useState, useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X, PencilIcon, DeleteIcon, TrashIcon } from "lucide-react";
import { redirect, useParams, useSearchParams } from "react-router-dom";
import { fetchDocketDetails } from "@/services/admin/Dockets/docket-Details-View";
import { useQuery, useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import Loader from "@/components/Loader";
import { UpdateDocketDetails } from "@/services/admin/Dockets/docket-Details-Edit";

import { format, parse, isValid } from "date-fns";
import {
  fetchDocketbasedContainers,
  updateFetchedListOfContainers,
  makeEquipmentInactive,
} from "@/services/admin/Dockets/create-docket";
import { Card, CardContent } from "@/components/ui/card";

interface RightDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: "create" | "edit";
  carrierBooking?: string;
  DocketRevitionDate?: any[];
}

interface ContainerData {
  containerNo: string;
  sealNo: string;
  netWeight: string;
  equipmentId: string;
}

export function RightDrawer({
  open,
  onOpenChange,
  mode,
  carrierBooking,
  DocketRevitionDate,
}: RightDrawerProps) {
  interface FormData {
    shipper: { address: string };
    consignee: { addressLine1: string };
    material: string;
    shipper_name: string;
    shippingDate: string;
    id: string;
    hsCode: string;
    docketStatus: string;
    origin: string;
    destination: string;
    blNo: string;
    destinationContact: string;
    telephone: string;
    terms: string;
    originOfGoods: string;
    weight: string;
    contact: string;
    containers: string;
    shipline: string;
    revisionNumber: string;
    comment: string;
    country_of_import_export: string;
    package_count: string;
    package_type: string;
    category: string;
  }
  const [searchParams, setSearchParams] = useSearchParams();
  const checkType = searchParams.get("main_status") || "Submit";
  const [initialStatus, setInitialStatus] = useState<string>("Submit");
  const [formData, setFormData] = useState<FormData | null>(null);
  const [displayDate, setDisplayDate] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { id } = useParams();
  const [containerValidationError, setContainerValidationError] = useState("");
  const [confirmContainerUpdate, setConfirmContainerUpdate] = useState(false);
  const [confirmCancelEdit, setConfirmCancelEdit] = useState(false);
  const [confirmDeleteContainer, setConfirmDeleteContainer] = useState(false);
  const [revisionNumber, setRevisionNumber] = useState("");
  const [deleteContainerIndex, setDeleteContainerIndex] = useState<
    number | null
  >(null);
  const [containersData, setContainersData] = useState<ContainerData[]>([]);
  const [isEditingContainer, setIsEditingContainer] = useState(false);
  const [editContainerIndex, setEditContainerIndex] = useState<number | null>(
    null
  );
  const [editContainerData, setEditContainerData] = useState<ContainerData>({
    containerNo: "",
    sealNo: "",
    netWeight: "",
    equipmentId: "",
  });

  const {
    data: apiData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["DocketDetails", id],
    queryFn: () => fetchDocketDetails({ docket_id: String(id) }),
    enabled: !!id && open,
  });

  const {
    data: containerListData,
    isLoading: isContainerLoading,
    isError: isContainerError,
    error: containerError,
    refetch: refetchContainers,
  } = useQuery({
    queryKey: ["ContainerListDetails", carrierBooking],
    queryFn: () =>
      fetchDocketbasedContainers({
        carrier_booking_number: String(carrierBooking),
      }),
    enabled: !!carrierBooking,
  });

  const updateMutation = useMutation({
    mutationFn: (data: any) =>
      UpdateDocketDetails({
        docket_id: String(id),
        data: data,
      }),
    onSuccess: (response) => {
      if (response.message?.status === 500) {
        const errorMessage =
          response.message?.message?.error || "Failed to update docket";
        toast.error(errorMessage);
      } else {
        if (initialStatus === "Draft") {
          toast.success("Docket saved as Draft successfully");
        } else {
          toast.success("Docket updated successfully");
        }
        refetch();
        onOpenChange(false);
        if (checkType === "Draft") {
          redirect("/dashboard/customers/customer-docket-list?status=all");
        } else {

          if ((DocketRevitionDate?.length ?? 0) > 0) {

          if (revisionNumber === "0") {
            toast.info("Please generate the document before sending it.", {
              style: {
                backgroundColor: "#ebc725ff",
                color: "#0f0e0eff",
                fontWeight: 500,
                fontSize: "14px",
                borderRadius: "0.5rem",
                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
              },
              position: "bottom-right",
              duration: 8000,
            });
          } else {
            toast.info("Please regenerate the document before sending it.", {
              style: {
                backgroundColor: "#ebc725ff",
                color: "#0f0e0eff",
                fontWeight: 500,
                fontSize: "14px",
                borderRadius: "0.5rem",
                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
              },
              position: "bottom-right",
              duration: 8000,
            });
          }
        }
        }
      }
    },
    onError: (error) => {
      toast.error(
        `Failed to update docket: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    },
  });

  const updateContainerMutation = useMutation({
    mutationFn: updateFetchedListOfContainers,
    onSuccess: (response) => {
      toast.dismiss();
      if (response.message?.status_code === 200) {
        toast.success("Container updated successfully");
        setTimeout(() => {
          refetchContainers();
        }, 300);
      }
      setIsEditingContainer(false);
      setEditContainerIndex(null);
      setContainerValidationError("");
    },
    onError: (error: Error) => {
      toast.dismiss();
      toast.error(`Error updating container: ${error.message}`);
      setIsEditingContainer(false);
      setEditContainerIndex(null);
      setContainerValidationError("");
    },
  });

  const deleteContainerMutation = useMutation({
    mutationFn: makeEquipmentInactive,
    onSuccess: (response) => {
      toast.dismiss();
      if (response.message?.status_code === 200) {
        toast.success("Container deleted successfully");
        // formData?.containers && handleChange("containers", "");
        setTimeout(() => {
          refetchContainers();
          refetch();
        }, 300);
      } else {
        toast.error(response.message?.message || "Failed to delete container");
      }
      setConfirmDeleteContainer(false);
      setDeleteContainerIndex(null);
    },
    onError: (error: Error) => {
      toast.dismiss();
      toast.error(`Error deleting container: ${error.message}`);
      setConfirmDeleteContainer(false);
      setDeleteContainerIndex(null);
    },
  });

  const containerList = containerListData?.message?.data?.equipments || [];

  useEffect(() => {
    if (containerList.length > 0) {
      const mappedContainers = containerList.map((container: any) => ({
        containerNo: container.equipment_name || "",
        sealNo: container.shipper_seal_number || "",
        netWeight: container.net_weight ? String(container.net_weight) : "",
        equipmentId: container.name || "",
      }));
      setContainersData(mappedContainers);
    }
  }, [containerList]);

  useEffect(() => {
    if (apiData?.message?.data) {
      const data = apiData.message.data;
      setRevisionNumber(String(data.revision_number) || "");
      const formattedDate = formatDisplayDate(data.shipping_date || "");

      setFormData({
        shipper: { address: data.shipper || "" },
        consignee: { addressLine1: data.consignee || "" },
        material: data.material || "",
        shipper_name: data.shipper_name || "",
        shippingDate: data.shipping_date || "",
        id: data.invoice || "",
        hsCode: data.hs_code || "",
        docketStatus: data?.status || "",
        origin: data?.origin || "",
        destination: data?.destination || "",
        blNo: data.blno || "",
        destinationContact: data.destination_contact || "",
        telephone: data.telephone || "",
        terms: data.terms || "",
        originOfGoods: data?.origin_of_goods || "",
        weight: data.weight || "",
        contact: data.contact || "",
        containers: data.containers || "",
        shipline: data.shipline || "",
        revisionNumber: data.revision_number || "",
        comment: data.comment || "",
        country_of_import_export: data.country_of_import_export || "",
        package_count: data.package_count || "",
        package_type: data.package_type || "",
        category: data.category || "",
      });
      setDisplayDate(formattedDate);
    }
  }, [apiData]);

  // Calculate total net weight
  const totalNetWeight = containersData.reduce((total, container) => {
    const weight = parseFloat(container.netWeight) || 0;
    return total + weight;
  }, 0);

  // Check if weight matches total net weight
  const isWeightValid = () => {
    if (!formData?.weight) return true; // If no weight is entered, consider it valid
    const enteredWeight = parseFloat(formData.weight) || 0;
    return Math.abs(enteredWeight - totalNetWeight) < 0.01; // Allow small floating point differences
  };

  const formatDisplayDate = (dateStr: string) => {
    if (!dateStr) return "";
    try {
      const date = parse(dateStr, "yyyy-MM-dd", new Date());
      return isValid(date) ? format(date, "dd/MM/yyyy") : dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  const parseInputDate = (dateStr: string) => {
    if (!dateStr) return "";
    try {
      const date = parse(dateStr, "dd/MM/yyyy", new Date());
      return isValid(date) ? format(date, "yyyy-MM-dd") : dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  const [containerFieldError, setContainerFieldError] = useState("");

  const handleChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        [field]: value,
      };
    });
    if (field === "containers" && value === "") {
      setContainerFieldError("Containers field is required");
    } else {
      setContainerFieldError("");
    }
  };

  const handleNestedChange = (
    parentField: keyof FormData,
    childField: string,
    value: string
  ) => {
    setFormData((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        [parentField]: {
          ...(typeof prev[parentField] === "object" &&
          prev[parentField] !== null
            ? prev[parentField]
            : {}),
          [childField]: value,
        },
      };
    });
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDisplayDate(value);

    const backendDate = parseInputDate(value);
    setFormData((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        shippingDate: backendDate,
      };
    });
  };

  const handleCalendarSelect = (date: Date | undefined) => {
    if (!date) return;

    const displayFormat = format(date, "dd/MM/yyyy");
    const backendFormat = format(date, "yyyy-MM-dd");

    setDisplayDate(displayFormat);
    setFormData((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        shippingDate: backendFormat,
      };
    });
  };

  const validateForm = (): { isValid: boolean; emptyFields: string[] } => {
    if (!formData) return { isValid: false, emptyFields: [] };

    const emptyFields: string[] = [];

    if (!formData.consignee?.addressLine1?.trim())
      emptyFields.push("Consignee");
    if (!formData.shippingDate?.trim()) emptyFields.push("Shipping Date");
    if (checkType !== "Draft" && !formData.id?.trim())
    emptyFields.push("Invoice");
    if (!formData.blNo?.trim()) emptyFields.push("BL No");
    if (!formData.destinationContact?.trim())
      emptyFields.push("Destination Contact");
    if (!formData.terms?.trim()) emptyFields.push("Terms");
    if (!formData.originOfGoods?.trim()) emptyFields.push("Origin Of Goods");
    if (!formData.weight?.trim()) emptyFields.push("Weight");
    if (!formData.contact?.trim()) emptyFields.push("Contact");
    if (checkType !== "Draft" && !formData.category?.trim()) emptyFields.push("Category");
    if (!formData.containers?.trim()) emptyFields.push("Containers");

    return {
      isValid: emptyFields.length === 0,
      emptyFields,
    };
  };

  const clearFormData = () => {
    setFormData({
      shipper: { address: "" },
      consignee: { addressLine1: "" },
      material: "",
      shipper_name: "",
      shippingDate: "",
      id: "",
      hsCode: "",
      docketStatus: "",
      origin: "",
      destination: "",
      blNo: "",
      destinationContact: "",
      telephone: "",
      terms: "",
      originOfGoods: "",
      weight: "",
      contact: "",
      containers: "",
      shipline: "",
      revisionNumber: "",
      comment: "",
      country_of_import_export: "",
      package_count: "",
      package_type: "",
      category: "",
    });
    setDisplayDate("");
  };

  const performUpdate = () => {
    if (!formData) return;

    const submitData: any = {};

    if (formData.shipper?.address)
      submitData.shipper = formData.shipper.address;
    if (formData.shipper_name) submitData.shipper_name = formData.shipper_name;
    if (formData.consignee?.addressLine1)
      submitData.consignee = formData.consignee.addressLine1;
    if (formData.material) submitData.material = formData.material;
    if (formData.shippingDate) submitData.shipping_date = formData.shippingDate;
    if (formData.id) submitData.invoice = formData.id;
    if (formData.hsCode) submitData.hs_code = formData.hsCode;
    if (formData.docketStatus) submitData.status = formData.docketStatus;
    if (formData.origin) submitData.origin = formData.origin;
    if (formData.destination) submitData.destination = formData.destination;
    if (formData.blNo) submitData.blno = formData.blNo;
    if (formData.destinationContact)
      submitData.destination_contact = formData.destinationContact;
    if (formData.telephone) submitData.telephone = formData.telephone;
    if (formData.terms) submitData.terms = formData.terms;
    if (formData.originOfGoods)
      submitData.origin_of_goods = formData.originOfGoods;
    if (formData.contact) submitData.contact = formData.contact;
    if (formData.containers) submitData.containers = formData.containers;
    if (formData.shipline) submitData.shipline = formData.shipline;
    if (formData.revisionNumber)
      submitData.revision_number = formData.revisionNumber;
    if (formData.comment) submitData.comment = formData.comment;
    if (formData.country_of_import_export)
      submitData.country_of_import_export = formData.country_of_import_export;
    if (formData.package_count)
      submitData.package_count = formData.package_count;
    if (formData.package_type) submitData.package_type = formData.package_type;
    if (formData.category) submitData.category = formData.category;
    if (formData.weight) submitData.weight = formData.weight;
    if (initialStatus === "Draft") {
      submitData.submit_type = "Old";
    } else {
      submitData.submit_type = "Submit";
    }
    updateMutation.mutate(submitData);
  };

  const handleSubmit = () => {
    if (!formData) return;

    const validation = validateForm();
    if (!validation.isValid) {
      toast.error(
        `Please fill in all required fields: ${validation.emptyFields.join(
          ", "
        )}`
      );
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirmYes = () => {
    setShowConfirmation(false);
    performUpdate();
  };

  const handleConfirmNo = () => {
    setShowConfirmation(false);
  };

  const validateContainerNumber = (containerNo: string) => {
    const regex = /^[A-Za-z]{4}\d{6,7}$/;
    return regex.test(containerNo);
  };

  const handleContainerEdit = (index: number) => {
    setEditContainerData(containersData[index]);
    setEditContainerIndex(index);
    setIsEditingContainer(true);
    setContainerValidationError("");
  };
  const handleDeleteContainer = (index: number) => {
    setDeleteContainerIndex(index);
    setConfirmDeleteContainer(true);
  };

  const handleContainerSave = () => {
    if (editContainerIndex === null || updateContainerMutation.isPending)
      return;

    if (!validateContainerNumber(editContainerData.containerNo)) {
      setContainerValidationError(
        "Invalid format. Must be 4 letters followed by 6-7 digits (e.g., ABCD1234567)"
      );
      return;
    }

    setConfirmContainerUpdate(true);
  };

  const handleContainerCancel = () => {
    if (editContainerIndex === null) return;

    const hasChanges =
      editContainerData.containerNo !==
        containersData[editContainerIndex].containerNo ||
      editContainerData.sealNo !== containersData[editContainerIndex].sealNo ||
      editContainerData.netWeight !==
        containersData[editContainerIndex].netWeight ||
      editContainerData.equipmentId !==
        containersData[editContainerIndex].equipmentId;

    if (!hasChanges) {
      setIsEditingContainer(false);
      setEditContainerIndex(null);
      setContainerValidationError("");
      return;
    }

    setConfirmCancelEdit(true);
  };

  const handleContainerFieldChange = (
    field: keyof ContainerData,
    value: string
  ) => {
    setEditContainerData((prev) => ({ ...prev, [field]: value }));

    if (field === "containerNo") {
      if (!value) {
        setContainerValidationError("");
      } else {
        const isValid = validateContainerNumber(value);
        setContainerValidationError(
          isValid
            ? ""
            : "Invalid format. Must be 4 letters followed by 6-7 digits (e.g., ABCD1234567)"
        );
      }
    }
  };

  if (isLoading || isContainerLoading) {
    return <Loader />;
  }

  if (isContainerError) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : containerError instanceof Error
        ? containerError.message
        : "Unknown error";
    toast.error(`Error loading data: ${errorMessage}`);
    return (
      <div className="pt-6">
        <div className="border p-4 rounded-xs shadow-sm bg-white">
          <div className="text-center text-red-500">
            Error loading data: {errorMessage}
          </div>
        </div>
      </div>
    );
  }
  const handleCloseClick = () => {
    if (containerFieldError) {
      // setConfirmCancelEdit(true); // show modal to confirm discard
      toast.error(
        "Please resolve container field error and save before closing."
      );
    } else {
      onOpenChange(false);
    }
  };

  if (isLoading || updateMutation.isPending) {
    return <Loader />;
  }

  if (isError && !formData) {
    toast.error(
      `Error loading docket details: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
    return null;
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-40 transition-opacity duration-300" />
        <Dialog.Content
          onInteractOutside={(event) => {
            event.preventDefault();
          }}
          className="fixed right-0 top-0 h-full w-full max-w-full sm:max-w-2xl md:max-w-3xl lg:max-w-4xl bg-white shadow-lg z-50 transition-all duration-500 ease-in-out overflow-y-auto"
        >
          {showConfirmation && (
            <div className="fixed inset-0 bg-black/50 z-[60] flex items-center justify-center">
              <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Confirm Save
                </h3>
                <p className="text-gray-600 mb-6">
                  Are you sure you want to save the data?
                </p>
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleConfirmNo}
                    className="px-4 py-2"
                  >
                    No
                  </Button>
                  <Button
                    onClick={handleConfirmYes}
                    className="px-4 py-2"
                    disabled={updateMutation.isPending}
                  >
                    {updateMutation.isPending ? "Saving..." : "Yes"}
                  </Button>
                </div>
              </div>
            </div>
          )}

          <div className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
            <h2 className="text-lg font-medium">
              {mode === "edit" ? "Edit Dockets" : "Create Docket"} File
              Information
            </h2>
            <Dialog.Close asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={(e) => {
                  e.preventDefault();
                  handleCloseClick();
                }}
              >
                <X className="w-4 h-4" />
              </Button>
            </Dialog.Close>
          </div>

          {formData && (
            <div className="p-4 sm:p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Shipper
                  </label>
                  <Textarea
                    disabled
                    className="h-24 min-h-[6rem]"
                    value={`${formData.shipper_name || ""}\n${
                      formData?.shipper?.address || "N/A"
                    }`}
                    onChange={(e) =>
                      handleNestedChange("shipper", "address", e.target.value)
                    }
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Consignee *
                  </label>
                  <Textarea
                    className="h-24 min-h-[6rem]"
                    value={formData?.consignee?.addressLine1}
                    onChange={(e) =>
                      handleNestedChange(
                        "consignee",
                        "addressLine1",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Material
                  </label>
                  <Input
                    value={formData?.material || ""}
                    onChange={(e) => handleChange("material", e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    HS Code
                  </label>
                  <Input
                    value={formData?.hsCode || ""}
                    onChange={(e) => handleChange("hsCode", e.target.value)}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Shipping Date <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="date"
                    className="h-11"
                    value={
                      formData?.shippingDate
                        ? format(new Date(formData.shippingDate), "yyyy-MM-dd")
                        : ""
                    }
                    onChange={(e) => {
                      const newDate = e.target.value;
                      setFormData((prev) =>
                        prev
                          ? {
                              ...prev,
                              shippingDate: newDate,
                            }
                          : prev
                      );
                      setDisplayDate(
                        format(
                          parse(newDate, "yyyy-MM-dd", new Date()),
                          "dd/MM/yyyy"
                        )
                      );
                    }}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Invoice {checkType !== "Draft" && <span className="text-red-500">*</span>}
                  </label>
                  <Input
                    value={formData?.id}
                    onChange={(e) => handleChange("id", e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Origin
                  </label>
                  <Input
                    value={formData?.origin || "N/A"}
                    onChange={(e) => handleChange("origin", e.target.value)}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Destination
                  </label>
                  <Input
                    value={formData?.destination || "N/A"}
                    onChange={(e) =>
                      handleChange("destination", e.target.value)
                    }
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    BL No <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData?.blNo}
                    onChange={(e) => handleChange("blNo", e.target.value)}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Destination Contact <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData?.destinationContact}
                    onChange={(e) =>
                      handleChange("destinationContact", e.target.value)
                    }
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Terms <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData?.terms || "N/A"}
                    onChange={(e) => handleChange("terms", e.target.value)}
                    className="w-full h-[43px] border border-gray-200 text-sm rounded-[3px] text-gray-700 shadow-sm focus:border-gray-700 focus:ring-gray-700 pl-[10px]"
                  >
                    <option value="">Select Terms</option>
                    <option value="CNF">CNF</option>
                    <option value="FOB">FOB</option>
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Origin Of Goods <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData?.originOfGoods || ""}
                    onChange={(e) =>
                      handleChange("originOfGoods", e.target.value)
                    }
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Country of Import/Export
                  </label>
                  <Input
                    value={formData?.country_of_import_export || ""}
                    onChange={(e) =>
                      handleChange("country_of_import_export", e.target.value)
                    }
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Package Type
                  </label>
                  <Input
                    value={formData?.package_type || ""}
                    onChange={(e) =>
                      handleChange("package_type", e.target.value)
                    }
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Package Count
                  </label>
                  <Input
                    value={formData?.package_count || ""}
                    onChange={(e) =>
                      handleChange("package_count", e.target.value)
                    }
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Contact <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData?.contact || " "}
                    onChange={(e) => handleChange("contact", e.target.value)}
                  />
                </div>

                <div className="relative">
                  <label className="text-sm font-medium text-gray-700">
                    Containers <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData?.containers || ""}
                    onChange={(e) => handleChange("containers", e.target.value)}
                    placeholder="eg: 1x45G0"
                    className={`${
                      containerFieldError
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                  />
                  {containerFieldError && (
                    <p className="text-red-500 text-xs mt-1 absolute">
                      {containerFieldError}
                    </p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Shipline
                  </label>
                  <Input
                    disabled
                    value={formData?.shipline}
                    onChange={(e) => handleChange("shipline", e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Category {checkType !== "Draft" && <span className="text-red-500">*</span>}
                  </label>
                  <Input
                    value={formData?.category}
                    onChange={(e) => handleChange("category", e.target.value)}
                  />
                </div>
                <div className="relative">
                  <label className="text-sm font-medium text-gray-700">
                    Weight(KG) <span className="text-red-500">*</span>
                  </label>
                  <Input
                    className={`${
                      !isWeightValid() && formData?.weight
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                    value={formData?.weight}
                    onChange={(e) => handleChange("weight", e.target.value)}
                  />
                  {!isWeightValid() && formData?.weight && (
                    <p className="text-red-500 text-xs mt-1 absolute">
                      Mismatch in the total weight from the BL and total
                      container weight
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end pt-4 gap-2">
                {checkType === "Draft" && (
                  <Button
                    id={"draftButton"}
                    type="submit"
                    variant="secondary"
                    onClick={(e) => {
                      setInitialStatus("Draft");
                      handleSubmit();
                    }}
                    disabled={updateMutation.isPending}
                    className="w-full sm:w-auto text-white"
                  >
                    {updateMutation.isPending ? "Saving..." : "Save to Draft"}
                  </Button>
                )}
                <Button
                  id={"submitButton"}
                  type="submit"
                  onClick={(e) => {
                    setInitialStatus("Submit");
                    handleSubmit();
                  }}
                  disabled={updateMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  {checkType === "Draft" ? (
                    <>
                      {updateMutation.isPending ? "Saving..." : "Final Submit"}
                    </>
                  ) : (
                    <>
                      {updateMutation.isPending ? "Saving..." : "Save & Submit"}
                    </>
                  )}
                </Button>
              </div>
              <div className="mt-8 mb-4 border-t pt-6">
                <h2 className="text-lg font-semibold text-gray-800">
                  Containers List
                </h2>
              </div>

              <div className="space-y-4">
                {containersData.map((container, index) => (
                  <Card key={index} className="p-4 sm:p-5 mt-4 sm:mt-6">
                    <CardContent>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 items-end text-xs ">
                        <div className="flex flex-col space-y-1 relative">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Container No
                          </p>
                          {isEditingContainer &&
                          editContainerIndex === index ? (
                            <div className="relative">
                              <Input
                                className={`h-8 text-xs ${
                                  containerValidationError
                                    ? "border-red-500 focus:ring-red-500"
                                    : ""
                                }`}
                                value={editContainerData.containerNo}
                                onChange={(e) =>
                                  handleContainerFieldChange(
                                    "containerNo",
                                    e.target.value
                                  )
                                }
                                placeholder={container.containerNo}
                                maxLength={11}
                              />
                              {containerValidationError && (
                                <p className="text-red-500 text-xs mt-1 absolute whitespace-nowrap">
                                  {containerValidationError}
                                </p>
                              )}
                            </div>
                          ) : (
                            <p className="font-semibold text-sm break-all">
                              {container.containerNo}
                            </p>
                          )}
                        </div>

                        <div className="flex flex-col space-y-1 relative">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Seal No
                          </p>
                          {isEditingContainer &&
                          editContainerIndex === index ? (
                            <div className="relative">
                              <Input
                                className="h-8 text-xs"
                                value={editContainerData.sealNo}
                                onChange={(e) =>
                                  handleContainerFieldChange(
                                    "sealNo",
                                    e.target.value
                                  )
                                }
                                placeholder={container.sealNo}
                              />
                            </div>
                          ) : (
                            <p className="font-semibold text-sm">
                              {container.sealNo || "-"}
                            </p>
                          )}
                        </div>

                        <div className="flex flex-col space-y-1 relative">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Net Weight
                          </p>
                          {isEditingContainer &&
                          editContainerIndex === index ? (
                            <div className="relative">
                              <Input
                                className="h-8 text-xs"
                                value={editContainerData.netWeight}
                                onChange={(e) =>
                                  handleContainerFieldChange(
                                    "netWeight",
                                    e.target.value
                                  )
                                }
                                placeholder={container.netWeight}
                              />
                            </div>
                          ) : (
                            <p className="font-semibold text-sm">
                              {container.netWeight || "-"}
                            </p>
                          )}
                        </div>

                        <div className="flex justify-end gap-2 mt-3 sm:mt-5">
                          {isEditingContainer &&
                          editContainerIndex === index ? (
                            <>
                              <button
                                className={`w-[80px] sm:w-[100px] h-8 sm:h-[36px] bg-[#F9FAFC] text-black border border-[#D1D5DB] rounded-[3px] hover:bg-[#191C36] hover:text-white text-xs font-medium flex items-center justify-center ${
                                  containerValidationError
                                    ? "opacity-50 cursor-not-allowed"
                                    : ""
                                }`}
                                onClick={handleContainerSave}
                                disabled={!!containerValidationError}
                              >
                                Save
                              </button>
                              <button
                                className="h-8 w-8 sm:h-[36px] sm:w-[36px] bg-[#F9FAFC] text-black border border-[#D1D5DB] rounded-[3px] hover:bg-red-500 hover:text-white flex items-center justify-center"
                                onClick={handleContainerCancel}
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </>
                          ) : (
                            <>
                              <button
                                className="w-[80px] sm:w-[100px] h-8 sm:h-[36px] bg-[#F9FAFC] text-black border border-[#D1D5DB] rounded-[3px] hover:bg-[#191C36] hover:text-white text-xs font-medium flex items-center justify-center"
                                onClick={() => handleContainerEdit(index)}
                              >
                                <PencilIcon className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                                Edit
                              </button>
                              <button
                                className="w-[80px] sm:w-[100px] h-8 sm:h-[36px] bg-orange-100 text-orange-600 border border-orange-200 rounded-[3px] hover:bg-orange-500 hover:text-white text-xs font-medium flex items-center justify-center"
                                onClick={() => handleDeleteContainer(index)}
                                disabled={deleteContainerMutation.isPending}
                              >
                                <TrashIcon className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                                {deleteContainerMutation.isPending
                                  ? "Deleting..."
                                  : "Delete"}
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Total Net Weight Row */}
                {containersData.length > 0 && (
                  <Card className="p-4 sm:p-5 mt-4 sm:mt-6 bg-gray-50">
                    <CardContent>
                      <div className="flex justify-between items-end text-xs min-h-[70px]">
                        {/* Left side - Total Containers */}
                        <div className="flex flex-col space-y-1">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Total Containers
                          </p>
                          <p className="font-semibold text-sm">
                            {containersData.length}
                          </p>
                        </div>

                        {/* Right side - Total Net Weight */}
                        <div className="flex flex-col space-y-1 text-right">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Total Net Weight
                          </p>
                          <p className="font-semibold text-sm">
                            {totalNetWeight.toFixed(2)} KG
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {confirmContainerUpdate && (
            <div className="fixed inset-0 z-[70] bg-black/50 flex items-center justify-center items-start pt-20">
              <div className="bg-white p-6 rounded-lg max-w-sm shadow-md">
                <p className="text-gray-800 font-semibold mb-4">
                  Are you sure to update this container?
                </p>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setConfirmContainerUpdate(false)}
                  >
                    No
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      const container = containersData[editContainerIndex!];
                      const updatePayload = {
                        equipment_id:
                          editContainerData.equipmentId ||
                          container.equipmentId,
                        container_number:
                          editContainerData.containerNo ||
                          container.containerNo,
                        seal_number:
                          editContainerData.sealNo || container.sealNo,
                        net_weight: Number(
                          editContainerData.netWeight || container.netWeight
                        ),

                        booking_id: id || "",
                      };

                      updateContainerMutation.mutate(updatePayload);
                      setConfirmContainerUpdate(false);
                    }}
                  >
                    Yes
                  </Button>
                </div>
              </div>
            </div>
          )}

          {confirmCancelEdit && (
            <div className="fixed inset-0 z-[70] bg-black/50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg max-w-sm shadow-md">
                <p className="text-gray-800 font-semibold mb-4">
                  Are you sure you want to discard the entered data?
                </p>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setConfirmCancelEdit(false)}
                  >
                    No
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      setIsEditingContainer(false);
                      setEditContainerIndex(null);
                      setContainerValidationError("");
                      setConfirmCancelEdit(false);
                    }}
                  >
                    Yes
                  </Button>
                </div>
              </div>
            </div>
          )}

          {confirmDeleteContainer && deleteContainerIndex !== null && (
            <div className="fixed inset-0 z-[70] bg-black/50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg max-w-sm shadow-md">
                <p className="text-gray-800 font-semibold mb-4">
                  Are you sure you want to delete this container?
                </p>
                <p className="text-gray-600 text-sm mb-4">
                  Container: {containersData[deleteContainerIndex]?.containerNo}
                </p>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setConfirmDeleteContainer(false);
                      setDeleteContainerIndex(null);
                    }}
                    disabled={deleteContainerMutation.isPending}
                  >
                    No
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      const equipmentId =
                        containersData[deleteContainerIndex].equipmentId;
                      deleteContainerMutation.mutate(equipmentId);
                    }}
                    disabled={deleteContainerMutation.isPending}
                    className="bg-orange-500 hover:bg-orange-400"
                  >
                    {deleteContainerMutation.isPending
                      ? "Deleting..."
                      : "Yes, Delete"}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
