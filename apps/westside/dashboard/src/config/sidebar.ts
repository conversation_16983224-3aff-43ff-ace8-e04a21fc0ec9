import dayjs from "dayjs";
import {
  Globe2,
  Home,
  FileChartColumnIncreasing,
  Users,
  FileText,
  LucideIcon,
  Folder,
  File,
  Receipt,
  CalendarClock,
} from "lucide-react";

export const MenuItems: {
  title: string;
  url: string;
  icon: LucideIcon;
  roles: ("Admin" | "Vendor" | "Customer")[];
  subMenu?: {
    title: string;
    url: string;
    roles?: ("Admin" | "Vendor" | "Customer")[];
  }[];
}[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Home,
    roles: ["Admin", "Vendor", "Customer"],
  },
  {
    title: "Upcoming Tasks",
    url: "#",
    icon: CalendarClock,
    subMenu: [
      {
        title: "Draft SI",
        url: "/dashboard/upcoming-task/draft-si",
      },
      {
        title: "Draft Dockets",
        url: "/dashboard/upcoming-task/draft-dockets",
      },
    ],
    roles: ["Admin"],
  },
  {
    title: "Vendor",
    url: "#",
    icon: Users,
    subMenu: [
      {
        title: "Vendors",
        url: "/dashboard/vendors/vendors-list",
      },
      {
        title: "Jobs",
        url: "/dashboard/jobs?tab=list",
      },
      {
        title: "Bills",
        url: "/dashboard/vendors/bills",
      },
    ],
    roles: ["Admin"],
  },
  {
    title: "Customer",
    url: "#",
    icon: Users,
    subMenu: [
      {
        title: "Customers",
        url: "/dashboard/customers/list",
      },
      {
        title: "Dockets",
        url: "/dashboard/customers/customer-docket-list",
      },
      {
        title: "Invoice",
        url: "/dashboard/customers/customer-invoice-list",
      },
    ],
    roles: ["Admin"],
  },

  {
    title: "Booking",
    url: "#",
    icon: Globe2,
    subMenu: [
      {
        title: "Create Booking",
        url: "/dashboard/booking/booking-request",
        roles: ["Admin"],
      },
      {
        title: "My Booking",
        url: "/dashboard/booking/my-booking",
      },
      {
        title: "Template",
        url: "/dashboard/booking/template",
        roles: ["Admin"],
      },
    ],
    roles: ["Admin", "Vendor", "Customer"],
  },
  {
    title: "Documentation",
    url: "#",
    icon: FileChartColumnIncreasing,
    subMenu: [
      {
        title: "Create SI",
        url: "/dashboard/booking/my-booking/create-si",
      },
      {
        title: "Shipping Instruction",
        url: "/dashboard/documentation/my-shipping-instruction",
      },
      {
        title: "Bill Of Lading",
        url: "/dashboard/documentation/bill-of-ladding",
      },
      {
        title: "eVGM",
        url: "/dashboard/documentation/eVGM-workspace",
      },
      {
        title: "Create eVGM",
        url: "/dashboard/documentation/create-eVGM",
      },
    ],
    roles: ["Admin"],
  },
  {
    title: "Track",
    url: "#",
    icon: Users,
    subMenu: [
      {
        title: "Track Containers",
        url: "/dashboard/track/list",
      },
    ],
    roles: ["Admin"],
  },
  {
    title: "Plan",
    url: "#",
    icon: FileChartColumnIncreasing,
    subMenu: [
      {
        title: "Ocean Schedules",
        url: "/dashboard/plan/ocean-schedules",
      },
    ],
    roles: ["Admin"],
  },

  {
    title: "Jobs",
    url: "/dashboard/vendors/jobs",
    icon: Users,
    roles: ["Vendor"],
  },
  {
    title: "Dockets",
    url: "/dashboard/customer/docketList",
    icon: Folder,
    roles: ["Customer"],
  },
  {
    title: "Bills",
    url: "/dashboard/vendor/bills",
    icon: File,
    roles: ["Vendor"],
  },
  {
    title: "Invoice",
    url: "/dashboard/customer/invoiceList",
    icon: Receipt,
    roles: ["Customer"],
  },
  {
    title: "Reports",
    url: "#",
    icon: FileText,
    roles: ["Admin", "Customer", "Vendor"],
    subMenu: [
      {
        title: "Customer Shipment Reports",
        url: `/dashboard/reports/customer?sd=${dayjs().subtract(1, "month").format("YYYY-MM-DD")}&ed=${dayjs().format(
          "YYYY-MM-DD"
        )}`,
        roles: ["Admin", "Customer"],
      },
      {
        title: "Vendor Shipment Reports",
        url: `/dashboard/reports/vendor?sd=${dayjs().subtract(1, "month").format("YYYY-MM-DD")}&ed=${dayjs().format(
          "YYYY-MM-DD"
        )}`,
        roles: ["Admin", "Vendor"],
      },
      {
        title: "Invoice Shipment Reports",
        url: `/dashboard/reports/invoice?sd=${dayjs().subtract(1, "month").format("YYYY-MM-DD")}&ed=${dayjs().format(
          "YYYY-MM-DD"
        )}`,
        roles: ["Admin", "Customer"],
      },
      {
        title: "Bill Shipment Reports",
        url: `/dashboard/reports/bill?sd=${dayjs().subtract(1, "month").format("YYYY-MM-DD")}&ed=${dayjs().format(
          "YYYY-MM-DD"
        )}`,
        roles: ["Admin", "Vendor"],
      },
      // {
      //   title: "Product Shipment Reports",
      //   url: `/dashboard/reports/product?sd=${dayjs().subtract(1, "month").format("YYYY-MM-DD")}&ed=${dayjs().format(
      //     "YYYY-MM-DD"
      //   )}`,
      // },
    ],
  },
];
