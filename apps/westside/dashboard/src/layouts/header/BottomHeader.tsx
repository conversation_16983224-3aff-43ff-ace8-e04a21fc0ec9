// Icons
import { ChevronDown, Search, SidebarCloseIcon,X } from "lucide-react";
// custom component
import { Typography } from "@/components/typography";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";
import { MenuItems } from "@/config/sidebar";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import { Link } from "react-router-dom";
import GlobalSearchDrawer from "./globalSearchDrawer";
import { useState } from "react";

const BottomHeader = () => {
  const { role } = useAuthContext();
  const [open, setOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchValue, setSearchValue] = useState("");

  return (
    <div className="h-10 bg-sidebar px-2">
      <div className="h-full flex justify-between gap-2 items-center">
        <Menubar className="bg-transparent text-sidebar-foreground border-0  divide-sidebar-foreground">
          {MenuItems?.map((item) => {
            if (
              item?.roles.includes(
                (role as "Admin" | "Vendor" | "Customer") || "Customer"
              )
            ) {
              return (
                <MenubarMenu key={item?.title}>
                  <MenubarTrigger className="">
                    <Link className="text-white text-[15px]" to={item.url}>
                      {item?.title}
                    </Link>
                  </MenubarTrigger>
                  {item?.subMenu && (
                    <MenubarContent>
                      {item?.subMenu?.map((subItem) => {
                        if (!subItem?.roles) {
                          return (
                            <MenubarItem asChild>
                              <Link to={subItem?.url}>{subItem?.title}</Link>
                            </MenubarItem>
                          );
                        } else if (
                          subItem?.roles &&
                          subItem?.roles?.includes(
                            (role as "Admin" | "Vendor" | "Customer") ||
                              "Customer"
                          )
                        ) {
                          return (
                            <MenubarItem asChild>
                              <Link to={subItem?.url}>{subItem?.title}</Link>
                            </MenubarItem>
                          );
                        }
                      })}
                    </MenubarContent>
                  )}
                </MenubarMenu>
              );
            }
          })}
        </Menubar>
        {/* Right Part */}
        <div className="flex items-center gap-4 divide-x divide-sidebar-foreground">
          {role === "Admin" && (
            <div className="relative flex items-center pr-2">
              {/* Search wrapper */}
              <div
                className={`flex items-center ${showSearch && "bg-white"}  ${
                  showSearch && "rounded-sm"
                } overflow-hidden shadow-md transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)] ${
                  showSearch ? "w-72" : "w-9"
                }`}
              >
                {/* Left fixed search icon */}
                {/* <button
                  onClick={() => setShowSearch((prev) => !prev)}
                  className={`flex items-center justify-center w-9 h-9 ${
                    showSearch
                      ? "text-gray-600 hover:text-gray-800"
                      : "text-white hover:text-gray-300 cursor-pointer"
                  } transition`}
                >
                  {showSearch ? (
                    <SidebarCloseIcon size={18} />
                  ) : (
                    <Search size={18} />
                  )}
                </button> */}

                {/* Expanding input field */}
                <input
                  type="text"
                  id="header-search"
                  placeholder="Search..."
                  className={`bg-transparent text-gray-800 text-sm flex-1 px-2 outline-none transition-all duration-500 ${
                    showSearch ? "opacity-100 w-full" : "opacity-0 w-0"
                  }`}
                  autoFocus={showSearch}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                />
                {searchValue && (
                    <button
                      onClick={() => setSearchValue("")}
                      className="flex items-center justify-center text-gray-500 hover:text-gray-700 transition p-1"
                    >
                      <X size={16} />
                    </button>
                  )}
                <button
                  onClick={() => {
                    const val = searchValue.trim();
                    if (!val) return;
                    setSearchQuery(val);
                    setOpen(true);
                  }}
                  disabled={searchValue.trim().length < 5}
                  className={`px-4 py-2 text-sm font-medium rounded-r-sm transition-all duration-500
    ${
      searchValue.trim().length >= 5
        ? "bg-orange-500 text-white"
        : "bg-gray-300 text-gray-500 cursor-not-allowed"
    }
    ${
      showSearch
        ? "opacity-100 translate-x-0"
        : "opacity-0 translate-x-2 pointer-events-none"
    }
  `}
                >
                  Search
                </button>
              </div>
            </div>
          )}
          {/* <Typography
            variant={"p"}
            weight={"medium"}
            className="text-sidebar-primary-foreground cursor-pointer flex gap-0.5 items-center"
          >
            <span className="text-primary">EN</span> <ChevronDown size={10} />
          </Typography> */}
        </div>
      </div>
      <GlobalSearchDrawer
        open={open}
        onOpenChange={setOpen}
        searchQuery={searchQuery}
      />
    </div>
  );
};

export default BottomHeader;
