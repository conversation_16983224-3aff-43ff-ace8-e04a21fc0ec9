import { axiosClient } from "@/lib/axios";
import {
  CarrierBookingNumberSIType,
  CreateShippingInstructionInitialDataType,
  SIClausesType,
  SIEquipmentTypes,
  SITemplateListingType,
  TnTContainerSIType,
} from "@/types/shippingInstructions";

export const fetchShippingInstructionRequestInitialData = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      bookingId: string;
    }
  ];
}): Promise<{
  message: {
    message: CreateShippingInstructionInitialDataType;
  };
}> => {
  const [, { bookingId }] = queryKey;

  let api = `/method/westside.www.Web_API.create_shipping_instruction.get_shipping_instruction_data?booking_request_id=${bookingId}`;

  const response = await axiosClient(api);

  const respData: any = response?.data;

  return { message: respData };
};

export const fetchShippingInstructionRequestTrackNTraceData = async ({
  intraBookingReferenceNumber,
}: {
  intraBookingReferenceNumber: string;
}): Promise<{
  message: { message: TnTContainerSIType[] };
}> => {
  console.log(intraBookingReferenceNumber);

  let api = `/method/westside.www.API.track_and_trace.track_and_trace?carrier_booking_reference=${intraBookingReferenceNumber}`;

  const response = await axiosClient(api);

  const respData: any = response?.data;

  return { message: respData };
};

export const submitShippingInstructionRequest = async ({
  payload,
}: {
  payload: any;
}): Promise<{
  message: {
    message: {
      xml_content: {
        status: string;
        file_path: string;
        message: string;
      };
      status_code: number;
      message: string;
      data: string;
    };
  };
}> => {
  const token = localStorage.getItem("token");
  let api = `/method/westside.www.Web_API.create_shipping_instruction.create_shipping_request`;

  const response = await axiosClient.post(api, payload, {
    headers: {
      Authorization: `Basic ${token}`,
    },
  });

  const respData: any = response?.data;

  return { message: respData };
};

export const saveShippingInstructionRequestTemplate = async ({
  siName,
  siId,
  siTemplateData,
}: {
  siName: string;
  siId?: string;
  siTemplateData: any;
}): Promise<{
  message: any;
}> => {
  const token = localStorage.getItem("token");
  let api = `/method/westside.www.Web_API.create_shipping_instruction.create_si_template`;

  const response = await axiosClient.post(
    api,
    {
      template_name: siName,
      si_id: siId ?? undefined,
      si_data: siTemplateData,
    },
    {
      headers: {
        Authorization: `Basic ${token}`,
      },
    }
  );

  const respData: any = response?.data;

  return { message: respData };
};

export const fetchSITemplateListData = async ({
  queryKey,
}: {
  queryKey: [string, { page?: number; limit_page_length?: number; template_name?: string }];
}): Promise<{
  message: {
    message: {
      data: SITemplateListingType[];
      limit_page_length: number;
      limit_start: number;
      message: string;
      status: string;
      total_count: number;
      page: number;
      has_next: boolean;
    };
  };
}> => {
  const token = localStorage.getItem("token");
  const [, { page = 0, limit_page_length = 10, template_name = "" }] = queryKey;

  let api = `/method/westside.www.Web_API.create_shipping_instruction.list_si_templates?page=${page}&limit_page_length=${limit_page_length}`;

  if (template_name.trim()) {
    api += `&template_name=${encodeURIComponent(template_name.trim())}`;
  }

  const response = await axiosClient.get(api, {
    headers: {
      Authorization: `Basic ${token}`,
    },
  });

  const respData: any = response?.data;

  return { message: respData };
};

export const fetchSingleShippingInstructionTemplate = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      bookingId: string;
    }
  ];
}): Promise<{
  message: {
    data: {
      creation: string;
      modified: string;
      name: string;
      si_data: string;
      si_id: string | null;
      template_name: string;
    };
    message: string;
    status: string;
  };
}> => {
  try {
    const [, { bookingId }] = queryKey;
    let api = `/method/westside.www.Web_API.create_shipping_instruction.fetch_si_template_by_id?template_name=${bookingId}`;
    const response = await axiosClient.get(api);
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const fetchSIDocumentationClauses = async (): Promise<{
  message: {
    message: {
      message: string;
      status: string;
      data: SIClausesType[];
    };
  };
}> => {
  let api = `/method/westside.www.Web_API.currency_type.get_si_clauses`;

  const response = await axiosClient(api);

  const respData: any = response?.data;

  return { message: respData };
};

export const fetchEquipmentDetails = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      bookingId: string;
    }
  ];
}): Promise<{
  message: {
    status: string;
    message: string;
    data: SIEquipmentTypes[];
  };
}> => {
  try {
    const [, { bookingId }] = queryKey;
    let api = `/method/westside.www.Web_API.currency_type.get_equipment_details?booking_id=${bookingId}`;
    const response = await axiosClient.get(api);
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// amend SI Submit API.
export const amendShippingInstructionRequest = async ({
  payload,
}: {
  payload: any;
}): Promise<{
  message: {
    message: {
      xml_content: {
        status: string;
        file_path: string;
        message: string;
      };
      status_code: number;
      message: string;
      data: string;
    };
  };
}> => {
  const token = localStorage.getItem("token");
  let api = `/method/westside.www.Web_API.create_shipping_instruction.update_shipping_request`;

  const response = await axiosClient.post(api, payload, {
    headers: {
      Authorization: `Basic ${token}`,
    },
  });

  const respData: any = response?.data;

  return { message: respData };
};

// fetch Carrier Booking Number.
export const fetchCarrierBookingNumbers = async ({
  bookingId,
}: {
  bookingId: string;
}): Promise<{
  message: {
    status_code: number;
    message: string;
    data: CreateShippingInstructionInitialDataType;
  };
}> => {
  const token = localStorage.getItem("token");
  let api = `/method/westside.www.Web_API.create_shipping_instruction.search_carrier?carrier_booking_number=${bookingId}`;

  const response = await axiosClient.get(api, {
    headers: {
      Authorization: `Basic ${token}`,
    },
  });

  const respData: any = response?.data;

  return respData;
};

export const deleteSIRequestTemplate = async (tempId: string) => {
  try {
    const token = localStorage.getItem("token");
    const api = `/method/westside.www.Web_API.create_shipping_instruction.delete_si_template`;

    const response = await axiosClient.post(
      api,
      { template_name: tempId },
      {
        headers: {
          Authorization: `Basic ${token}`,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
