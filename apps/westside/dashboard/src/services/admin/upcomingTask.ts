import { axiosClient } from "@/lib/axios";
import { ShippingInstructionDraft } from "@/types/upcomingTask";

export const fetchDraftedSI = async ({
 pageParam,
  portOfLoad,
  carrier,
  consignee,
  searchText,
}: {
  pageParam: number;
  portOfLoad?: string;
  carrier?: string;
  consignee?: string;
  searchText?: string;
}): Promise<{
  message: ShippingInstructionDraft;
  status: string;
  status_code: number;
  page: number;
  limit_page_length: number;
  total_count: number;
  user: string;
}> => {
  // let filters = ``;
  // if (bookingStatus !== "all" && bookingStatus) {
  //   filters = `&status=${bookingStatus}`;
  // }
  // if (carrier !== "all" && carrier) {
  //   filters += `&carrier=${carrier}`;
  // }
  // if (search) {
  //   filters += `&search=${search}`;
  // }

  // if (sortBy === "bln_most_recent") {
  //   filters += `&bln_most_recent=True`;
  // } else if (sortBy === "bln_si_due_12_days") {
  //   filters += `&bln_si_due_12_days=True`;
  // }

  try {
    const token = localStorage.getItem("token");
    const api = `/method/westside.www.Web_API.create_shipping_instruction.draft_shipping_instructions_list?limit_page_length=20&page=${pageParam}`;
    const params: Record<string, any> = { };
    if (consignee) params.consignee = consignee;
    if (portOfLoad) params.port_of_loading = portOfLoad;
    if (carrier) params.carrier = carrier;
    if (searchText) params.carrier_booking_number = searchText;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params,
    });
    return response?.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};
