import { axiosClient } from "@/lib/axios";
import {
  BookingConfirmationType,
  BookingOceanSheduleDataTye,
  BookingRequestGeneralType,
  BookingTemplateType,
  LocationType,
  MyBookingResponseType,
  TrackAndTraceItemType,
  BookingDetailsType,
  BookingAmendBookingDataType,
} from "@/types/booking";
import { BookingRequestFormType } from "@/views/admins/booking/bookingRequest/schema";

export const fecthBasicBookingRequestData = async (): Promise<{
  message: BookingRequestGeneralType;
}> => {
  try {
    const api = `/method/westside.www.Web_API.booking_api.fetch_booking_data`;
    const response = await axiosClient.get(api);
    return response?.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const fetchMyBooking = async ({
  bookingStatus,
  carrier,
  search,
  sortBy,
  pageParam = 1,
}: {
  bookingStatus: string;
  carrier: string;
  search: string;
  sortBy: string;
  pageParam: number;
}): Promise<{
  message: MyBookingResponseType;
  page_size: number;
  total_count: number;
}> => {
  let filters = ``;
  if (bookingStatus !== "all" && bookingStatus) {
    filters = `&status=${bookingStatus}`;
  }
  if (carrier !== "all" && carrier) {
    filters += `&carrier=${carrier}`;
  }
  if (search) {
    filters += `&search=${search}`;
  }

  if (sortBy === "bln_most_recent") {
    filters += `&bln_most_recent=True`;
  } else if (sortBy === "bln_si_due_12_days") {
    filters += `&bln_si_due_12_days=True`;
  }

  try {
    const token = localStorage.getItem("token");
    const api = `/method/westside.www.Web_API.my_booking_api.get_booking_requests?page_size=10${filters}&page=${pageParam}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const fetchBookingOceanSchedule = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      originPort: string;
      destinationPort: string;
      carrier: string;
      searchDate: string;
    }
  ];
}): Promise<{
  message: BookingOceanSheduleDataTye[];
}> => {
  const [, { originPort, destinationPort, carrier, searchDate }] = queryKey;
  try {
    const token = localStorage.getItem("token");

    const api = `/method/westside.www.API.inttra_auth_api.get_ocean_schedule?originPort=${originPort}&destinationPort=${destinationPort}&scacs=${carrier}&searchDate=${searchDate}&type=BOOKING`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const fetchBookingConfirmation = async (
  id: string
): Promise<{
  message: BookingConfirmationType;
}> => {
  try {
    const token = localStorage.getItem("token");
    const api = `/method/westside.www.Web_API.view_booking_details.get_booking_request_details?name=${id}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const submitBookingRequest = async (formData: Record<string, any>) => {
  try {
    const token = localStorage.getItem("token");

    const api =
      "/method/westside.www.Web_API.my_booking_api.create_booking_request";
    const response = await axiosClient.post(api, JSON.stringify(formData), {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const submitEditBookingRequest = async ({
  formData,
  bookingId,
}: {
  formData: Record<string, any>;
  bookingId: string;
}) => {
  try {
    const token = localStorage.getItem("token");

    const api = `/method/westside.www.Web_API.my_booking_api.update_booking_request?name=${bookingId}`;

    const response = await axiosClient.post(api, JSON.stringify(formData), {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const submitBookingRequestTemplate = async (
  formData: Record<string, any>
) => {
  try {
    const token = localStorage.getItem("token");

    if (!formData?.template_name) {
      throw new Error("Please provide template name.");
    }
    const api =
      "/method/westside.www.Web_API.my_booking_api.create_template_or_update_template";

    const response = await axiosClient.post(api, formData, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const deleteBookingRequestTemplate = async (bookingId: string) => {
  try {
    const token = localStorage.getItem("token");
    const api = `/method/westside.www.Web_API.my_booking_api.delete_booking_template`;

    const response = await axiosClient.post(
      api,
      { template_id: bookingId },
      {
        headers: {
          Authorization: `Basic ${token}`,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const searchBookingTemplateByName = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      search: string;
    }
  ];
}): Promise<{
  message: { results: any[] };
}> => {
  try {
    const token = localStorage.getItem("token");

    const [, { search }] = queryKey;
    const api = `/method/westside.www.Web_API.my_booking_api.search_template`;
    const response = await axiosClient.post(
      api,
      { search_text: search },
      {
        headers: {
          Authorization: `Basic ${token}`,
        },
      }
    );
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const fetchBookingLocations = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      search: string;
    }
  ];
}): Promise<{
  message: { results: LocationType[] };
}> => {
  try {
    const [, { search }] = queryKey;
    const api = `/method/westside.www.Web_API.location_view.search_locations?search_text=${search}`;
    const response = await axiosClient.get(api);
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

// template_name → fetch single template
export const fetchBookingTemplate = async ({
  pageParam,
  searchText,
  count,
}: {
  pageParam: number;
  searchText: string;
  count: number;
}): Promise<{
  message: {
    status: string;
    mode: string;
    message: string;
    data: BookingTemplateType[];
    page: number;
    count: number;
    total_count: number;
  };
}> => {
  try {
    const api = `/method/westside.www.Web_API.my_booking_api.list_booking_template?page=${pageParam}&search_text=${searchText}&count=${count}`;
    const response = await axiosClient.get(api);
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const fetchSingleBookingTemplate = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      bookingId: string;
    }
  ];
}): Promise<{
  message: {
    data: {
      booking_data: BookingRequestFormType;
      creation: string;
      modified: string;
      name: string;
      template_name: string;
    };
    message: string;
    status: string;
  };
}> => {
  try {
    const token = localStorage.getItem("token");

    const [, { bookingId }] = queryKey;
    const api = `/method/westside.www.Web_API.my_booking_api.fetch_booking_template?template_id=${bookingId}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const fetchSingleBookingData = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      bookingId: string;
    }
  ];
}): Promise<{
  message: {
    status_code: number;
    message: string;
    data: BookingAmendBookingDataType;
  };
}> => {
  try {
    const token = localStorage.getItem("token");

    const [, { bookingId }] = queryKey;

    const api = `/method/westside.www.Web_API.my_booking_api.get_amend_details?name=${bookingId}`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const fetchTrackAndTrace = async ({
  queryKey,
}: {
  queryKey: [
    string,
    {
      inttra_booking_reference: string;
      carrier_booking_reference: string;
      bill_of_lading_number: string;
      equipment_reference: string;
    }
  ];
}): Promise<{ message: TrackAndTraceItemType }> => {
  const [
    ,
    {
      inttra_booking_reference,
      carrier_booking_reference,
      bill_of_lading_number,
      equipment_reference,
    },
  ] = queryKey;

  try {
    const token = localStorage.getItem("token");

    const api = `/method/westside.www.API.track_and_trace.track_and_trace`;
    const response = await axiosClient.get(api, {
      params: {
        inttra_booking_reference,
        carrier_booking_reference,
        bill_of_lading_number,
        equipment_reference,
      },
      headers: {
        Authorization: `Basic ${token}`,
      },
    });

    return response.data;
  } catch (err) {
    console.error("Error fetching track and trace:", err);
    throw err;
  }
};

export const fetchContainersDetails = async (
  id: string
): Promise<{
  message: { data: BookingDetailsType };
}> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.job.create_job_api.get_assigned_containers`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: {
        name: id,
      },
    });

    if (!response.data || response.data.status === 500) {
      throw new Error(response.data?.message || "Failed to update vendor");
    }

    return response.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const fetchBookingDeatils = async (
  id: string
): Promise<{
  message: BookingDetailsType;
}> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.job.create_job_api.get_booking_request`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: {
        booking_id: id,
      },
    });

    if (!response.data || response.data.status === 500) {
      throw new Error(response.data?.message || "Failed to update vendor");
    }

    return response.data.data || response.data;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const fetchTrackAndTraceMainPage = async (
  searchKey: string
): Promise<{ message: TrackAndTraceItemType }> => {
  try {
    const token = localStorage.getItem("token");

    const api = `/method/westside.www.API.track_and_trace.track_trace_main_page?broad_search_key=${searchKey}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });

    return response.data;
  } catch (err) {
    console.error("Error fetching track and trace:", err);
    throw err;
  }
};

export const fetchCarrierScacList = async () => {
  try {
    const api = `/method/westside.www.Web_API.carrier_scac_list.get_all_carriers?search_term=`;
    const response = await axiosClient.get(api);
    return response.data;
  } catch (err) {
    console.error("Error fetching track and trace:", err);
    throw err;
  }
};

export const CancelBooking = async (bookingID: string) => {
  try {
    const api = `/method/westside.westside.doctype.booking_request.booking_request.cancel_booking?name=${bookingID}`;

    const response = await axiosClient.post(api);
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
