import os
import json
import frappe
from frappe import _
from frappe.utils.pdf import get_pdf
from jinja2 import Environment, FileSystemLoader, StrictUndefined
from westside.www.Authentication.auth_decorators import role_required
 
 
@frappe.whitelist()
@role_required(["Customer"])
def get_all_docket_revisions(docket_id):
    try:
        if not docket_id:
            return {'status': 400, 'message': 'DocketId is required.'}

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': 404, 'message': 'Docket not found'}
        
        customer = frappe.session.user # current user
        customer_id = frappe.get_value("Customer DB", {"email_id": customer}, "name")
    
        if docket.customer_id != customer_id:
            return {"status": 400, "message": "You don't have permission to access this docket."}

        customer_flag_for_custom_docs = frappe.get_value("Customer DB", {"name": docket.customer_id}, "flag_for_custom_docs")
        
        if docket.docket_revisions:
            row = docket.docket_revisions[-1]

            site_url = frappe.utils.get_url()

            revisions_data = []
        
            # Fetch additional attachments for this revision
            additional_files = frappe.get_all(
                "Additional Revision Attachments",
                filters={"docket_revision": row.name},
                fields=["file_name", "attachments"]
            )

            # Convert to full URLs
            attachments_data = [
                {
                    "file_name": f.get("file_name"),
                    "url": site_url + f.get("attachments") if f.get("attachments") else "",
                }
                for f in additional_files
            ]
            
            # Common revision data
            revision_dict = {
                "revision_number": row.revision_number,
                "revision_name": row.name,
                "packing_list": site_url + row.packing_list if row.packing_list else "",
                "certificate_of_origin": site_url + row.certificate_of_origin if row.certificate_of_origin else "",
                "form9": site_url + row.form9 if row.form9 else "",
                "form6": site_url + row.form6 if row.form6 else "",
                "bill_of_ladding": site_url + row.bill_of_ladding if row.bill_of_ladding else "",
                "invoice": site_url + row.invoice if row.invoice else "",
                "status": row.status,
                "show_to_customer": row.show_to_customer,
                "creation": row.created_on,
                "customer_response_on": row.customer_response_on,
                "is_invoice_created": row.is_invoice_created,
                "is_custom_docs_needed": customer_flag_for_custom_docs,
                "additional_attachments": attachments_data
            }

            # Conditionally include ISCC documents
            if customer_flag_for_custom_docs:
                revision_dict.update({
                    "isccplus_self_declaration": site_url + row.isccplus_self_declaration if row.isccplus_self_declaration else "",
                    "iscc_self_declaration": site_url + row.iscc_self_declaration if row.iscc_self_declaration else "",
                })
    
            revisions_data.append(revision_dict)

            return {
                "status": 200,
                "message": "Docket revision sent successfully",
                "docket_id": docket.name,
                "revisions": revisions_data
            }
        else:
            return {'status': 400, 'message': 'No revisions found for this docket.'}

    except frappe.DoesNotExistError:
        return {"status": 404, "message": "Docket not found"}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Send docket API Error")
        return {"status": 500, "message": {"error": str(e)}}



@frappe.whitelist()
@role_required(["Customer"])
def update_docket_status(docket_id, revision_id, status):
    try:
        if not docket_id:
            return {'status': 400, 'message': 'DocketId is required.'}
        
        if not revision_id:
            return {"status": 400, "message": "Revision ID is required."}

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': 404, 'message': 'Docket not found'}
        

        customer = frappe.session.user # current user
        customer_id = frappe.get_value("Customer DB", {"email_id": customer}, "name")

        if docket.customer_id != customer_id:
            return {"status": 400, "message": "You don't have permission to access this docket."}

        # Search for child row within parent
        docket_revision = None
        for rev in docket.docket_revisions:
            if rev.revision_number == revision_id:
                docket_revision = rev
                break

        if not docket_revision:
            return {"status": 404, "message": "Docket Revision not found."}
        
        docket_revision.status = status
        docket.status = status
        docket.save(ignore_permissions=True)
        
        frappe.db.commit()

        if docket_revision.status == "Rejected":
            try:
                customer_doc = frappe.get_doc("Customer DB", docket.customer_id)
                carrier_booking_number = docket.carrier_booking_number if docket.carrier_booking_number else None

                subject = f"Rejected Docket Revison {docket.revision_number} - Shipment:{carrier_booking_number}  Bol: {docket.blno}  Docket: {docket.name}"

                link = f"{site_url}/dashboard/customer/dockets-Details-View/{docket.name}"

                
                message = (
                    f"Dear Administrator,<br><br>"
                    f"The {customer_doc.customer_name} has <strong style='color: #DC2626;'>rejected</strong> the docket "
                    f"<strong>Rev{docket.revision_number}</strong> for booking "
                    f"<strong>{carrier_booking_number}</strong>.<br><br>"
                    f"There might be some corrections or discrepancies in the submitted documents. "
                    f"Please review the comments provided by the customer in the comment section for more details.<br><br>"
                    f"You can view the docket and take necessary actions by logging into your Admin portal "
                    f"by clicking <a href='{link}' style='color: #1D4ED8;'>here</a>.<br><br><br>"
                    f"Thank.<br><br>"
                    f"<strong>Customer Team</strong><br>"
                    f"{customer_doc.customer_name}<br>"
                    f"{customer_doc.company_name}<br>"
                    f"{customer_doc.address}<br>"
                    f"{customer_doc.phone}<br>"
                    f"Email: <a href='mailto:{customer_doc.notification_email}'>{customer_doc.notification_email}</a>"
                )
                email_list = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ]
  
                frappe.enqueue(
                    "westside.api.email.send_docket_email_async",
                    queue="default",
                    timeout=300,
                    email_id=email_list,
                    subject=subject,
                    message=message
                )
            except Exception as e:
                pass
        
        elif docket_revision.status == "Accepted":
            try:
                customer_doc = frappe.get_doc("Customer DB", docket.customer_id)
                carrier_booking_number = docket.carrier_booking_number if docket.carrier_booking_number else None

                subject = f"Accepted Docket Revison {docket.revision_number} - Shipment:{carrier_booking_number}  Bol: {docket.blno}  Docket: {docket.name}"

                link = f"{site_url}/dashboard/customer/dockets-Details-View/{docket.name}"

                
                message = (
                    f"Dear Administrator,<br><br>"
                    f"The {customer_doc.customer_name} has <strong style='color: #16A34A;'>approved</strong> the docket "
                    f"<strong>Rev{docket.revision_number}</strong> for booking "
                    f"<strong>{carrier_booking_number}</strong>.<br><br>"
                    f"You can view the docket and take necessary actions by logging into your Admin portal "
                    f"by clicking <a href='{link}' style='color: #1D4ED8;'>here</a>.<br><br><br>"
                    f"Thank.<br><br>"
                    f"<strong>Customer Team</strong><br>"
                    f"{customer_doc.customer_name}<br>"
                    f"{customer_doc.company_name}<br>"
                    f"{customer_doc.address}<br>"
                    f"{customer_doc.phone}<br>"
                    f"Email: <a href='mailto:{customer_doc.notification_email}'>{customer_doc.notification_email}</a>"
                )
                email_list = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ]

                frappe.enqueue(
                    "westside.api.email.send_docket_email_async",
                    queue="default",
                    timeout=300,
                    email_id=email_list,
                    subject=subject,
                    message=message
                )
            except Exception as e:
                pass
            
        return { "status": 200, "message": "Docket revision status updated successfully", "docket": docket.name, "docket_revision": docket_revision.revision_number, "updated_status": status }

    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Update Docket status API Error")
        return {"status": 500, "message": {"error": str(e)}}
