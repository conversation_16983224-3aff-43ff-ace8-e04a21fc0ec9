import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils.response import build_response
from frappe.utils.response import json_handler
import json
from math import ceil

from westside.westside.doctype.booking_request.booking_request import amend_booking, send_booking_to_inttra
from westside.www.API.webhook import save_previous_booking_data
from westside.www.Authentication.auth_decorators import role_required

from datetime import datetime, timedelta


def parse_date(date_str):
    formats = ["%Y-%m-%d %H:%M", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%SZ", "%Y-%m-%d"]
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except (ValueError, TypeError):
            continue
    return None

@frappe.whitelist()
def get_booking_requests(status=None, from_date=None, to_date=None, search=None, carrier=None, page=1, page_size=10,bln_most_recent=False,bln_si_due_12_days=False):
    try:

        user = frappe.session.user
        roles = frappe.get_roles(user)
        # 
        filters = []
        or_filters = []
        if user in ["Administrator", "Admin"] or "System Manager" in roles:
            pass
        else:
            if "Vendor" in roles:
                vendor_name = frappe.db.get_value("Vendor", {"user_id": user}, "name")
                
                if not vendor_name:
                    frappe.response["http_status_code"] = 404
                    frappe.response["message"] = {"error": "No booking requests found."}
                    return
                
                bookings_names = frappe.get_all("Job", filters={"vendor_name": vendor_name}, pluck="booking_id")
                if not bookings_names:
                    frappe.response["http_status_code"] = 404
                    frappe.response["message"] = {"error": "No booking requests found."}
                    return
                
                filters.append(["name", "in", bookings_names])

            if "Customer" in roles:
                customer_name = frappe.db.get_value("Customer DB", {"user_id": user}, "name")
                if not customer_name:
                    frappe.response["http_status_code"] = 404
                    frappe.response["message"] = {"error": "No booking requests found."}
                    return

                bookings_names = frappe.get_all(
                    "Docket DB",
                    filters={"customer_id": customer_name},
                    pluck="booking"
                )

                if bookings_names:
                    or_filters.append(["name", "in", bookings_names])
                    or_filters.append(["consignee", "=", customer_name])
                else:
                    filters.append(["consignee", "=", customer_name])



        # Date validation
        if from_date and to_date and from_date > to_date:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid date range: 'from_date' cannot be greater than 'to_date'."}
            return

        # Date filters
        if from_date:
            filters.append(["earliest_departure_date", ">=", from_date])
        if to_date:
            filters.append(["earliest_departure_date", "<=", to_date])

        # Always exclude FAILED
        filters.append(["booking_status", "!=", "FAILED"])
        if status:
            if status.upper() == "FAILED":
                frappe.response["http_status_code"] = 404
                frappe.response["message"] = {"error": "No Failed booking requests found."}
                return
            else:
                filters.append(["booking_status", "=", status])

        # Carrier filter
        if carrier:
            matching_carriers = frappe.get_all(
                "Carrier",
                or_filters=[
                    ["partyname1", "like", f"%{carrier}%"],
                    ["partyalias", "like", f"%{carrier}%"],
                ],
                pluck="name"
            )
            if matching_carriers:
                filters.append(["booking_agent", "in", matching_carriers])
            else:
                frappe.response["http_status_code"] = 404
                frappe.response["message"] = {"error": "No booking requests found matching the given carrier."}
                return

        # Search filter
        if search:
            or_filters = [
                ["name", "like", f"%{search}%"],
                ["inttra_reference", "like", f"%{search}%"],
                ["carrier_booking_number", "like", f"%{search}%"],
                ["parent_carrier_booking_number", "like", f"%{search}%"],
                ["carrier_source_booking_number", "like", f"%{search}%"],
                ["custom_place_of_carrier_receipt_name", "like", f"%{search}%"],
                ["custom_place_of_carrier_delivery_name", "like", f"%{search}%"],
            ]

        # Pagination setup
        page = int(page)
        page_size = int(page_size)
        offset = (page - 1) * page_size

        # Total count
        if or_filters:
            total_count = len(frappe.get_all(
                "Booking Request",
                filters=filters,
                or_filters=or_filters,
                fields=["name"]
            ))
        else:
            total_count = frappe.db.count("Booking Request", filters=filters)

        if total_count == 0:
            frappe.response["http_status_code"] = 404
            frappe.response["message"] = {"error": "No booking requests found matching the given filters."}
            return
        
        order_by="si_due_date desc"
        if bln_most_recent:
            order_by = "creation desc"
        if bln_si_due_12_days:
            today = datetime.now()
            twelve_days_later = today + timedelta(days=15)
            
            start_datetime = datetime.combine(today, datetime.min.time())
            end_datetime = datetime.combine(twelve_days_later, datetime.max.time())

            filters.append(["si_due_date", ">=", start_datetime])
            filters.append(["si_due_date", "<=", end_datetime])
            
            order_by = "si_due_date asc"

        # Fetch records
        booking_requests = frappe.get_all(
            "Booking Request",
            filters=filters,
            or_filters=or_filters,
            fields=[
                "name", "earliest_departure_date", "booking_agent", 
                "place_of_carrier_receipt", "place_of_carrier_delivery", 
                "latest_delivery_date", "inttra_reference", "modified", 
                "booking_status", "carrier_booking_number", "si_due_date", 
                "vgm_due_date", "cy_date","parent_carrier_booking_number","carrier_source_booking_number","out_side_booking",
            ],
            start=offset,
            page_length=page_size,
            order_by=order_by
        )

        response_data = []

        for doc in booking_requests:
            try:
                # Carrier info
                carrier_doc = frappe.get_doc("Carrier", doc.booking_agent) if doc.booking_agent else None
                party_code = carrier_doc.partyalias if carrier_doc else ""
                party_name = carrier_doc.partyname1 if carrier_doc else ""
                party_short_name = carrier_doc.party_short_name if carrier_doc else ""

                si_id = frappe.get_value("Shipping Instructions", {"carrier_booking_number": doc.carrier_booking_number}, "name") if doc.carrier_booking_number else ""
                if si_id:
                    si_doc = frappe.get_doc("Inttra Status SI", {"shipping_instruction":si_id},"si_carrier_status")
                    si_carrier_status = si_doc.si_carrier_status if si_doc else ""
                else:
                    si_carrier_status = ""
                
                lst_all_transports = []
                # Main Carriage (ETD/ETA)
                main_carriage = frappe.get_all(
                    "Booking Main Carriage", 
                    filters={"parent": doc.name}, 
                    fields=["voyage", "vessel", "port_of_load", "port_of_discharge", "etd", "eta"],
                    order_by="etd asc"
                )
                lst_all_transports += main_carriage if main_carriage else []

                pre_carriage = frappe.get_all(
                    "Booking Carriage", 
                    filters={"parent": doc.name, "parentfield": "add_pre_carriage"}, 
                    fields=["start", "end", "etd", "eta"]
                )
                if pre_carriage:
                    lst_all_transports += pre_carriage

                on_carriage = frappe.get_all(
                    "Booking Carriage", 
                    filters={"parent": doc.name, "parentfield": "add_on_carriage"}, 
                    fields=["start", "end", "etd", "eta"]
                )
                if on_carriage:
                    lst_all_transports += on_carriage

                # Sort by ETD (datetime), fallback to datetime.min if missing
                lst_all_transports = sorted(lst_all_transports, key=lambda x: x.get("etd") or datetime.min)


                dat_details = ""
                dat_arrival = ""
                vessal_and_voyage = ""
                if main_carriage:
                    dat_details = lst_all_transports[0].get("etd") or ""
                    dat_arrival = lst_all_transports[-1].get("eta") or ""
                    vessal_and_voyage = f"{main_carriage[0].get('vessel', '')} / {main_carriage[0].get('voyage', '')}"

                # if main_carriage:
                #     etd_dates = [parse_date(item.get("etd")) for item in main_carriage if item.get("etd")]
                #     eta_dates = [parse_date(item.get("eta")) for item in main_carriage if item.get("eta")]

                #     earliest_etd = min(etd_dates) if etd_dates else None
                #     latest_eta = max(eta_dates) if eta_dates else None

                #     dat_details = earliest_etd.strftime("%Y-%m-%d %H:%M") if earliest_etd else ""
                #     dat_arrival = latest_eta.strftime("%Y-%m-%d %H:%M") if latest_eta else ""
                # else:
                   

                # Containers
                containers = frappe.get_all(
                    "Booking Container", 
                    filters={"parent": doc.name}, 
                    fields=[
                        "container_quantitytype", "container_descp", 
                        "number_of_containers", "container_comments", "container_type_code"
                    ]
                )
                total_containers = sum(int(c.get("number_of_containers") or 0) for c in containers)

                # Cargo
                cargo = frappe.get_all("Cargo", filters={"parent": doc.name}, fields=["cargo_description"])

                # Location names
                receipt_location_name = ""
                if doc.place_of_carrier_receipt:
                    receipt_doc = frappe.get_doc("UNLOCODE Locations", doc.place_of_carrier_receipt)
                    receipt_location_name = receipt_doc.location_name if receipt_doc else ""

                delivery_location_name = ""
                if doc.place_of_carrier_delivery:
                    delivery_doc = frappe.get_doc("UNLOCODE Locations", doc.place_of_carrier_delivery)
                    delivery_location_name = delivery_doc.location_name if delivery_doc else ""

                str_job_ids = ""
                doc_jobs = frappe.get_all("Job", filters={"booking_id": doc.name}, fields=["name"])
                if doc_jobs:
                    lst_job_ids = [job.get("name") for job in doc_jobs]
                    str_job_ids = ", ".join(lst_job_ids)
                
                str_bl_reference_numbers = ""
                str_bl_reference_numbers = frappe.get_value("Bill of Lading", {"carrier_booking_number": doc.carrier_booking_number}, "bol_number")
                if not str_bl_reference_numbers:
                    doc_additional_reference = frappe.get_all("Reference Information", filters={"parent": doc.name}, fields=["reference_type", "text"])
                    if doc_additional_reference:
                        for ref in doc_additional_reference:
                            if ref.reference_type == "BillOfLadingNumber":
                                str_bl_reference_numbers = ref.text
                    

            except Exception as child_err:
                frappe.log_error(frappe.get_traceback(), "Booking Request Child Data Error")
                continue

            booking_data = {
                "booking_id": doc.name,
                "booking_date": doc.earliest_departure_date,
                "party_code": party_code,
                "party_name": party_name,
                "carrier_booking_number": doc.carrier_booking_number,
                "main_carriage": main_carriage,
                "containers": containers,
                "cargo": cargo,
                "location_name": delivery_location_name,
                "place_of_carrier_receipt": {
                    "location_name": receipt_location_name,
                    "date": doc.earliest_departure_date
                },
                "place_of_carrier_delivery": {
                    "location_name": delivery_location_name,
                    "date": doc.latest_delivery_date
                },
                "departure_date": doc.earliest_departure_date,
                "inttra_reference": doc.inttra_reference,
                "last_modified": doc.modified,
                "booking_status": doc.booking_status,
                "si_due_date": doc.si_due_date,
                "vgm_due_date": doc.vgm_due_date,
                "cy_date": doc.cy_date,
                "total_containers": total_containers,
                "dat_departure": dat_details,
                "dat_arrival": dat_arrival,
                "vessel_and_voyage": vessal_and_voyage or ".....",
                "parent_carrier_booking_number": doc.parent_carrier_booking_number,
                "carrier_source_booking_number" : doc.carrier_source_booking_number,
                "out_side_booking": doc.out_side_booking,
                "si_id": si_id,
                "party_short_name": party_short_name,
                "si_carrier_status": si_carrier_status,
                "job_ids": str_job_ids,
                "bill_of_lading": str_bl_reference_numbers or ""


            }

            response_data.append(booking_data)  

            if bln_si_due_12_days:
                response_data.sort(key=lambda b: (b.get("vessel_and_voyage") or "").lower())
                response_data.sort(key=lambda b: (b.get("party_code") or "").lower())
                response_data.sort(key=lambda b: (b.get("place_of_carrier_receipt", {}).get("location_name") or "").lower())
                response_data.sort(key=lambda b: (b.get("si_due_date") or ""))
                

        # Final response
        frappe.response["message"] = {
            "status_code": 200,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size,
            "bookings": response_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Request API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}


@frappe.whitelist(allow_guest=True)  
def create_booking_request():
    try:
        request_data = frappe.request.data
        if not request_data:
            return build_response({ "status_code": 400, "message": f"Invalid request: No data provided"})

        try:
            data = json.loads(request_data)
        except json.JSONDecodeError as e:
            return build_response({ "status_code": 400, "message": f"Invalid JSON format: {str(e)}"})

        frappe.db.begin()

        # Validate Shipper field
        shipper = data.get("shipper")
        if not shipper:
            return{
                "status_code": 400,
                "message": f"Please check the Shipper or re-enter it agian..!!"
            }
        if shipper and not frappe.db.exists("Shipper", shipper):
            return{
                "status_code": 400,
                "message": f"Please check the Shipper or re-enter it agian..!!"
            }

        consignee = data.get("consignee")
        if not shipper:
            return{
                "status_code": 400,
                "message": f"Please check the Consignee or re-enter it agian..!!"
            }
        if consignee and not frappe.db.exists("Customer DB", consignee):
            return{
                "status_code": 400,
                "message": f"Please check the Consignee or re-enter it agian..!!"
            }
        contract_party = data.get("contract_party")
        if contract_party and not (frappe.db.exists("Customer DB", contract_party) or frappe.db.exists("Shipper", contract_party)):
            return{
                "status_code": 400,
                "message": f"Please check the Contract Party or re-enter it agian..!!"
            }
        notify_party = data.get("notify_party")
        if notify_party and not (frappe.db.exists("Customer DB", notify_party)):
            return{
                "status_code": 400,
                "message": f"Please check the Notify Party or re-enter it agian..!!"
            }
        notify_party_1 = data.get("notify_party_1")
        if notify_party_1 and not (frappe.db.exists("Customer DB", notify_party_1)):
            return{
                "status_code": 400,
                "message": f"Please check the Additional Notify Party 1 or re-enter it agian..!!"
            }
        notify_party_2 = data.get("notify_party_2")
        if notify_party_2 and not (frappe.db.exists("Customer DB", notify_party_2)):
            return{
                "status_code": 400,
                "message": f"Please check the Additional Notify Party 2 or re-enter it agian..!!"
            }

        # Create the main Booking Request document
        booking_request = frappe.get_doc({
            "doctype": "Booking Request",
            "booking_agent": data.get("booking_agent"), 
            "contract_number": data.get("contract_number"),
            "booking_office": data.get("booking_office"),
            "carrier_booking_number": data.get("carrier_booking_number"),
            "filer_idscac": data.get("filer_idscac"),
            "doc_cut_of_date": data.get("doc_cut_of_date"),
            "shipper": shipper,

            # Parties
            "forwarder": data.get("forwarder"),
            "consignee": data.get("consignee"),
            "contract_party": data.get("contract_party"),
            "customs_broker": data.get("customs_broker"),
            "shipper_on_booking": data.get("shipper_on_booking") or  None,
            "consignee_on_booking": data.get("consignee_on_booking") or None,
            "forwarder_on_booking":  data.get("forwarder_on_booking") or None,
            "contract_party_on_booking": data.get("contract_party_on_booking") or None,
            "notify_party_on_booking": data.get("notify_party_on_booking") or None,
            "customs_broker_on_booking": data.get("customs_broker_on_booking") or None,
            "notify_party_on_booking":  data.get("notify_party_on_booking") or None,
            "notify_party_1": data.get("notify_party_1") or None,
            "notify_party_1_on_booking": data.get("notify_party_1_on_booking") if not data.get("notify_party_1") else None,
            "notify_party_2": data.get("notify_party_2") or None,
            "notify_party_2_on_booking": data.get("notify_party_2_on_booking") if not data.get("notify_party_2") else None,
            "notify_parties": data.get("notify_party") or None,



            # References
            "shippers_reference_numbers": ",".join(map(str, data.get("shippers_reference_numbers", []))),
            "forwarders_reference_numbers": ",".join(map(str, data.get("forwarders_reference_numbers", []))),
            "purchase_order_numbers": ",".join(map(str, data.get("purchase_order_numbers", []))),
            "tariff_number": data.get("tariff_number"),
            "contract_party_reference_numbers": ",".join(map(str, data.get("contract_party_reference_numbers", []))),
            "consignees_reference_numbers": ",".join(map(str, data.get("consignees_reference_numbers", []))),
            "bl_reference_numbers": ",".join(map(str, data.get("bl_reference_numbers", []))),
            "customer_shipment_id": data.get("customer_shipment_id"),

            # Transport
            "move_type": data.get("move_type"),
            "place_of_carrier_receipt": data.get("place_of_carrier_receipt"),  
            "earliest_departure_date": data.get("earliest_departure_date"),
            "place_of_carrier_delivery": data.get("place_of_carrier_delivery"),  
            "latest_delivery_date": data.get("latest_delivery_date"),
            
            "customer_comments": data.get("customer_comments"),
            "partner_email_notifications": data.get("partner_email_notifications"),

        })


        

        # Pre-Carriage
        if isinstance(data.get("pre_carriage"), list):
            for carriage in data["pre_carriage"]:
                start_location = carriage.get("start")
                end_location = carriage.get("end")
                
                # Validate that start and end locations exist
                try:
                    if start_location and not frappe.db.exists("UNLOCODE Locations", start_location):
                        frappe.throw(f"Start location '{start_location}' does not exist. Please enter a valid location.")
                    
                    if end_location and not frappe.db.exists("UNLOCODE Locations", end_location):
                        frappe.throw(f"End location '{end_location}' does not exist. Please enter a valid location.")

                    booking_request.append("add_pre_carriage", {
                        "doctype": "Booking Carriage",
                        "start": start_location,
                        "etd": carriage.get("etd"),
                        "end": end_location,
                        "eta": carriage.get("eta"),
                        "mode": carriage.get("mode") or "",
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response 
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response 

        # Main-Carriage
        if isinstance(data.get("main_carriage"), list):
            for main in data["main_carriage"]:
                port_of_load = main.get("port_of_load")
                port_of_discharge = main.get("port_of_discharge")

                try:
                    # Validate that port_of_load and port_of_discharge exist
                    if port_of_load and not frappe.db.exists("UNLOCODE Locations", port_of_load):
                        frappe.throw(f"Port of Load '{port_of_load}' does not exist. Please enter a valid port.")

                    if port_of_discharge and not frappe.db.exists("UNLOCODE Locations", port_of_discharge):
                        frappe.throw(f"Port of Discharge '{port_of_discharge}' does not exist. Please enter a valid port.")

                    booking_request.append("main_carriage", {
                        "doctype": "Booking Main Carriage",
                        "port_of_load": port_of_load,
                        "etd": main.get("etd"),
                        "vessel": main.get("vessel"),
                        "port_of_discharge": port_of_discharge,
                        "eta": main.get("eta"),
                        "voyage": main.get("voyage"),
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response  
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response


        # On-Carriage 
        if isinstance(data.get("on_carriage"), list):
            for on in data["on_carriage"]:
                start_location = on.get("start")
                end_location = on.get("end")

                try:
                    # Validate start location
                    if start_location and not frappe.db.exists("UNLOCODE Locations", start_location):
                        frappe.throw(f"Start location '{start_location}' does not exist. Please enter a valid location.")
                    
                    # Validate end location
                    if end_location and not frappe.db.exists("UNLOCODE Locations", end_location):
                        frappe.throw(f"End location '{end_location}' does not exist. Please enter a valid location.")

                    booking_request.append("add_on_carriage", {
                        "doctype": "Booking Carriage",
                        "start": start_location,
                        "etd": on.get("etd"),
                        "end": end_location,
                        "eta": on.get("eta"),
                        "mode": on.get("mode"),
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response


        # Containers
        if isinstance(data.get("containers"), list):
            for container in data["containers"]:
                quantity_type = container.get("container_quantitytype")

                try:
                    # Validate quantity type exists in the linked doctype
                    if quantity_type and not frappe.db.exists("Container Type", quantity_type):
                        frappe.throw(f"Container Quantity Type '{quantity_type}' does not exist. Please enter a valid type.")

                    # Append valid container data to booking_request
                    booking_request.append("containers", {
                        "doctype": "Booking Container",
                        "number_of_containers" : container.get("number_of_containers"),
                        "container_quantitytype": quantity_type, 
                        "container_comments": container.get("container_comments"),
                        "shipper_owned": container.get("shipper_owned"),
                        "haulage_detail": container.get("haulage_detail"),
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response  
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response 


        # Cargo
        if isinstance(data.get("cargo"), list):
            for cargo in data["cargo"]:
                hs_code = cargo.get("hs_code")

                try:
                    # Validate HS Code exists in the linked doctype
                    if hs_code and not frappe.db.exists("HS Code", hs_code):
                        frappe.throw(f"HS Code '{hs_code}' does not exist. Please enter a valid HS Code.")

                    booking_request.append("cargo", {
                        "doctype": "Cargo",
                        "hs_code": hs_code,
                        "cargo_description": cargo.get("cargo_description"),
                        "cargo_gross_weight": cargo.get("cargo_gross_weight"),
                        "net_weight": cargo.get("net_weight"),
                        "net_weight_unit": cargo.get("net_weight_unit"),
                        "gross_volume": cargo.get("gross_volume"),
                        "gross_volume_unit": cargo.get("gross_volume_unit"),
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response 
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response  


        # Payment Details
        if isinstance(data.get("payment_details"), list):
            for payment in data["payment_details"]:
                payment_location = payment.get("payment_location")

                try:
                    # Validate Payment location
                    if payment_location and not frappe.db.exists("UNLOCODE Locations", payment_location):
                        frappe.throw(f"Payment Location '{payment_location}' does not exist.")

                    # Append valid payment data to booking_request
                    booking_request.append("payment", {
                        "doctype": "Payment Details",
                        "charge_type": payment.get("charge_type"),
                        "payment_term": payment.get("payment_term"),
                        "payer": payment.get("payer"),
                        "payment_location": payment_location  
                    })

                except frappe.ValidationError as e:
                    error_response = {
                        "status_code": 400,
                        "message": f"Validation Error: {str(e)}"
                    }
                    return error_response  
                
                except Exception as e:
                    error_response = {
                        "status_code": 500,
                        "message": f"Internal Server Error: {str(e)}"
                    }
                    return error_response 


        booking_request.insert(ignore_permissions=True)

        if hasattr(booking_request, "_external_api_error"):
            return {
                "status_code": 400,
                "message": booking_request._external_api_error
            }

        # Call INTTRA API before committing
        try:
            response = send_booking_to_inttra(booking_request)

            if response.get('status_code') == 200:
                response_data = response.get('response', {})
                booking_details = response_data.get("booking", {}).get("details", [])
                booking_id = booking_details[0].get("bookingId")
                if booking_details and booking_id:                    
                    inttra_reference = booking_details[0].get("payload", {}).get("inttraReference")
                    booking_request.inttra_booking_id = booking_id
                    booking_request.customer_shipment_id = response.get('shipmentId')
                    booking_request.inttra_reference = inttra_reference
                    booking_request.inttra_response_status = response.get("status_code")
                    booking_request.product_channel = 'API'
                    booking_request.booking_status = booking_details[0].get("payload", {}).get("bookingState")
                    booking_request.carrier_booking_number = booking_details[0].get("payload", {}).get("carrierReferenceNumber")
                    booking_request.flags.inttra_sending = True
                    booking_request.save(ignore_permissions=True)
                    frappe.db.commit()

                    api_log = frappe.get_doc({
                        'doctype': 'Inttra API Logs',
                        'description': 'Booking Request',
                        'booking_id': booking_request.name,
                        'inttra_refference_id': inttra_reference,
                        'booking_status': 'REQUEST',
                        'response': json.dumps(response_data) if response_data else "",
                        'requested_json': json.dumps(response.get("payload")) or None
                    })
                    api_log.insert(ignore_permissions=True)
                    frappe.db.commit()


                    frappe.msgprint(f"Booking successfully sent to INTTRA with booking ID: {booking_id}")
                    frappe.local.response["status_code"] = response.get("status_code")
                    frappe.local.response["message"] = f"Booking created successfully with INTTRA booking ID: {inttra_reference}"
                    frappe.local.response["data"] = booking_request.as_dict() 
                    
                else:
                    booking_request.booking_status = "FAILED"
                    booking_request.save(ignore_permissions=True)
                    frappe.db.commit()
                    api_log = frappe.get_doc({
                        'doctype': 'Inttra API Logs',
                        'description': 'Booking Request',
                        'booking_id': booking_request.name,
                        'inttra_refference_id': "",
                        'booking_status': 'FAILED',
                        'response': json.dumps(response) if response else "",
                        'requested_json': json.dumps(response.get("payload")) or None
                    })
                    api_log.insert(ignore_permissions=True)
                    frappe.db.commit()
                    frappe.msgprint("Failed to send booking to INTTRA")
                    frappe.local.response["status_code"] = response.status_code
                    frappe.local.response["message"] = f"Failed to send booking to INTTRA: {response.text}"
                    
            else: 
                booking_request.booking_status = "FAILED"
                booking_request.save(ignore_permissions=True)
                frappe.db.commit()
                api_log = frappe.get_doc({
                        'doctype': 'Inttra API Logs',
                        'description': 'Booking Request',
                        'booking_id': booking_request.name,
                        'inttra_refference_id': "",
                        'booking_status': 'FAILED',
                        'response': json.dumps(response) if response else "",
                        'requested_json': json.dumps(response.get("payload")) or None
                })
                api_log.insert(ignore_permissions=True)
                frappe.db.commit()
                return {
                    "status_code": response.get('status_code', 500),
                    "message": (
                        response.get('message', [{}])[0].get('message')
                        if isinstance(response.get('message'), list) and len(response['message']) > 0
                        else "INTTRA API Error"
                    )
                }   
                       
                
        except Exception as e:
            frappe.db.rollback()
            frappe.log_error(frappe.get_traceback(), "INTTRA Booking Request Failed")
            return build_response({
                "status_code": 500,
                "message": f"INTTRA send failed: {str(e)}"
            })

        
        
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Booking Request API Error")
        return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
    



@frappe.whitelist(allow_guest=True)
def get_amend_details(name):
    try:
        if not name:
            return {
                "status_code": 400,
                "message": "Missing parameter: booking_name"
            }

        if not frappe.db.exists("Booking Request", name):
            return {
                "status_code": 404,
                "message": f"Booking Request '{name}' does not exist"
            }

        booking_doc = frappe.get_doc("Booking Request", name)
        booking_data = booking_doc.as_dict()

        meta_fields_to_remove = [
            "_user_tags", "_comments", "_assign", "docstatus", "idx", "creation",
            "modified", "modified_by", "owner", "parent", "parenttype", "parentfield"
        ]

        for field in meta_fields_to_remove:
            booking_data.pop(field, None)

        meta = frappe.get_meta("Booking Request")
        link_fields = {df.fieldname: df.options for df in meta.fields if df.fieldtype == "Link"}
        for fieldname, linked_doctype in link_fields.items():
            if booking_data.get(fieldname):
                try:
                    linked_doc = frappe.get_doc(linked_doctype, booking_data[fieldname]).as_dict()
                    for f in meta_fields_to_remove:
                        linked_doc.pop(f, None)
                    booking_data[fieldname] = linked_doc
                except Exception:
                    pass
        
        if booking_data.get("contract_party"):
            contract_party_id = booking_data["contract_party"]
            contract_party = None

            try:
                if contract_party_id.startswith("SHI-"):
                    contract_party = frappe.get_doc("Shipper", contract_party_id).as_dict()
                elif contract_party_id.startswith("CUS-"):
                    contract_party = frappe.get_doc("Customer DB", contract_party_id).as_dict()

                if contract_party:
                    for f in meta_fields_to_remove:
                        contract_party.pop(f, None)
                    booking_data["contract_party"] = contract_party

            except Exception as e:
                frappe.logger().warning(f"Failed to fetch contract_party {contract_party_id}: {e}")


        child_tables = [
            "add_pre_carriage",
            "main_carriage",
            "add_on_carriage",
            "containers",
            "cargo",
            "payment"
        ]

        for table in child_tables:
            cleaned_children = []
            for child in booking_doc.get(table) or []:
                if isinstance(child, str):
                    continue
                child_data = child.as_dict()

                for f in meta_fields_to_remove:
                    child_data.pop(f, None)

                # Expand linked fields inside child doc
                child_meta = frappe.get_meta(child.doctype)
                child_link_fields = {
                    df.fieldname: df.options for df in child_meta.fields if df.fieldtype == "Link"
                }

                for fieldname, linked_doctype in child_link_fields.items():
                    if linked_doctype == "UNLOCODE Locations" and child_data.get(fieldname):
                        try:
                            linked_doc = frappe.get_doc(linked_doctype, child_data[fieldname]).as_dict()
                            for f in meta_fields_to_remove:
                                linked_doc.pop(f, None)
                            child_data[fieldname] = linked_doc
                        except Exception:
                            pass  # skip if linked doc doesn't exist

                cleaned_children.append(child_data)

            booking_data[table] = cleaned_children

        return {
            "status_code": 200,
            "message": "Booking request details fetched successfully",
            "data": booking_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Booking Request Details API Error")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }


@frappe.whitelist(allow_guest=True)  
def update_booking_request(name=None):

    try:
        # Get the booking_request_id from URL params if not provided in function argumentsim
        name = frappe.request.args.get("name")   
        if not name:
            name = frappe.form_dict.get("name")

        # Check if booking_request_id is provided
        if not name:
            frappe.throw(_("Booking Request ID is required"), exc=frappe.ValidationError)

        # Check if the Booking Request exists
        if not frappe.db.exists("Booking Request", name):
            frappe.throw(_("Booking Request with ID '{0}' not found").format(name), exc=frappe.DoesNotExistError)

        request_data = frappe.request.data
        if not request_data:
            frappe.throw(_("Invalid request: No data provided"), exc=frappe.ValidationError)

        try:
            save_previous_booking_data(name)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Error in save_previous_booking_data")
            pass

        try:
            data = json.loads(request_data)
        except json.JSONDecodeError as e:
            frappe.throw(_("Invalid JSON format: {0}").format(str(e)), exc=frappe.ValidationError)


        
        frappe.db.begin()
        
        
        # Fetch the existing Booking Request
        booking_request = frappe.get_doc("Booking Request", name)
        # Validate Shipper field if provided
        shipper = data.get("shipper")
        if not shipper:
            return{
                "status_code": 400,
                "message": f"Please check the Shipper or Re-enter it agian..!!"
            }
        if shipper and not frappe.db.exists("Shipper", shipper):
            return{
                "status_code": 400,
                "message": f"Please check the Shipper or Re-enter it agian..!!"
            }
        consignee = data.get("consignee")
        if consignee and not frappe.db.exists("Customer DB", consignee):
            return{
                "status_code": 400,
                "message": f"Please check the Consignee or Re-enter it agian..!!"
            }
        contract_party = data.get("contract_party")

        if contract_party and not (frappe.db.exists("Shipper", contract_party) or frappe.db.exists("Customer DB", contract_party)):
            return{
                "status_code": 400,
                "message": f"Please check the Contract Party or Re-enter it agian..!!"
            }
        notify_party_1 = data.get("notify_party_1")
        if notify_party_1 and not (frappe.db.exists("Customer DB", notify_party_1)):
            return{
                "status_code": 400,
                "message": f"Please check the Additional Notify Party 1 or Re-enter it agian..!!"
            }
        notify_party_2 = data.get("notify_party_2")
        if notify_party_2 and not (frappe.db.exists("Customer DB", notify_party_2)):
            return{
                "status_code": 400,
                "message": f"Please check the Additional Notify Party 2 or Re-enter it agian..!!"
            }

        # Update main fields if provided
        fields_to_update = [
            "booking_agent", "contract_number", "booking_office", "carrier_booking_number", 
            "filer_idscac", "doc_cut_of_date", "shipper", "forwarder", "consignee", "shipper_on_booking",
            "consignee_on_booking", "forwarder_on_booking", "contract_party_on_booking",
            "notify_party_on_booking", "customs_broker_on_booking", "notify_party_1_on_booking",
            "notify_party_2_on_booking", "notify_party", "notify_party_1", "notify_party_2",
            "contract_party", "customs_broker", "tariff_number", "customer_shipment_id",
            "move_type", "place_of_carrier_receipt", "earliest_departure_date", 
            "place_of_carrier_delivery", "latest_delivery_date", "customer_comments",
            "partner_email_notifications"
        ]
        
        for field in fields_to_update:
            if field in data:
                booking_request.set(field, data.get(field))
        
        # Update reference number fields that are stored as comma-separated strings
        reference_fields = [
            "shippers_reference_numbers", "forwarders_reference_numbers", 
            "purchase_order_numbers", "contract_party_reference_numbers", 
            "consignees_reference_numbers", "bl_reference_numbers"
        ]
        
        for field in reference_fields:
            if field in data and isinstance(data.get(field), list):
                booking_request.set(field, ",".join(map(str, data.get(field, []))))
        
        # Update child tables if provided
        
        # Update Notify Parties
        if isinstance(data.get("notify_parties"), list):
            # Clear existing notify parties
            booking_request.notify_parties = []
            
            for party_name in data["notify_parties"]:
                try:
                    if party_name and not frappe.db.exists("Notify Party", party_name['name1']):
                        frappe.throw(f"Notify Party '{party_name['name1']}' does not exist. Please enter a valid Notify party.")
                    
                    booking_request.append("notify_parties", {
                        "notify_parties": party_name['name1']  
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update Pre-Carriage
        if isinstance(data.get("pre_carriage"), list):
            # Clear existing pre-carriage entries
            booking_request.add_pre_carriage = []
            
            for carriage in data["pre_carriage"]:
                start_location = carriage.get("start")
                end_location = carriage.get("end")
                
                try:
                    if start_location and not frappe.db.exists("UNLOCODE Locations", start_location):
                        frappe.throw(f"Start location '{start_location}' does not exist. Please enter a valid location.")
                    
                    if end_location and not frappe.db.exists("UNLOCODE Locations", end_location):
                        frappe.throw(f"End location '{end_location}' does not exist. Please enter a valid location.")

                    booking_request.append("add_pre_carriage", {
                        "doctype": "Booking Carriage",
                        "start": start_location,
                        "etd": carriage.get("etd"),
                        "end": end_location,
                        "eta": carriage.get("eta"),
                        "mode": carriage.get("mode") or "",
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update Main-Carriage
        if isinstance(data.get("main_carriage"), list):
            # Clear existing main carriage entries
            booking_request.main_carriage = []
            
            for main in data["main_carriage"]:
                port_of_load = main.get("port_of_load")
                port_of_discharge = main.get("port_of_discharge")

                try:
                    if port_of_load and not frappe.db.exists("UNLOCODE Locations", port_of_load):
                        frappe.throw(f"Port of Load '{port_of_load}' does not exist. Please enter a valid port.")

                    if port_of_discharge and not frappe.db.exists("UNLOCODE Locations", port_of_discharge):
                        frappe.throw(f"Port of Discharge '{port_of_discharge}' does not exist. Please enter a valid port.")

                    booking_request.append("main_carriage", {
                        "doctype": "Booking Main Carriage",
                        "port_of_load": port_of_load,
                        "etd": main.get("etd"),
                        "vessel": main.get("vessel"),
                        "port_of_discharge": port_of_discharge,
                        "eta": main.get("eta"),
                        "voyage": main.get("voyage"),
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update On-Carriage
        if isinstance(data.get("on_carriage"), list):
            # Clear existing on-carriage entries
            booking_request.add_on_carriage = []
            
            for on in data["on_carriage"]:
                start_location = on.get("start")
                end_location = on.get("end")

                try:
                    if start_location and not frappe.db.exists("UNLOCODE Locations", start_location):
                        frappe.throw(f"Start location '{start_location}' does not exist. Please enter a valid location.")
                    
                    if end_location and not frappe.db.exists("UNLOCODE Locations", end_location):
                        frappe.throw(f"End location '{end_location}' does not exist. Please enter a valid location.")

                    booking_request.append("add_on_carriage", {
                        "doctype": "Booking Carriage",
                        "start": start_location,
                        "etd": on.get("etd"),
                        "end": end_location,
                        "eta": on.get("eta"),
                        "mode": on.get("mode"),
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update Containers
        if isinstance(data.get("containers"), list):
            # Clear existing containers
            booking_request.containers = []
            
            for container in data["containers"]:
                quantity_type = container.get("container_quantitytype")

                try:
                    if quantity_type and not frappe.db.exists("Container Type", quantity_type):
                        frappe.throw(f"Container Quantity Type '{quantity_type}' does not exist. Please enter a valid type.")

                    booking_request.append("containers", {
                        "doctype": "Booking Container",
                        "number_of_containers": container.get("number_of_containers"),
                        "container_quantitytype": quantity_type, 
                        "container_comments": container.get("container_comments"),
                        "shipper_owned": container.get("shipper_owned"),
                        "haulage_detail": container.get("haulage_detail"),
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update Cargo
        if isinstance(data.get("cargo"), list):
            # Clear existing cargo
            booking_request.cargo = []
            
            for cargo in data["cargo"]:
                hs_code = cargo.get("hs_code")

                try:
                    if hs_code and not frappe.db.exists("HS Code", hs_code):
                        frappe.throw(f"HS Code '{hs_code}' does not exist. Please enter a valid HS Code.")

                    booking_request.append("cargo", {
                        "doctype": "Cargo",
                        "hs_code": hs_code,
                        "cargo_description": cargo.get("cargo_description"),
                        "cargo_gross_weight": cargo.get("cargo_gross_weight"),
                        "net_weight": cargo.get("net_weight"),
                        "net_weight_unit": cargo.get("net_weight_unit"),
                        "gross_volume": cargo.get("gross_volume"),
                        "gross_volume_unit": cargo.get("gross_volume_unit"),
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Update Payment Details
        if isinstance(data.get("payment_details"), list):
            # Clear existing payment details
            booking_request.payment = []
            
            for payment in data["payment_details"]:
                payment_location = payment.get("payment_location")

                try:
                    if payment_location and not frappe.db.exists("UNLOCODE Locations", payment_location):
                        frappe.throw(f"Payment Location '{payment_location}' does not exist.")

                    booking_request.append("payment", {
                        "doctype": "Payment Details",
                        "charge_type": payment.get("charge_type"),
                        "payment_term": payment.get("payment_term"),
                        "payer": payment.get("payer"),
                        "payment_location": payment_location  
                    })
                except frappe.ValidationError as e:
                    return build_response({ "status_code": 400, "message": f"Validation Error: {str(e)}"})
                except Exception as e:
                    return build_response({ "status_code": 500, "message": f"Internal Server Error: {str(e)}"})
        
        # Save the updated booking request
        booking_request.save(ignore_permissions=True)
        
        # Call the amend function
        booking = frappe.get_doc("Booking Request", name)
        amend_resp = amend_booking(booking)
        
        if amend_resp.get('status_code') == 200:
            frappe.db.commit()
            return {
                "success": True,
                "status_code": amend_resp.get('status_code'),
                "message": f"Booking {name} updated and amended successfully!.",
                "data": {
                    "booking_id": name,
                    "amend_response": amend_resp
                }
            }
        else:
            frappe.db.rollback()
            frappe.log_error(frappe.get_traceback(), "Update Booking Error")
            return {
                "success": False,
                "status_code": amend_resp.get('status_code'),
                "message": f"Booking {name} not amended:-\n{amend_resp.get('message')}",
                "data": {
                    "booking_id": name,
                }
            }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Update Booking Error")
        return {
            "success": False,
            "error": str(e)
        }



@frappe.whitelist(allow_guest=True)
def create_booking_template(template_name, booking_data):
 
    if not template_name or not booking_data:
        return {"status": "error", "message": _("Missing required fields.")}

    try:
        
        if isinstance(booking_data, str):
            booking_data = json.loads(booking_data)

        booking_template = frappe.get_doc({
            "doctype": "Booking Template",
            "template_name": template_name,
            "booking_data": json.dumps(booking_data)
        })
        booking_template.insert(ignore_permissions=True)
        frappe.db.commit()

        return {"status": "success", "message": _("Booking Template created successfully."),
                "data": {
                "name": booking_template.name,
                "template_name": booking_template.template_name
            }}
    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Template Error")
        return {"status": "error", "message": str(e)}





@frappe.whitelist(allow_guest=True)
def list_booking_template(template_name=None, search_text=None, page=1, count=10):
    try:
        if template_name:
            # Fetch single template
            doc = frappe.get_doc("Booking Template", template_name)
            return {
                "status": "success",
                "mode": "view",
                "data": {
                    "name": doc.name,
                    "template_name": doc.template_name,
                    "booking_data": json.loads(doc.booking_data or "{}"),
                    "creation": doc.creation,
                    "modified": doc.modified
                }
            }
        else:
            filters = []
            if search_text:
                filters.append(["template_name", "like", f"%{search_text}%"])
            
            page_length = int(count)
            start = (int(page) - 1) * page_length

            templates = frappe.get_all(
                "Booking Template",
                filters=filters,
                fields=["name", "template_name", "creation", "modified", "booking_data"],
                order_by="modified desc",
                limit_start=start,
                limit_page_length=page_length
            )

            for t in templates:
                t["booking_data"] = json.loads(t["booking_data"] or "{}")

            # Get total count for frontend pagination
            total_count = frappe.db.count("Booking Template", filters=filters)
            total_pages = ceil(total_count / page_length) if total_count else 1

            return {
                "status": "success",
                "message": _("Fetched booking templates."),
                "data": templates,
                "total_count": total_count,
                "total_pages": total_pages,
                "page": int(page),
                "count": page_length
            }

    except frappe.DoesNotExistError:
        return {"status": "error", "message": _("Template not found.")}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Template API Error")
        return {"status": "error", "message": str(e)}



@frappe.whitelist(allow_guest=True)
def fetch_booking_template(template_id):
    if not template_id:
        return {"status": "error", "message": _("Template ID is required.")}

    try:
        doc = frappe.get_doc("Booking Template", template_id)
        return {
            "status": "success",
            "message": _("Booking Template fetched successfully."),
            "data": {
                "name": doc.name,
                "template_name": doc.template_name,
                "booking_data": json.loads(doc.booking_data or "{}"),
                "creation": doc.creation,
                "modified": doc.modified
            }
        }
    except frappe.DoesNotExistError:
        return {"status": "error", "message": _("Template not found.")}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Fetch Booking Template Error")
        return {"status": "error", "message": str(e)}
    


@frappe.whitelist(allow_guest=True)  
def create_template_or_update_template(template_name=None, booking_data=None):
    """
    Create or update a booking template.
    If the template already exists, it will be updated with the new data.
    If it does not exist, a new template will be created.
    """
    if not template_name or not booking_data:
        return {"status": "error", "message": _("Missing required fields.")}

    try:
        if isinstance(booking_data, str):
            booking_data = json.loads(booking_data)

        existing_template = frappe.db.exists("Booking Template", {"template_name": template_name})

        if existing_template:
            booking_template = frappe.get_doc("Booking Template", existing_template)
            booking_template.booking_data = json.dumps(booking_data)
            str_message = _("Booking Template updated successfully.")
            booking_template.save(ignore_permissions=True)
        else:
            booking_template = frappe.get_doc({
                "doctype": "Booking Template",
                "template_name": template_name,
                "booking_data": json.dumps(booking_data)
            })
            booking_template.insert(ignore_permissions=True)
            str_message = _("Booking Template created successfully.")

        frappe.db.commit()

        return {"status": "success", "message": str_message,
                "data": {
                    "name": booking_template.name,
                    "template_name": booking_template.template_name
                }}
    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Create or Update Booking Template Error")
        return {"status": "error", "message": str(e)}
    


@frappe.whitelist(allow_guest=True)
@role_required(["Admin", "System Manager", "Administrator"])
def delete_booking_template(template_id=None):
    if not template_id:
        return {"status": "error", "message": _("Template ID is required.")}

    try:
        doc = frappe.get_doc("Booking Template", template_id)
        doc.delete()
        frappe.db.commit()
        return {"status": "success", "message": _("Booking Template deleted successfully.")}
    except frappe.DoesNotExistError:
        return {"status": "error", "message": _("Template not found.")}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Delete Booking Template Error")
        return {"status": "error", "message": str(e)}
    

@frappe.whitelist(allow_guest=True)
def search_template(search_text=None):
    if not search_text:
        return {"status": "error", "message": _("Search text is required.")}

    try:
        templates = frappe.get_all(
            "Booking Template",
            filters=[["template_name", "like", f"%{search_text}%"]],
            fields=["name", "template_name", "creation", "modified"],
            order_by="modified desc"
        )

        return {
            "status": "success",
            "message": _("Fetched booking templates."),
            "data": templates
        }
    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Search Booking Template Error")
        return {"status": "error", "message": str(e)}
   