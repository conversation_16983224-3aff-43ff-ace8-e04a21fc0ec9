
import frappe
from datetime import datetime

from westside.westside.doctype.bill_of_lading.bill_of_lading import update_bol_parties_name


@frappe.whitelist(allow_guest=True)
def bol_filter_carrier_data():
    try:

        carriers = frappe.get_all(
            "Carrier", fields=["partyalias", "partyname1", "name"])

        return {
            "status": "success",
            "carriers": carriers,

        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }
    

@frappe.whitelist(allow_guest=True)
def get_bill_of_lading(consignee=None, port_of_loading=None, carrier=None, search_text=None, page=1, limit=20):
    try:
        doctype = "Bill of Lading"
        filters, or_filters = [], []

        # ---------------------------
        # 1. Carrier filter
        # ---------------------------
        if carrier:
            carrier_record = None
            if frappe.db.exists("Carrier", carrier):
                carrier_record = frappe.get_value("Carrier", carrier, ["partyalias", "partyname1"], as_dict=True)
            else:
                carrier_record = frappe.get_value(
                    "Carrier", {"partyalias": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"], as_dict=True
                ) or frappe.get_value(
                    "Carrier", {"partyname1": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"], as_dict=True
                )

            if not carrier_record:
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={"partner_role": "Carrier", "partner_name": ["like", f"%{carrier}%"]},
                    pluck="parent"
                )
                if bol_names:
                    filters.append(["name", "in", bol_names])
                else:
                    frappe.local.response.update({"status_code": 404, "error": f"No Carrier found matching '{carrier}'"})
                    return
            else:
                carrier_values = [c for c in [carrier_record.partyalias, carrier_record.partyname1] if c]
                filters.append(["carrier", "in", carrier_values])

        # ---------------------------
        # 2. Consignee filter
        # ---------------------------
        if consignee:
            bol_names = []
            if frappe.db.exists("Customer DB", consignee):
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={
                        "partner_role": "Consignee",
                        "partner_table_name": consignee  
                    },
                    pluck="parent"
                )
            else:
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={"partner_role": "Consignee","is_active": 1},
                    or_filters=[
                        {"partner_table_name": consignee},  
                        {"partner_name": ["like", f"%{consignee}%"]},  
                    ],
                    pluck="parent"
                )

            if not bol_names:
                frappe.local.response.update({
                    "status_code": 404,
                    "error": f"No Consignee found matching '{consignee}'"
                })
                return

            filters.append(["name", "in", bol_names])


        if port_of_loading:
            filters.append(["port_of_load", "=", port_of_loading])

        if search_text:
            or_filters.extend([
                ["bol_number", "like", f"%{search_text}%"],
                ["carrier_booking_number", "like", f"%{search_text}%"],
                ["message_status", "like", f"%{search_text}%"],
            ])
        # ---------------------------
        # 3. Fetch core BOL docs (only needed fields)
        # ---------------------------
        offset = (int(page) - 1) * int(limit)
        total_count = frappe.get_all(
            doctype,
            filters=filters,
            or_filters=or_filters,
            fields=["name"],
        )
        total_count = len(total_count)


        bol_rows = frappe.get_all(
            doctype,
            filters=filters,
            or_filters=or_filters,
            fields=["name", "bol_number", "carrier", "carrier_booking_number", "port_of_load", "port_of_discharge","message_status", "modified","main_transport_sail_date","create_date_time","port_of_load_location","port_of_discharge_location","creation","total_equipment", "total_gross_weight","created_status"],
            order_by="modified desc",
            start=offset,
            page_length=int(limit),
        )

        if not bol_rows:
            frappe.local.response.update({"status_code": 200, "message": [], "total_count": 0, "page": page, "limit": limit})
            return

        bol_names = [row.name for row in bol_rows]
        bol_numbers = [row.bol_number for row in bol_rows if row.bol_number]
        booking_numbers = [row.carrier_booking_number for row in bol_rows if row.carrier_booking_number]

        # ---------------------------
        # 4. Bulk fetch related data
        # ---------------------------
        # Docket DB
        docket_map = {
            d.blno: d for d in frappe.get_all("Docket DB", filters={"blno": ["in", bol_numbers]}, fields=["name", "status", "blno", "main_status"])
        }

        # Consignee
        consignee_map = {}
        for p in frappe.get_all("Partner Information", filters={"parent": ["in", bol_names], "partner_role": "Consignee"}, fields=["parent", "partner_name"]):
            consignee_map[p.parent] = p.partner_name

        # Booking Request → Main Carriage
        booking_map = {}
        if booking_numbers:
            booking_requests = frappe.get_all("Booking Request", filters={"carrier_booking_number": ["in", booking_numbers]}, fields=["name", "carrier_booking_number"])
            booking_dict = {b.carrier_booking_number: b.name for b in booking_requests}

            carriages = frappe.get_all("Booking Main Carriage", filters={"parent": ["in", [b.name for b in booking_requests]]}, fields=["parent", "eta", "etd"])
            carriage_map = {}
            for c in carriages:
                carriage_map.setdefault(c.parent, []).append(c)

            for carrier_booking, booking_id in booking_dict.items():
                lst = carriage_map.get(booking_id, [])
                if lst:
                    lst_sorted = sorted(lst, key=lambda x: x.etd or datetime.min)
                    booking_map[carrier_booking] = lst_sorted[-1].eta if lst_sorted[-1].eta else ""

        # Attachments
        attachment_map = {}
        bol_attachments = frappe.get_all("BOL Attachments", filters={"parent": ["in", bol_names]}, fields=["parent", "file_name", "file_url", "order", "modified"])
        for att in bol_attachments:
            existing = attachment_map.get(att.parent)
            if not existing or (att.order or 0, att.modified) > (existing.get("order") or 0, existing.get("modified")):
                attachment_map[att.parent] = att

        # ---------------------------
        # 5. Build response
        # ---------------------------
        results = []
        for row in bol_rows:
            clean = row.copy()
            docket = docket_map.get(row.bol_number)
            clean["docket_id"] = docket.name if docket else None
            docket = docket_map.get(row.bol_number)
            if docket:
                docket_main_status = docket.get("main_status")
                if docket_main_status == "Draft":
                    clean["docket_status"] = docket_main_status
                else:
                    clean["docket_status"] = docket.get("status")
            else:
                clean["docket_status"] = None

            clean["consignee"] = consignee_map.get(row.name)
            clean["eta"] = booking_map.get(row.carrier_booking_number, "")
            att = attachment_map.get(row.name)
            clean["attachments"] = [att] if att else []
            results.append(clean)

        frappe.local.response.update({
            "status_code": 200,
            "message": results,
            "total_count": total_count,
            "page": int(page),
            "limit": int(limit)
        })

    except Exception as e:
        frappe.local.response.update({"status_code": 500, "error": f"Failed processing: {str(e)}"})
        raise



@frappe.whitelist(allow_guest=True)
def bol_filter_consignee_data():
    try:
        
        customers = frappe.get_all("Customer DB", filters={"is_active": 1}, fields=["name","customer_name","first_name", "last_name"], order_by="customer_name asc")

        return {
            "status": "success",
            "consignee": customers,

        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }





@frappe.whitelist(allow_guest=True)
def get_details_bill_of_lading(bol_id=None):
    """
    Get Bill of Lading documents (cleaned format) with optional filters and pagination.
    - Filters: Consignee, Port of Loading, Carrier
    - Pagination: Defaults to 20 per page, accepts 'page' param
    """

    try:

        doctype = "Bill of Lading"
        results = []
        excluded_prefixes = ("section_break_", "column_break_")
        excluded_fields = {
            "doctype", "owner", "creation", "modified", "modified_by", "docstatus","xml_data","xml_file_name","master_data_section"
        }

        try:
            update_bol_parties_name(bol_id)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Error in update_bol_parties_name")
        doc = frappe.get_doc(doctype, bol_id)
        clean_data = {}
        clean_data["name"] = doc.name

        for df in doc.meta.fields:
            fieldname = df.fieldname

            if (
                fieldname in excluded_fields
                or any(fieldname.startswith(prefix) for prefix in excluded_prefixes)
            ):
                continue

            if fieldname == "total_gross_weight":
                    value = doc.get(fieldname)
                    if value is not None:
                        try:
                            doc.set(fieldname, "{:.2f}".format(float(value)))
                        except (ValueError, TypeError):
                            pass 
		
            if df.fieldtype == "Table":
                child_items = doc.get(fieldname)
                clean_data[fieldname] = [
                    {k: v for k, v in item.as_dict().items()
                        if k not in (
                        "doctype", "parent", "parentfield", "parenttype", "idx",
                        "owner", "modified", "modified_by", "docstatus", "creation",
                        "section_break_oukm", "column_break_ojnf"
                    )}
                    for item in child_items
                ]
            else:
                clean_data[fieldname] = doc.get(fieldname)

            docket_info = frappe.get_value(
                "Docket DB",
                {"blno": clean_data.get("bol_number")},
                ["name", "status"],
                as_dict=True
            )

            if docket_info:
                clean_data["docket_id"] = docket_info.name
                clean_data["docket_status"] = docket_info.status
            else:
                clean_data["docket_id"] = None
                clean_data["docket_status"] = None
        
        lst_equipment = []
        lst_cargo = []
        total_net_weight_caluclated = 0
        if frappe.db.exists("Equipments", {"bill_of_lading_id": bol_id}):
            equipment_docs = frappe.get_all(
                "Equipments",
                filters={"bill_of_lading_id": bol_id,"is_active": 1},
                fields=["name"]  
            )

            for eqp in equipment_docs if equipment_docs else []:
                full_doc = frappe.get_doc("Equipments", eqp.name)
                if full_doc: 
                    total_net_weight_caluclated += full_doc.cargo[0].get("net_weight") if full_doc.cargo else 0
                    dct_equipment = {
                        "name": full_doc.name,
                        "equipment_name": full_doc.equipment_name,
                        "code_value": full_doc.code_value,
                        "equipment_type": frappe.get_value("Container Type", full_doc.container_type_id, "shortdescription"),
                        "description": full_doc.description,
                        "comment": full_doc.comment,
                        "shipper_seal_number": full_doc.shipper_seal_number,
                        "carrier_seal_number": full_doc.carrier_seal_number,
                        "weight_type": full_doc.weight_type,
                        "weight_value": full_doc.weight_value,
                        "cargo_gross_weight": full_doc.cargo[0].get("cargo_gross_weight") if full_doc.cargo else None,
                        "gross_volume": full_doc.cargo[0].get("gross_volume") if full_doc.cargo else None,
                        "volume_unit": full_doc.cargo[0].get("gross_volume_unit") if full_doc.cargo else None,
                        "package_count": full_doc.cargo[0].get("package_count") if full_doc.cargo else None,
                        "package_type_description": full_doc.cargo[0].get("package_type_description") if full_doc.cargo else None,
                    }
                    lst_equipment.append(dct_equipment)
                    dct_cargo = {
                        "hs_code": full_doc.cargo[0].get("hs_code") if full_doc.cargo else None,
                        "cargo_description": full_doc.cargo[0].get("cargo_description") if full_doc.cargo else None,
                        "cargo_gross_weight": full_doc.cargo[0].get("cargo_gross_weight") if full_doc.cargo else None,
                        "gross_volume": full_doc.cargo[0].get("gross_volume") if full_doc.cargo else None,
                        "volume_unit": full_doc.cargo[0].get("volume_unit") if full_doc.cargo else None,
                        "package_count": full_doc.cargo[0].get("package_count") if full_doc.cargo else None,
                        "package_type_description": full_doc.cargo[0].get("package_type_description") if full_doc.cargo else None,
                        "package_counttype_outermost": full_doc.cargo[0].get("package_counttype_outermost") if full_doc.cargo else None
                    }
                    lst_cargo.append(dct_cargo)
        clean_data["equipment"] = lst_equipment
        clean_data["cargo"] = lst_cargo
        clean_data["total_net_weight_caluclated"] = ''
        clean_data["weight_difference_warning"] = ''
        if total_net_weight_caluclated:
            clean_data["total_net_weight_caluclated"] = "{:.2f}".format(total_net_weight_caluclated)
            dbl_weight_difference = abs(float(clean_data.get("total_gross_weight") or 0) - (float(total_net_weight_caluclated) or 0) )
            if  dbl_weight_difference > 0:
                clean_data["weight_difference_warning"] = f"There is a difference of {round(dbl_weight_difference,2)} KG in the total gross weight and the calculated total net weight of the equipments. Please check the equipment details."

        results.append(clean_data)

        frappe.local.response["status_code"] = 200
        frappe.local.response["message"] = results
        

    except Exception as e:
        frappe.local.response["status_code"] = 500
        frappe.local.response["error"] = f"Failed processing: {str(e)}"
        raise


@frappe.whitelist(allow_guest=True)
def get_bill_of_lading_old(consignee=None, port_of_loading=None, carrier=None,search_text=None, page=1, limit=20):
    """
    Get Bill of Lading documents (cleaned format) with optional filters and pagination.
    - Filters: Consignee, Port of Loading, Carrier
    - Pagination: Defaults to 20 per page, accepts 'page' param
    """

    try:
        doctype = "Bill of Lading"
        filters = []
        or_filters = []

        # Carrier filter
        if carrier:
            carrier_record = None

            # Case 1: Carrier passed as primary key
            if frappe.db.exists("Carrier", carrier):
                carrier_record = frappe.get_value(
                    "Carrier",
                    carrier,
                    ["partyalias", "partyname1"],
                    as_dict=True
                )
            else:
                # Case 2: Carrier passed as text
                carrier_record = frappe.get_value(
                    "Carrier",
                    {"partyalias": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"],
                    as_dict=True
                ) or frappe.get_value(
                    "Carrier",
                    {"partyname1": ["like", f"%{carrier}%"]},
                    ["partyalias", "partyname1"],
                    as_dict=True
                )

            # Case 3: Fallback to Partner Information
            if not carrier_record:
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={
                        "partner_role": "Carrier",
                        "partner_name": ["like", f"%{carrier}%"]
                    },
                    pluck="parent"
                )
                if bol_names:
                    filters.append(["name", "in", bol_names])
                else:
                    frappe.local.response.update({
                        "status_code": 404,
                        "error": f"No Carrier found matching '{carrier}'"
                    })
                    return
            else:
                carrier_values = [carrier_record.partyalias, carrier_record.partyname1]
                carrier_values = [c for c in carrier_values if c]
                filters.append(["carrier", "in", carrier_values])

        
        if consignee:
            bol_names = []
            
            # Always resolve PK -> customer_name first
            if frappe.db.exists("Customer DB", consignee):
                customer_name = frappe.get_value("Customer DB", consignee, "customer_name")

                # Collect all matching BOLs (new + old)
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={"partner_role": "Consignee"},
                    or_filters=[
                        {"partner_table_name": consignee},             # new records
                        {"partner_name": ["like", f"%{customer_name}%"]}  # old records
                    ],
                    pluck="parent"
                )
            else:
                # fallback: maybe frontend sent invalid PK or only text
                bol_names = frappe.get_all(
                    "Partner Information",
                    filters={
                        "partner_role": "Consignee",
                        "partner_name": ["like", f"%{consignee}%"]
                    },
                    pluck="parent"
                )

            if not bol_names:
                frappe.local.response.update({
                    "status_code": 404,
                    "error": f"No Consignee found matching '{consignee}'"
                })
                return

            filters.append(["name", "in", bol_names])



        if port_of_loading: 
            filters.append(["port_of_load", "=", port_of_loading])

        if search_text:
            or_filters.extend([
                ["bol_number", "like", f"%{search_text}%"],
                ["carrier_booking_number", "like", f"%{search_text}%"],
                ["message_status", "like", f"%{search_text}%"],
            ])

            


        offset = (int(page) - 1) * int(limit)

        # Fetch only names with filters and pagination
        int_count = frappe.db.count(doctype, filters=filters)
        docs = []
        if all:  # fetch all records
            docs = frappe.get_all(
                doctype,
                filters=filters,
                or_filters=or_filters,
                fields=["name"],
                order_by="modified desc"
            )
        else:  
            offset = (int(page) - 1) * int(limit)
            docs = frappe.get_all(
                doctype,
                filters=filters,
                or_filters=or_filters,
                fields=["name"],
                start=offset,
                page_length=int(limit),
                order_by="modified desc"
            )

        results = []
        excluded_prefixes = ("section_break_", "column_break_")
        excluded_fields = {
            "doctype", "owner", "creation", "modified", "modified_by", "docstatus","xml_data","xml_file_name","master_data_section"
        }

        for d in docs:
            doc = frappe.get_doc(doctype, d.name)
            clean_data = {}
            clean_data["name"] = doc.name

            for df in doc.meta.fields:
                fieldname = df.fieldname
                if (
                    fieldname in excluded_fields
                    or any(fieldname.startswith(prefix) for prefix in excluded_prefixes)
                ):
                    continue

                if df.fieldtype == "Table":
                    pass
                else:
                    clean_data[fieldname] = doc.get(fieldname)

                docket_info = frappe.get_value(
                    "Docket DB",
                    {"blno": clean_data.get("bol_number")},
                    ["name", "status"],
                    as_dict=True
                )

                if docket_info:
                    clean_data["docket_id"] = docket_info.name
                    clean_data["docket_status"] = docket_info.status
                else:
                    clean_data["docket_id"] = None
                    clean_data["docket_status"] = None

                if doc.parties:
                    for party in doc.parties:
                        if party.partner_role == "Consignee":
                            clean_data["consignee"] = party.partner_name
                            break

                # ETA
                clean_data["eta"] = ""
                booking_request = frappe.get_value(
                    "Booking Request",
                    {"carrier_booking_number": doc.carrier_booking_number},
                    ["name"],
                    as_dict=True
                )  
                if booking_request:     
                    lst_main_carriage = frappe.get_all(
                        "Booking Main Carriage", 
                        filters={"parent": booking_request.name}, 
                        fields=["port_of_discharge", "etd", "eta"],
                        order_by="etd asc"
                    )
                    lst_main_carriage = sorted(
                        lst_main_carriage,
                        key=lambda x: x.get("etd") or datetime.min
                    )
                    if lst_main_carriage:
                        clean_data["eta"] = lst_main_carriage[-1].get("eta") or ""

            # 🔹 Add latest attachments 
            attachments = []
            if doc.bol_attachments:
                sorted_attachments = sorted(
                    doc.bol_attachments,
                    key=lambda x: (x.order or 0, x.modified or datetime.min),
                    reverse=True
                )
                latest = sorted_attachments[0]  
                attachments.append({
                    "file_name": latest.file_name,
                    "file_url": latest.file_url,
                    "order": latest.order
                })

            clean_data["attachments"] = attachments

            results.append(clean_data)

        frappe.local.response["status_code"] = 200
        frappe.local.response["message"] = results
        frappe.local.response["total_count"] = int(int_count)
        frappe.local.response["page"] = int(page)
        frappe.local.response["limit"] = int(limit)

    except Exception as e:
        frappe.local.response["status_code"] = 500
        frappe.local.response["error"] = f"Failed processing: {str(e)}"
        raise





@frappe.whitelist(allow_guest=True)
def make_equipment_inactive(equipment_id):
    """
    Mark an Equipment as inactive and update the corresponding Docket DB 'containers' field
    based on active equipment count for that carrier booking number.
    """
    try:
        if not equipment_id:
            return {
                "status_code": 400,
                "message": "Equipment ID is required."
            }

        # Fetch Equipment document
        equipment_doc = frappe.get_doc("Equipments", equipment_id)
        carrier_booking_number = equipment_doc.carrier_booking_number
        equipment_type = equipment_doc.container_type_id  # e.g., 45G0, 20GP, etc.

        if not carrier_booking_number:
            return {
                "status_code": 400,
                "message": "Carrier Booking Number is missing in Equipment."
            }

        # Mark equipment as inactive
        equipment_doc.is_active = 0
        equipment_doc.save(ignore_permissions=True)

        # Fetch count of active equipments with same booking number
        active_count = frappe.db.count(
            "Equipments",
            filters={
                "carrier_booking_number": carrier_booking_number,
                "is_active": 1
            }
        )

        # Fetch the related Docket DB record (based on carrier booking number)
        docket_name = frappe.db.get_value(
            "Docket DB",
            {"carrier_booking_number": carrier_booking_number},
            "name"
        )

        if docket_name:
            docket_doc = frappe.get_doc("Docket DB", docket_name)

            # Example format: "3X45G0" → Replace "3" with the new active_count
            # Only update if containers field is not empty
            if docket_doc.containers:
                import re
                updated_containers = re.sub(
                     r"^\d+(?=[xX])",
                    str(active_count),
                    docket_doc.containers.strip(),
                    flags=re.IGNORECASE
                )
                docket_doc.containers = updated_containers
                docket_doc.save(ignore_permissions=True)
                frappe.db.commit()

            return {
                "status_code": 200,
                "message": f"Equipment made inactive successfully. Active count: {active_count}",
                "carrier_booking_number": carrier_booking_number,
                "updated_containers": docket_doc.containers
            }

        else:
            # Commit even if no Docket found (since equipment was updated)
            frappe.db.commit()
            return {
                "status_code": 200,
                "message": f"Equipment made inactive successfully, but no Docket DB record found for {carrier_booking_number}.",
                "carrier_booking_number": carrier_booking_number,
                "active_count": active_count
            }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Make Equipment Inactive Error")
        return {
            "status_code": 500,
            "message": f"Error: {str(e)}"
        }
