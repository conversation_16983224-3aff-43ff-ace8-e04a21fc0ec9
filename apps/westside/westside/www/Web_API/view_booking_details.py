import json
import frappe
from frappe.utils import cstr

@frappe.whitelist(allow_guest=True)
def get_booking_request_details(name=None):
    if not name:
     name = frappe.form_dict.get('name')


    if not name:
        return {'message': "Booking Details Not Available."}

    try:
        booking_request = frappe.get_doc('Booking Request', name)
        if not booking_request:
            return {'message': "Booking Details Not Available."}

        result = {
            'name': booking_request.name,
            'modified_date': booking_request.modified,
            'booking_status': booking_request.booking_status,
            'contract_number': booking_request.contract_number,
            'earliest_departure_date': booking_request.earliest_departure_date,
            'carrier_booking_number': booking_request.carrier_booking_number,
            'doc_cut_of_date': booking_request.doc_cut_of_date,
            'port_cut_of_date': booking_request.port_cut_of_date,
            'barge_cut_of_date': booking_request.barge_cut_of_date,
            'forwarder': booking_request.forwarder,
            'contract_party': booking_request.contract_party,
            "shipper_on_booking" : booking_request.shipper_on_booking ,
            "consignee_on_booking" : booking_request.consignee_on_booking,
            "forwarder_on_booking" : booking_request.forwarder_on_booking,
            "contract_party_on_booking" : booking_request.contract_party_on_booking,
            "notify_party_on_booking" : booking_request.notify_party_on_booking,
            "customs_broker_on_booking" : booking_request.customs_broker_on_booking,
            "notify_party_1_on_booking" : booking_request.notify_party_1_on_booking,
            "notify_party_2_on_booking" : booking_request.notify_party_2_on_booking,
            'shippers_reference_numbers': booking_request.shippers_reference_numbers,
            'forwarders_reference_numbers': booking_request.forwarders_reference_numbers,
            'purchase_order_numbers': booking_request.purchase_order_numbers,
            'contract_party_reference_numbers': booking_request.contract_party_reference_numbers,
            'consignees_reference_numbers': booking_request.consignees_reference_numbers,
            'bl_reference_numbers': booking_request.bl_reference_numbers,
            'customer_shipment_id': booking_request.customer_shipment_id,
            'move_type': booking_request.move_type,
            'latest_delivery_date': booking_request.latest_delivery_date,
            'customer_comments': booking_request.customer_comments,
            'partner_email_notifications': booking_request.partner_email_notifications,
            'notify_me_regarding_the_status_and_update_of_this_booking': booking_request.notify_me_regarding_the_status_and_update_of_this_booking,
            'inttra_response_status': booking_request.inttra_response_status,
            'inttra_booking_id': booking_request.inttra_booking_id,
            'inttra_reference': booking_request.inttra_reference,
            'response': booking_request.response,
            'si_due_date': booking_request.si_due_date,
            'vgm_due_date': booking_request.vgm_due_date,
            'product_channel': booking_request.product_channel,
            'add_pre_carriage': booking_request.add_pre_carriage,
            'add_on_carriage': booking_request.add_on_carriage,
            "first_foreign_port_of_acceptance": booking_request.first_foreign_port_of_acceptance,
            "last_non_us_port": booking_request.last_non_us_port,
            "final_port_for_ams_documentation": booking_request.final_port_for_ams_documentation,
            "first_us_port": booking_request.first_us_port,
            "estimated_arrival_date": booking_request.estimated_arrival_date,
            "ams_filing_due_date": booking_request.ams_filing_due_date,
            "general_comments": booking_request.general_comments,
            "carrier_source_booking_number":booking_request.carrier_source_booking_number,
            "parent_carrier_booking_number":booking_request.parent_carrier_booking_number

        }

        # Optional link fields
        def safe_get_doc(doctype, name):
            try:
                return frappe.get_doc(doctype, name) if name else None
            except frappe.DoesNotExistError:
                return None

        place_of_carrier_receipt = safe_get_doc('UNLOCODE Locations', booking_request.place_of_carrier_receipt)
        place_of_carrier_delivery = safe_get_doc('UNLOCODE Locations', booking_request.place_of_carrier_delivery)
        booking_office = safe_get_doc('UNLOCODE Locations', booking_request.booking_office)
        shipper = safe_get_doc('Shipper', booking_request.shipper)
        booking_agent = safe_get_doc('Carrier', booking_request.booking_agent)

        if place_of_carrier_receipt:
            result['place_of_carrier_receipt'] = f"{place_of_carrier_receipt.locode}, {place_of_carrier_receipt.location_name},{place_of_carrier_receipt.country_code} " 
        if place_of_carrier_delivery:
            result['place_of_carrier_delivery'] = f"{place_of_carrier_delivery.locode}, {place_of_carrier_delivery.location_name},{place_of_carrier_delivery.country_code} "
            

        if booking_office:
            result['booking_office'] = booking_office.location_name

        result['shipper'] = []
        if shipper:
            result['shipper'].append({
                'name': shipper.name,
                'shipper_name': shipper.shipper_name,
                'shipper_code': shipper.shipper_code,
                'shipper_details': shipper.shipper_details,
                'shipper_phone': shipper.phone,
                'shipper_email': shipper.email,
                'shipper_address': shipper.custom_address,
                'inttra_company_id': shipper.inttra_company_id,
                'subscription': shipper.subscription,
                'postal_code': shipper.postal_code,
                'contact_name': shipper.contact_name
            })

        result['carrier'] = []
        if booking_agent:
            result['carrier'].append({
                'name': booking_agent.name,
                'inttra_id': booking_agent.inttra_id,
                'address': booking_agent.address,
                'postal_code': booking_agent.postal_code,
                'country_code': booking_agent.country_code,
                'carrier_sac': booking_agent.partyalias,
                'carrier_name': booking_agent.partyname1,
            })

        # Contract Party
        result['contract_party'] = []
        if booking_request.contract_party:
            party_id = booking_request.contract_party
            fields = []
            party_type = None
            if party_id.startswith("CUS-"):
                party_type = "Customer DB"
                fields = ["name", 
                        "customer_name as party_name",
                        "customer_country as country",
                        "phone as phone",
                        "customer_address as address",
                        "email_id as email",
                        "customer_zip as postal_code",
                        ]
            elif party_id.startswith("SHI-"):
                party_type = "Shipper"
                fields = ["name",
                        "shipper_name as party_name",
                        "email as email",
                        "phone as phone",
                        "custom_address as address",
                        "postal_code as postal_code",
                        "country as country"
                    ]
            
            party_data = frappe.get_all(
                party_type,
                filters={"name": party_id},
                fields=fields,
                limit_page_length=1
            ) if party_type else []
            
            if party_data:
                party = party_data[0]
                party["type"] = party_type.replace(" DB", "")  
                result["contract_party"].append(party)

        elif booking_request.contract_party_on_booking:
            result["contract_party"].append({
                "party_name": booking_request.contract_party_on_booking,
                "phone": None,
                "email": None,
                "address": None,
                "postal_code": None,
                "country": None,
                "type": "Manual"
            })


        # Consignee
        result['consignee'] = []
        consignee = safe_get_doc('Customer DB', booking_request.consignee)
        if consignee:
            result['consignee'].append({
                'name': consignee.name,
                "consigne_name":consignee.customer_name,
                'company_pass_through_code': consignee.inttra_company_id,
                'address': consignee.customer_free_form_address,
                'postal_code': consignee.customer_zip,
            })

        result['main_notify_party'] = []
        consignee = safe_get_doc('Customer DB', booking_request.notify_parties)
        if consignee:
            result['main_notify_party'].append({
                'name': consignee.name,
                "consigne_name":consignee.customer_name,
                'company_pass_through_code': consignee.inttra_company_id,
                'address': consignee.customer_free_form_address,
                'postal_code': consignee.customer_zip,
            })

        result['additional_notify_party_one'] = []
        consignee = safe_get_doc('Customer DB', booking_request.notify_party_1)
        if consignee:
            result['additional_notify_party_one'].append({
                'name': consignee.name,
                "consigne_name":consignee.customer_name,
                'company_pass_through_code': consignee.inttra_company_id,
                'address': consignee.customer_free_form_address,
                'postal_code': consignee.customer_zip,
            })

        result['additional_notify_party_two'] = []
        consignee = safe_get_doc('Customer DB', booking_request.notify_party_2)
        if consignee:
            result['additional_notify_party_two'].append({
                'name': consignee.name,
                "consigne_name":consignee.customer_name,
                'company_pass_through_code': consignee.inttra_company_id,
                'address': consignee.customer_free_form_address,
                'postal_code': consignee.customer_zip,
            })


        # Booking Carriages
        result['booking_carriages'] = []
        for bc in frappe.get_all('Booking Carriage', filters={'parent': booking_request.name}, fields=['start', 'etd', 'end', 'eta', 'mode']) or []:
            start = safe_get_doc('UNLOCODE Locations', bc.start)
            end = safe_get_doc('UNLOCODE Locations', bc.end)
            result['booking_carriages'].append({
                'start': start.locode if start else '',
                'start_country_code': start.country_code if start else '',
                'start_location_name': start.location_name if start else '',
                'etd': bc.etd,
                'end': end.locode if end else '',
                'end_country_code': end.country_code if end else '',
                'end_location_name': end.location_name if end else '',
                'eta': bc.eta,
                'mode': bc.mode
            })
        result['booking_carriages'].reverse()  # Reverse the order to match the original logic
        # Containers
        result['containers'] = []
        for container in booking_request.containers or []:
            container_details = {}
            if container.container_quantitytype:
                container_details['container_type_details'] = frappe.get_value(
                    'Container Type', container.container_quantitytype,
                    ['name', 'typecode', 'groupcode', 'typecategory', 'description', 'shortdescription', 'listheight', 'height', 'listlength', 'length', 'listwidth', 'width', 'spareindicator', 'nonstandardtype', 'displayflag', 'displaycategory'],
                    as_dict=True
                )
            result['containers'].append(container_details)

        

        # Booking Container(or Equipment)
        result['Booking_Container'] = []
        doc_equipments = frappe.get_all(
            "Equipments",
            filters={"booking_request": name, "is_active": 1},
            fields=["count","equipment_name","supplier_type","service_type","code_value","description","weight_value","weight_type","comment","response_haulage_details"]  
        )

        if doc_equipments:
            for equipment in doc_equipments:
                result['Booking_Container'].append({
                    'number_of_containers': equipment.count,
                    'container_name': equipment.equipment_name,
                    'supplier_type': equipment.supplier_type,
                    'service_type': equipment.service_type,
                    'container_quantitytype': equipment.code_value,
                    'container_descp': equipment.description,
                    'weight_value': equipment.weight_value,
                    'weight_type': equipment.weight_type,
                    'container_comments': equipment.comment,
                    'haulage_detail' : json.loads(equipment.response_haulage_details) if equipment.response_haulage_details else '',
                })
        else:
            for container in booking_request.containers or []:
                result['Booking_Container'].append({
                    'container_quantitytype': container.container_quantitytype,
                    'container_descp': container.container_descp,
                    'container_name': container.container_name,
                    'number_of_containers': container.number_of_containers,
                    'container_comments': container.container_comments,
                    'shipper_owned': container.shipper_owned,
                    'company_name': container.company_name,
                    'address': container.address,
                    'requested_empty_pick_up_date': container.requested_empty_pick_up_date,
                    'time': container.time,
                    'contact_name': container.contact_name,
                    'contact_phone_number': container.contact_phone_number,
                    'haulage_detail' : json.loads(container.haulage_detail) if container.haulage_detail else '',
                })


        # Haulage Arrangement details based on move_type
        haulage_arrangement = {}
        if booking_request.move_type == 'Port, Ramp, or CY to Port, Ramp, or CY':
            
            haulage_arrangement['description'] = 'Port,Ramp,CY/CFS to Port,Ramp,CY/CFS'
            haulage_arrangement['arrangement'] = 'MerchantMerchant'

        elif booking_request.move_type == 'Door to Port, Ramp, or CY':
            haulage_arrangement['description'] = 'Door to Port,Ramp,CY/CFS'
            haulage_arrangement['arrangement'] = 'CarrierMerchant'
        
        elif booking_request.move_type == 'Door to Door':
            haulage_arrangement['description'] = 'Door To Door'
            haulage_arrangement['arrangement'] = 'CarrierCarrier'

        elif booking_request.move_type == 'Port, Ramp, or CY to Door':
            haulage_arrangement['description'] = 'Port,Ramp,CY/CFS to Door'
            haulage_arrangement['arrangement'] = 'MerchantCarrier'
            
        else:
            haulage_arrangement['description'] = 'Unknown'
            haulage_arrangement['arrangement'] = 'Unknown'

        result['haulage_arrangement'] = haulage_arrangement

        # Cargo
        result['cargo'] = []
        for cargo in booking_request.cargo or []:
            loc = safe_get_doc('UNLOCODE Locations', cargo.origin_of_goods)
            cargo_details = {
                'description': cargo.cargo_description,
                'location': loc.location_name if loc else '',
                'net_weight': "{:.2f}".format(float(cargo.net_weight)) if cargo.net_weight else 0,
                'cargo_gross_weight': "{:.2f}".format(float(cargo.cargo_gross_weight)) if cargo.cargo_gross_weight else 0 ,
                'gross_volume': "{:.2f}".format(float(cargo.gross_volume)) if cargo.gross_volume else 0,
                'net_weight_unit': cargo.net_weight_unit,
            }
            if cargo.hs_code:
                cargo_details['hs_code_details'] = frappe.get_value(
                    'HS Code', cargo.hs_code,
                    ['hs_code', 'chapter_code', 'chapter_description', 'sub_chapter_code', 'sub_chapter_description'],
                    as_dict=True
                )
            result['cargo'].append(cargo_details)

        # Main Carriage
        result['main_carriage'] = []
        for carriage in sorted(booking_request.main_carriage or [], key=lambda x: x.etd or frappe.utils.now_datetime()):
            carriage_details = {
                'voyage_number': carriage.voyage,
                'vessel_name': carriage.vessel,
                'eta': carriage.eta,
                'etd': carriage.etd,
                'country': carriage.country,
                'lloyds_code': carriage.lloyds_code,
                'carrier_code':carriage.carrier,
                'mode':carriage.mode,
                'mean':carriage.mean
            }
            if carriage.port_of_load:
                carriage_details['port_of_load'] = frappe.get_value(
                    'UNLOCODE Locations', carriage.port_of_load,
                    ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True
                )
            if carriage.port_of_discharge:
                carriage_details['port_of_discharge'] = frappe.get_value(
                    'UNLOCODE Locations', carriage.port_of_discharge,
                    ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True
                )
            result['main_carriage'].append(carriage_details) 
        # Pre-Carriage
        result['pre_carriage'] = []
        # for pre_carriage in booking_request.add_pre_carriage or []:
        for pre_carriage in sorted(booking_request.add_pre_carriage or [], key=lambda x: x.etd or frappe.utils.now_datetime()):
            if pre_carriage:
                result['pre_carriage'].append({
                    'start': frappe.db.get_value('UNLOCODE Locations', pre_carriage.start, ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True) or '',
                    'end': frappe.db.get_value('UNLOCODE Locations', pre_carriage.end, ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True) or '',
                    'eta': pre_carriage.eta,
                    'etd': pre_carriage.etd,
                    'mode': pre_carriage.mode,
                    'mean': pre_carriage.mean,
                    'carrier_code': pre_carriage.carrier_code,
                    'county':pre_carriage.country
                })
        
        # On-Carriage
        result['on_carriage'] = []
        for on_carriage in sorted(booking_request.add_on_carriage or [], key=lambda x: x.etd or frappe.utils.now_datetime()):
            if on_carriage:
                result['on_carriage'].append({
                    'start': frappe.db.get_value('UNLOCODE Locations', on_carriage.start, ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True) or '',
                    'end': frappe.db.get_value('UNLOCODE Locations', on_carriage.end, ['locode', 'country', 'country_code', 'location_name'],
                    as_dict=True) or '',
                    'eta': on_carriage.eta,
                    'etd': on_carriage.etd,
                    'mode': on_carriage.mode,
                    'mean': on_carriage.mean,
                    'carrier_ode': on_carriage.carrier_code,
                    'county':on_carriage.country
                })
        
        # Notify Parties
        result['notify_parties'] = []
        # for party_name in booking_request.notify_parties or []:
        #     party = safe_get_doc('Notify Party', party_name)
        #     if party:
        #         result['notify_parties'].append({
        #             'name': party.name1,
        #             'contact': party.phone,
        #             'address': party.address,
        #             'fax': party.fax,
        #             'email': party.email,
        #             'postal_code': party.postal_code,
        #             'country': party.country
        #         })

        # Payment
        result['payment'] = []
        for payment in booking_request.payment or []:
            loc = safe_get_doc('UNLOCODE Locations', payment.payment_location)
            result['payment'].append({
                'charge_type': payment.charge_type,
                'payment_term': payment.payment_term,
                'payer': payment.payer,
                'payment_location_name': loc.location_name if loc else ''
            })
        
        draft_si = frappe.get_all(
            "Shipping Instructions",
            filters={
                "booking_request_id": booking_request.name,
                "carrier_booking_number": booking_request.carrier_booking_number
            },
            fields=["name", "main_status"],
            limit_page_length=1
        )

        if draft_si:
            result["draft_si_id"] = draft_si[0].get("name")
            result["si_main_status"] = draft_si[0].get("main_status")
        else:
            result["draft_si_id"] = None
            result["si_main_status"] = None


        return result

    except frappe.DoesNotExistError:
        return {'message': "Booking Details Not Available."}
    except Exception as e:
        frappe.log_error(f"Error in get_booking_request_details: {str(e)}")
        return {'message': f"Error retrieving booking request details: {str(e)}"}


