import os
import json
import frappe
from frappe import _
from frappe.utils.pdf import get_pdf
from frappe.utils.file_manager import save_file
from jinja2 import Environment, FileSystemLoader, StrictUndefined
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
def add_docket_comment(docket_id, revision_id, comment_text):
    if not comment_text:
        return {"status": 400, "message": "Comment text is required."}

    try:
        if not frappe.db.exists("Docket DB", docket_id):
            return {"status": 404, "message": "Docket not found."}

        if not frappe.db.exists("Docket Revision", revision_id):
            return {"status": 404, "message": "Docket Revision not found."}

        username = ""
        if frappe.session.user:
            user = frappe.get_doc("User", frappe.session.user)
            username = user.username
        
        comment = frappe.get_doc({
            "doctype": "Comment Record",
            "docket_id": docket_id,
            "revision_id": revision_id,
            "comment_text": comment_text,
            "commented_by": frappe.session.user,
            "commented_by_name": username
        }) 
         
        comment.insert(ignore_permissions=True)
        frappe.db.commit()

        return {"status": 200, "message": "Comment added successfully."}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Add Docket Comment Error")
        return {"status": 500, "message": str(e)}
    
 
@frappe.whitelist()
def get_docket_comments(docket_id, revision_id=None, sort_order="asc"):
    try:
        if not docket_id:
            return {"status": 400, "message": "Docket ID is required."}

        if not frappe.db.exists("Docket DB", docket_id):
            return {"status": 404, "message": "Docket not found."}

        filters = {"docket_id": docket_id}
        if revision_id:
            if not frappe.db.exists("Docket Revision", revision_id):
                return {"status": 404, "message": "Docket Revision not found."}
            filters["revision_id"] = revision_id

        order_by = "creation asc" if sort_order == "asc" else "creation desc"

        comments = frappe.get_all(
            "Comment Record",
            filters=filters,
            fields=[
                "name", "comment_text", "commented_by", "commented_by_name",
                "revision_id", "docket_id", "creation"
            ],
            order_by=order_by
        )
        return {
            "status": 200,
            "comments": comments
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Docket Comments Error")
        return {"status": 500, "message": str(e)}



@frappe.whitelist()
@role_required(["Admin"])
def add_additional_attachment(docket_id, revision_id):
    try:
        if not (docket_id and revision_id):
            frappe.throw(_("Missing required parameters"))

        if not frappe.db.exists("Docket DB", docket_id):
            return {"status": 404, "message": "Docket not found."}

        if not frappe.db.exists("Docket Revision", revision_id):
            return {"status": 404, "message": "Docket Revision not found."}

        # Get uploaded files
        additional_attachments = frappe.request.files.getlist("additional_attachments")

        if not additional_attachments:
            return {"status": 400, "message": "No files uploaded."}

        site_url = frappe.utils.get_url()

        for additional_attachment in additional_attachments:
            file_doc = save_file(
                additional_attachment.filename,
                additional_attachment.stream.read(),
                "Docket DB",
                revision_id,
                is_private=0
            )
            
            frappe.get_doc({
                "doctype": "Additional Revision Attachments",
                "docket_revision": revision_id,  
                "docket": docket_id,
                "attachments": file_doc.file_url,
                "file_name": additional_attachment.filename
            }).insert(ignore_permissions=True)

        frappe.db.commit()

        return {
            "status": 200,
            "message": "Files attached successfully."
        }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Add Additional Attachment Error")
        return {
            "status": 500,
            "message": f"Error adding attachments: {str(e)}"
        }



@frappe.whitelist()
@role_required(["Admin"])
def remove_additional_attachment(docket_id, revision_id, file_id):
    try:
        if not (docket_id and revision_id and file_id):
            return {"status": 400, "message": "Missing required parameters."}

        if not frappe.db.exists("Docket DB", docket_id):
            return {"status": 404, "message": "Docket not found."}

        if not frappe.db.exists("Docket Revision", revision_id):
            return {"status": 404, "message": "Docket Revision not found."}

        attachment_doc = frappe.get_doc("Additional Revision Attachments", file_id)

        if attachment_doc.docket != docket_id or attachment_doc.docket_revision != revision_id:
            return {
                "status": 403,
                "message": "Attachment does not belong to the given docket or revision."
            }

        attachment_doc.delete()
        frappe.db.commit()

        return {
            "status": 200,
            "message": "Attachment removed successfully."
        }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Remove Additional Attachment Error")
        return {
            "status": 500,
            "message": f"Error removing attachment: {str(e)}"
        }




@frappe.whitelist()
@role_required(["Admin"])
def update_docket_status(docket_id, revision_id, status):
    try:
        if not docket_id:
            return {'status': 400, 'message': 'DocketId is required.'}
        
        if not revision_id:
            return {"status": 400, "message": "Revision ID is required."}

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': 404, 'message': 'Docket not found'}
        
        # Search for child row within parent
        docket_revision = None
        for rev in docket.docket_revisions:
            if rev.revision_number == revision_id:
                docket_revision = rev
                break

        if not docket_revision:
            return {"status": 404, "message": "Docket Revision not found."}
        
        docket_revision.status = status
        docket.status = status
        docket.save(ignore_permissions=True)
        
        docket.save(ignore_permissions=True)
        frappe.db.commit()
        
        return { "status": 200, "message": "Docket revision status updated successfully", "docket": docket.name, "docket_revision": docket_revision.revision_number, "updated_status": status }

    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Update Docket status API Error")
        return {"status": 500, "message": {"error": str(e)}}
