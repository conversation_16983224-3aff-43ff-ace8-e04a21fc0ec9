import re
import json
import base64
import datetime
import frappe
import requests
from frappe import _
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required


@frappe.whitelist()
@role_required(["Admin"])
def get_booking_request(booking_id):

    try:
        booking = frappe.get_doc("Booking Request", booking_id)

        if not booking:
            return { 'status_code': 404, "message": "Booking request not found"}

        cargo = None
        hs_code = None
        if booking.cargo:
            cargo = frappe.get_doc("Cargo", booking.cargo)

            if cargo:
                hs_code = frappe.get_doc("HS Code", booking.cargo[0].hs_code)
        
        port_of_origin = None
        if booking.main_carriage:
            lst_main_carriage = frappe.get_all(
                "Booking Main Carriage", 
                filters={"parent": booking.name}, 
                fields=["voyage", "vessel", "port_of_load", "port_of_discharge", "etd", "eta"],
                order_by="etd asc"
            )
            lst_main_carriage = sorted(lst_main_carriage, key=lambda x: x.get("etd") or datetime.min)
            port_of_origin = frappe.get_value(
                "UNLOCODE Locations",
                lst_main_carriage[0].get("port_of_load"),
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            ) 


        no_of_containers = 0
        container_list = frappe.get_all(
                "Booking Container",
                filters={"parent": booking.name},
                fields=["number_of_containers"]
        )
        no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)

        return {
            "status": "success",
            "data": {
                "booking_id": booking.name,
                "port_of_origin": port_of_origin,
                "port_cut_off_date": booking.vgm_due_date,
                "doc_cut_off_date": booking.si_due_date,
                "no_of_containers": no_of_containers,
                "barge_cut_off_date": booking.vgm_due_date,
                "inttra_reference": booking.inttra_reference,
                "carrier_booking_number": booking.carrier_booking_number,
                "hs_code" :  hs_code.name if hs_code and hs_code.name else None,
                "commodity_description": hs_code.hs_code_description if hs_code and hs_code.hs_code_description else None,
                "si_due_date" : booking.si_due_date,
                "vgm_due_date": booking.vgm_due_date
            }
        }

    except frappe.DoesNotExistError:
        frappe.response["http_status_code"] = 400
        frappe.response["message"] = {"error": "Booking Request not found."}
        return

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "get_booking_request_error")
        frappe.throw(_("An unexpected error occurred: ") + str(e))



@frappe.whitelist()
@role_required(["Admin"])
def get_assigned_containers(name=None):
    if not name:
        return {
            "status_code": 422,
            "message": "Booking Id is required."
        }

    try:
        booking_request = frappe.get_doc('Booking Request', name)
        
        # get the number of containers requested to intrra.
        no_of_containers = 0
        container_list = frappe.get_all(
                "Booking Container",
                filters={"parent": booking_request.name},
                fields=["number_of_containers"]
        )
        no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)

        result = {
            'name': booking_request.name,
            'no_of_containers': no_of_containers,
        }
    
        equipments = frappe.get_all('Equipments', 
                filters={'booking_request': booking_request.name, "is_active": True}, 
                fields=[
                    'name', 'equipment_name', 'code_value', 'shipper_seal_number', 'carrier_seal_number',
                    'description', 'supplier_type', 'service_type','weight_value', 'weight_type', 'job',
                    'tare_weight', 'cargo_weight', 'gross_weight', 'max_weight', 'cubic_capacity', "creation"
                ], 
                order_by='creation desc'
            )
        # Count only the equipments that have a non-empty 'job' field
        job_count = len([e for e in equipments if e.get('job')])
        result['number_of_assigned_containers'] = job_count
        # result['number_of_assigned_containers'] = len(equipments)
        
        result['equipments'] = []
        site_url = frappe.utils.get_url()

        vendors = []
        # Attach container goods images and cargo to each equipment
        for equipment in equipments:

            container_goods = frappe.get_all('Container goods image', 
                filters={'parent': equipment['name'], 'parenttype': 'Equipments'},
                fields=['container_image', "file_name"]) 

            equipment_images = []
            for row in container_goods:
                full_image_url = row.container_image if row.container_image else ""
                file_name = row.file_name if row.file_name else "file_name.jpg"
                equipment_images.append({"image": full_image_url, "file_name": file_name})
                
            # Attach images to the equipment
            equipment['container_goods_images'] = equipment_images

            # Step 2: Add vendor name from job if exists
            if equipment.get('job'):
                try:
                    job_doc = frappe.get_doc("Job", equipment['job'])
                    if job_doc.vendor_name:
                        vendor_doc = frappe.get_doc("Vendor", job_doc.vendor_name)
                        equipment['assigned_vendor_name'] = vendor_doc.vendor_name

                except Exception as e:
                    frappe.log_error(f"Error fetching Job doc for equipment {equipment['name']}: {str(e)}")

        
        result["assigned_equipments"] = equipments
        # result["all_assigned_vendors"] = vendors

        return {
            "status_code": 200,
            "message": "Booking Request fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Booking Request not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in get_assigned_containers: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }



@frappe.whitelist()
@role_required(["Admin"])
def get_vendors():
    try:
        result = {
            'vendors': [],
        }
        vendors = frappe.get_all('Vendor',  filters={'is_active': True}, fields=['name', 'vendor_name'])
        result['vendors'] = vendors

        return {
            "status_code": 200,
            "message": "Vendors fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Vendors not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in vendors: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }



# API Not used
@frappe.whitelist(allow_guest=True)
def get_commodities():
    try:
        result = {
            'commodities': [],
        }
        cargo = frappe.get_all('Cargo', fields=['name', 'commodity_name', 'cargo_description'])
        result['commodities'] = cargo

        return {
            "status_code": 200,
            "message": "Commodities fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Commodities not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in vendors: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }

 
@frappe.whitelist(allow_guest=True)
def get_hs_codes():
    try:
        result = {
            'hs_code': [],
        }
        hs_codes = frappe.get_all('HS Code', fields=['name', 'hs_code', 'hs_code_description'])
        result['hs_code'] = hs_codes

        return {
            "status_code": 200,
            "message": "Hs Codes fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "HS codes not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in vendors: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }



@frappe.whitelist( methods=["POST"]) 
@role_required(["Admin"])
def create_job():
    try:
        data_str = frappe.form_dict.get("data")
        
        if not data_str:
            return { "status_code": 400, "message": "Invalid request: No data provided"}
        
        try:
            data = json.loads(data_str)
        except json.JSONDecodeError as e:
            return { "status_code": 500, "message": f"Invalid JSON format: {str(e)}"}

        vendor_name = data.get("vendor_name")
        commodity_description =  data.get("commodity_description")
        hs_code = data.get("hs_code")
        booking_id = data.get("booking_id")
        port_of_origin = data.get("port_of_origin")

        carrier_booking_number = None
        if booking_id:
            carrier_booking_number = frappe.db.get_value(
                "Booking Request",  
                booking_id,         
                "carrier_booking_number"  
            )

        # Validations
        if vendor_name and not frappe.db.exists("Vendor", vendor_name):
            frappe.throw(_(f"Vendor name '{vendor_name}' does not exist."))

        if hs_code and not frappe.db.exists("HS Code", hs_code):
            frappe.throw(_(f"HS Code '{hs_code}' does not exist."))
        
        if port_of_origin and not frappe.db.exists("UNLOCODE Locations", port_of_origin):
            frappe.throw(_(f"Port of origin '{port_of_origin}' does not exist."))

        no_of_containers = 0
        container_list = frappe.get_all(
                "Booking Container",
                filters={"parent": booking_id},
                fields=["number_of_containers"]
        )
        no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)

        job_doc = frappe.get_doc({
            "doctype": "Job",
            "booking_id": booking_id,
            "carrier_booking_number": carrier_booking_number if carrier_booking_number else None,
            "port_of_origin": data.get("port_of_origin"),
            "doc_cut_of_date": data.get("doc_cut_of_date"),
            "port_cut_of_date": data.get("port_cut_of_date"),
            "barge_cut_of_date": data.get("barge_cut_of_date"),
            "vendor_name": vendor_name,
            "hs_code": hs_code,
            "commodity" : commodity_description,
            "no_of_containers": no_of_containers
        })

        # Insert first      
        job_doc.insert(ignore_permissions=True)
        si_message = None
        if booking_id:
            booking_doc = frappe.get_doc("Booking Request", booking_id)

            booking_data = frappe.db.get_value(
                "Booking Request",
                booking_id,
                [
                    "carrier_booking_number",
                    "shipper",
                    "consignee",
                    "notify_parties",
                    "inttra_reference",
                    "move_type",
                    "place_of_carrier_receipt",
                    "place_of_carrier_delivery",
                    "booking_agent"
                ],
                as_dict=True
            )
            if booking_data:
                carrier_booking_number = booking_data.get("carrier_booking_number")

                existing_si = frappe.db.exists(
                    "Shipping Instructions",
                    {
                        "carrier_booking_number": carrier_booking_number,
                        "main_status": "Draft"
                    }
                )

                if existing_si:
                    frappe.logger().info(f"Draft Shipping Instruction already exists for Carrier Booking {carrier_booking_number}: {existing_si}")
                    si_message = f"Draft Shipping Instruction already exists for Carrier Booking {carrier_booking_number}: {existing_si}"
                else:
                    move_type_map = {
                        "Port, Ramp, or CY to Port, Ramp, or CY": "Port,Ramp,CY/CFS to Port,Ramp,CY/CFS",
                        "Door to Door": "Door To Door",
                        "Door to Port, Ramp, or CY": "Door to Port,Ramp,CY/CFS",
                        "Port, Ramp, or CY to Door": "Port,Ramp,CY/CFS to Door"
                    }
                    mapped_move_type = move_type_map.get(booking_data.get("move_type"), "")

                    port_of_load_code = ""
                    port_of_discharge_code = ""
                    vessel = ""
                    voyage = ""
                    port_of_load_meta = {}
                    port_of_discharge_meta = {}

                    if booking_doc.main_carriage:
                        lst_main_carriage = sorted(
                            booking_doc.main_carriage,
                            key=lambda x: x.etd or datetime.datetime.min
                        )
                        main_carriage_load = lst_main_carriage[0]
                        main_carriage_disc = lst_main_carriage[-1]
                        port_of_load_code = main_carriage_load.port_of_load or ""
                        port_of_discharge_code = main_carriage_disc.port_of_discharge or ""
                        vessel = main_carriage_load.vessel or ""
                        voyage = main_carriage_load.voyage or ""

                        if port_of_load_code:
                            try:
                                port_of_load_meta = frappe.get_doc(
                                    "UNLOCODE Locations", port_of_load_code
                                ).as_dict()
                            except frappe.DoesNotExistError:
                                port_of_load_meta = {}

                        if port_of_discharge_code:
                            try:
                                port_of_discharge_meta = frappe.get_doc(
                                    "UNLOCODE Locations", port_of_discharge_code
                                ).as_dict()
                            except frappe.DoesNotExistError:
                                port_of_discharge_meta = {}

                    si_doc = frappe.get_doc({
                        "doctype": "Shipping Instructions",
                        "booking_request_id": booking_id,
                        "carrier": booking_data.get("booking_agent"),
                        "carrier_booking_number": carrier_booking_number,
                        "shipper": booking_data.get("shipper"),
                        "consignee": booking_data.get("consignee"),
                        "notify_party": booking_data.get("notify_parties"),
                        "move_type": mapped_move_type,
                        "bookingnumber": booking_data.get("inttra_reference"),
                        "origin_place_of_carrier_receipt": booking_data.get("place_of_carrier_receipt"),
                        "destination_place_of_carrier_delivery": booking_data.get("place_of_carrier_delivery"),
                        "port_of_load": port_of_load_code,
                        "port_of_discharge": port_of_discharge_code,
                        "vessel": vessel,
                        "voyage": voyage,
                        "main_status": "Draft",
                        "message_status": "Original"
                    })

                    si_doc.insert(ignore_permissions=True)
                    frappe.db.commit()

                    si_message = f"New Draft Shipping Instruction ({si_doc.name}) created for Carrier Booking {carrier_booking_number}."


        return {
            "status_code": 201,
            "message": f"Job created successfully.,\n{si_message}",
            "data": {
                "job_name": job_doc.name
            }
        }

    except frappe.ValidationError as e:
        return {
            "status_code": 400,
            "message": f"Validation Error: {str(e)}"
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Error in create_job")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }
 
