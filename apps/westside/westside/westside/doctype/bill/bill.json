{"actions": [], "allow_rename": 1, "autoname": "BILL-.##", "creation": "2025-04-10 14:43:41.704456", "doctype": "DocType", "engine": "InnoDB", "field_order": ["master_data_section", "qb_bill_id", "bill_date", "sub_total", "tax", "total_amount", "origin_port", "destination_port", "status", "qb_vendor_contact", "qb_vendor_email", "shipping_date", "due_date", "booking_id", "document_number", "sync_token", "job_id", "column_break_yxpn", "vendor", "bol_number", "hs_code", "quickbooks_vendor", "memo", "qb_bill_to", "balance_amount", "paid_amount", "vendor_bill_no", "is_active", "origin", "section_break_ofzt", "bill_attachments", "section_break_qsgw", "category_details", "item_details", "tab_2_tab", "section_break_lisc", "request_and_response", "section_break_ivtt", "payment_type", "payment_note"], "fields": [{"fieldname": "bill_date", "fieldtype": "Datetime", "label": "<PERSON>"}, {"fieldname": "tax", "fieldtype": "Data", "label": "Tax"}, {"default": "Pending", "fieldname": "status", "fieldtype": "Data", "label": "Status"}, {"fieldname": "vendor", "fieldtype": "Link", "label": "vendor", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "column_break_yxpn", "fieldtype": "Column Break"}, {"fieldname": "section_break_qsgw", "fieldtype": "Section Break"}, {"fieldname": "category_details", "fieldtype": "Table", "label": "Category Details", "options": "Bill Category Details"}, {"fieldname": "item_details", "fieldtype": "Table", "label": "<PERSON><PERSON>", "options": "<PERSON>"}, {"fieldname": "sub_total", "fieldtype": "Data", "label": "Sub Total"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Amount", "precision": "2"}, {"fieldname": "quickbooks_vendor", "fieldtype": "JSON", "label": "Quickbooks_vendor"}, {"fieldname": "origin_port", "fieldtype": "Link", "label": "origin Port", "options": "UNLOCODE Locations"}, {"fieldname": "destination_port", "fieldtype": "Link", "label": "Destination Port", "options": "UNLOCODE Locations"}, {"fieldname": "bol_number", "fieldtype": "Data", "label": "BOL Number"}, {"fieldname": "master_data_section", "fieldtype": "Section Break", "label": "Master Data"}, {"fieldname": "tab_2_tab", "fieldtype": "Tab Break", "label": "QuickBooks Data"}, {"fieldname": "qb_bill_id", "fieldtype": "Data", "in_list_view": 1, "label": "QB Bill id"}, {"fieldname": "memo", "fieldtype": "Small Text", "label": "Memo"}, {"fieldname": "section_break_ofzt", "fieldtype": "Section Break"}, {"fieldname": "bill_attachments", "fieldtype": "Table", "label": "<PERSON>", "options": "<PERSON>"}, {"fieldname": "qb_bill_to", "fieldtype": "Small Text", "label": "QB Bill <PERSON>"}, {"fieldname": "qb_vendor_email", "fieldtype": "Data", "label": "QB <PERSON><PERSON>or <PERSON>"}, {"fieldname": "qb_vendor_contact", "fieldtype": "Data", "label": "QB Vendor Contact"}, {"fieldname": "hs_code", "fieldtype": "Data", "label": "HS Code"}, {"fieldname": "shipping_date", "fieldtype": "Datetime", "label": "Shipping Date"}, {"fieldname": "due_date", "fieldtype": "Datetime", "label": "Due Date"}, {"fieldname": "booking_id", "fieldtype": "Data", "label": "Booking ID"}, {"fieldname": "document_number", "fieldtype": "Data", "label": "Document Number"}, {"fieldname": "balance_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance Amount", "precision": "2"}, {"fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "precision": "2"}, {"fieldname": "vendor_bill_no", "fieldtype": "Data", "label": "Vendor Bill No"}, {"fieldname": "sync_token", "fieldtype": "Int", "label": "Sync Token"}, {"fieldname": "section_break_ivtt", "fieldtype": "Section Break"}, {"fieldname": "job_id", "fieldtype": "Data", "label": "Job Id"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "label": "Is active"}, {"fieldname": "payment_type", "fieldtype": "Data", "label": "Payment Type"}, {"fieldname": "section_break_lisc", "fieldtype": "Section Break"}, {"fieldname": "request_and_response", "fieldtype": "Table", "label": "Request and Response", "options": "Bill Request Response"}, {"fieldname": "payment_note", "fieldtype": "Small Text", "label": "Payment Note"}, {"fieldname": "origin", "fieldtype": "Select", "label": "Origin", "options": "Manual\nOCR"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-10-24 01:29:08.166150", "modified_by": "Administrator", "module": "Westside", "name": "Bill", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}