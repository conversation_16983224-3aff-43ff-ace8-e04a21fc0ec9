# Copyright (c) 2025, faircode and contributors
# For license information, please see license.txt

from frappe.model.document import Document
import os
import mimetypes
import frappe
import requests
from frappe.utils.file_manager import save_file
from frappe.utils import get_site_path
import ast
import json
from westside.westside.doctype.document_number_generate.document_number_generate import get_document_number
from westside.westside.doctype.quickbooks_settings.quickbooks_settings import get_quickbooks_access_token
from datetime import datetime
from frappe.utils.pdf import get_pdf
import time
from frappe import _

class Bill(Document):
	pass

QB_SETTINGS = frappe.get_single("QuickBooks Settings")
QB_REALM_ID = QB_SETTINGS.realm_id
QB_API_URL = QB_SETTINGS.api_url
MINOR_VERSION = QB_SETTINGS.minor_version


@frappe.whitelist()
def get_vendor_details():
	try:
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			start_time = time.time()
			resp_token = get_quickbooks_access_token()
			duration = time.time() - start_time
			print(f"QuickBooks token fetch took {duration:.2f} seconds","time duration for token fetch")
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']
			

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Content-Type": "application/json",
			"Accept": "application/json"
		}

		query = "SELECT * FROM Vendor"

		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"

		response = requests.get(url, headers=headers)

		if response.status_code != 200:
			frappe.log_error(response.text, "QuickBooks Vendor Fetch Error")
			frappe.throw("Failed to retrieve vendors from QuickBooks")

		vendors = response.json()
		# frappe.msgprint("Vendors fetched successfully.")
		return vendors
	
	except Exception as e:
		frappe.log_error(frappe.get_traceback())

@frappe.whitelist(allow_guest=True)
def get_customer_details():
	try:

		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']
			

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Content-Type": "application/json",
			"Accept": "application/json"
		}

		query = "SELECT * FROM Customer"

		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"

		response = requests.get(url, headers=headers)

		if response.status_code != 200:
			frappe.log_error(response.text, "QuickBooks Vendor Fetch Error")
			frappe.local.response['status_code'] = "404"
			frappe.local.response['message'] = "Failed to retrieve customers from QuickBooks"
			return

		coustomers = response.json()
		# frappe.msgprint("Vendors fetched successfully.")
		return coustomers
	
	except Exception as e:
		frappe.log_error(frappe.get_traceback())

@frappe.whitelist(allow_guest=True)
def fetch_all_quickbooks_accounts(search_type=None):
	try:
		if not search_type:
			time.sleep(3)
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']
		
		accounts =  frappe.cache().get_value("cached_accounts")	
		if not accounts:
			# print("Fetching accounts from QuickBooks API...")
			headers = {
				"Authorization": f"Bearer {access_token}",
				"Content-Type": "application/json",
				"Accept": "application/json"
			}

			query = "SELECT * FROM Account"

			url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"

			response = requests.get(url, headers=headers)

			if response.status_code != 200:
				accounts = None
				frappe.log_error(response.text, "QuickBooks Account Fetch Error")
				frappe.local.response['status_code'] = "500"
				frappe.local.response['message'] = "Failed to retrieve accounts types from QuickBooks"
				return


			accounts = response.json()
			frappe.cache().set_value("cached_accounts", accounts, expires_in_sec=3600)
		filtered_accounts = []
		if accounts and search_type:
			filtered_accounts = [
				acc for acc in accounts.get("QueryResponse", {}).get("Account", [])
				if any(
					search_type.lower() in str(value).lower()
					for value in acc.values()
					if isinstance(value, str) or isinstance(value, int)
				)
			]
		
		if accounts and not filtered_accounts:
			filtered_accounts = accounts.get("QueryResponse", {}).get("Account", [])

		return filtered_accounts

	except Exception as e:
		frappe.log_error(frappe.get_traceback())

@frappe.whitelist(allow_guest=True)
def fetch_all_quickbooks_products(search_type=None):
	try:
		if not search_type:
			time.sleep(3)
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			print("Fetching products from QuickBooks API...")
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']

		products = frappe.cache().get_value("cached_products")
		if not products:
			headers = {
				"Authorization": f"Bearer {access_token}",
				"Content-Type": "application/json",
				"Accept": "application/json"
			}

			query = "SELECT * FROM Item"

			url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"

			response = requests.get(url, headers=headers)

			if response.status_code != 200:
				products = None
				frappe.log_error(response.text, "QuickBooks Products Fetch Error")
				frappe.local.response['status_code'] = "404"
				frappe.local.response['message'] = "Failed to retrieve products from QuickBooks"
				return


			products = response.json()
			frappe.cache().set_value("cached_products", products, expires_in_sec=3600)
		filtered_products = []
		if products and search_type:
			filtered_products = [
				prod for prod in products.get("QueryResponse", {}).get("Item", [])
				if any(
					search_type.lower() in str(value).lower()
					for value in prod.values()
					if isinstance(value, str) or isinstance(value, int)  
				)
			]
		if products and not filtered_products:
			filtered_products = products.get("QueryResponse", {}).get("Item", [])

		return filtered_products

	except Exception as e:
		frappe.log_error(frappe.get_traceback())




@frappe.whitelist()
def create_quickbooks_bill():
	try:
		
		dct_temp_bill = {}
		lst_lines = []
		dct_temp_bill["doctype"] = "Bill"
		dct_temp_bill["origin"] = "Manual"
		json_data = frappe.form_dict.get("data")
		if not json_data:
			frappe.throw("Missing JSON payload in 'data' field.")

		try:
			dct_payload_data = json.loads(json_data)
		except Exception as e:
			frappe.response['error'] = "Invalid JSON payload in 'data' field."
		
		if not dct_payload_data:
			frappe.throw("No data received to create the bill.")

		dct_temp_bill["bill_date"] = dct_payload_data.get("bill_date", None)
		if not dct_temp_bill["bill_date"]:
			dct_temp_bill["bill_date"] = frappe.utils.nowdate()
		str_bill_number = get_document_number("Quickbook Bill","BL")
		dct_temp_bill['document_number'] = str_bill_number if str_bill_number else None
		dct_temp_bill["sub_total"] = dct_payload_data.get("sub_total", None)
		dct_temp_bill["job_id"] = dct_payload_data.get("job_id", None)
		dct_temp_bill["tax"] = dct_payload_data.get("tax", None)
		dct_temp_bill["paid_amount"] = 0
		dct_temp_bill["origin_port"] = dct_payload_data.get("origin_port", None)
		dct_temp_bill["destination_port"] = dct_payload_data.get("destination_port", None)
		dct_temp_bill["vendor"] = dct_payload_data.get("vendor", None)
		dct_temp_bill["vendor_bill_no"] = dct_payload_data.get("vendor_bill_no", None)
		dct_temp_bill["bol_number"] = dct_payload_data.get("bol_number", None)
		dct_temp_bill["shipping_date"] = dct_payload_data.get("shipping_date", None)
		dct_temp_bill["due_date"] = dct_payload_data.get("due_date", None)
		dct_temp_bill["booking_id"] = dct_payload_data.get("booking_id", None)
		dct_temp_bill["hs_code"] = dct_payload_data.get("hs_code", None)
		dct_temp_bill["memo"] = dct_payload_data.get("memo", None)
		dct_temp_bill["qb_bill_to"] = dct_payload_data.get("vendor_billTo", "N/A")
		dct_temp_bill["qb_vendor_email"] = dct_payload_data.get("vendor_email", "N/A")
		dct_temp_bill["qb_vendor_contact"] = dct_payload_data.get("vendor_contact", "N/A")
		dct_temp_bill['status'] = "Pending"
		dct_vendor = dct_payload_data.get("quickbooks_vendor", {})
		dct_temp_bill["quickbooks_vendor"] = dct_vendor
		
		

		lst_categories = []
		for data in dct_payload_data.get("category_items", []):
			lst_categories.append({
				"doctype": "Bill Category Details",
				"category": data.get("category_name"),
				"description": data.get("category_description"),
				"amount": data.get("amount"),
				"billable": data.get("billable"),
				"taxable": data.get("taxable"),
				"quickbooks_customer_id": data.get("quickbooks_customer_id"),
				"quickbooks_category_id": data.get("quickbooks_category_id")
			})
			if data.get("quickbooks_category_id"):
				dct_category = {
					"Amount": data.get("amount"),
					"DetailType": "AccountBasedExpenseLineDetail",
					"AccountBasedExpenseLineDetail": {
						"AccountRef": {
							"value": data.get("quickbooks_category_id")
						}
					},
					"Description": data.get("category_description")
				}
				if data.get("billable"):
					dct_category["AccountBasedExpenseLineDetail"]["BillableStatus"] = "Billable"
				if data.get("taxable"):
					dct_category["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {
						"value": "TAX" 
					}
				if data.get("quickbooks_customer_id"):
					dct_category["AccountBasedExpenseLineDetail"]["CustomerRef"] = {
						"value": data.get("quickbooks_customer_id")
					}
				lst_lines.append(dct_category)
		dct_temp_bill["category_details"] = lst_categories

		lst_items = []
		for data in dct_payload_data.get("items_details", []):
			lst_items.append({
				"doctype": "Bill Item Details",
				"product_service": data.get("item_name"),
				"description": data.get("item_description"),
				"quantity": data.get("qty"),
				"uom": data.get("uom"),
				"rate": data.get("rate"),
				"amount": data.get("amount"),
				"billable": data.get("billable"),
				"taxable": data.get("taxable"),
				"quickbooks_item_id": data.get("quickbooks_item_id"),
				"quickbooks_customer_id": data.get("quickbooks_customer_id")
			})
			if data.get("quickbooks_item_id"):
				dct_item = {
					"Amount": data.get("amount"),
					"DetailType": "ItemBasedExpenseLineDetail",
					"ItemBasedExpenseLineDetail": {
						"ItemRef": {
							"value": data.get("quickbooks_item_id"),
							"name": data.get("item_name")
						},
						"Qty": data.get("qty"),
						"UnitPrice": data.get("rate")
					},
					"Description": data.get("item_description")
				}
				if data.get("billable"):
					dct_item["ItemBasedExpenseLineDetail"]["BillableStatus"] = "Billable"
				if data.get("taxable"):
					dct_item["ItemBasedExpenseLineDetail"]["TaxCodeRef"] = {
						"value": "TAX" 
					}
				if data.get("quickbooks_customer_id"):
					dct_item["ItemBasedExpenseLineDetail"]["CustomerRef"] = {
						"value": data.get("quickbooks_customer_id")
					}
				lst_lines.append(dct_item)

		dct_temp_bill["item_details"] = lst_items

		

		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Accept": "application/json",
			"Content-Type": "application/json"
		}

		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill?minorversion={MINOR_VERSION}"

		# ===== Quickbook Bill Payload =====#
		payload = {
			"VendorRef": {
				"value": dct_vendor.get("Id")
			},
			"DueDate": dct_payload_data.get("due_date", None),
			"DocNumber": str_bill_number,
			"Line": lst_lines,
			"TxnDate": frappe.utils.nowdate(),
			"PrivateNote": dct_payload_data.get("memo", "Created via Frappe"),
		}


		response = requests.post(url, headers=headers, data=json.dumps(payload))
		if response.status_code != 200:
			frappe.db.rollback()
			frappe.response['message'] = response.json().get('Fault').get('Error', [{}])[0]
			frappe.response['status_code'] = "400"

		response_data = response.json()
		if response_data.get("Bill", {}):
			lst_req_resp = []
			lst_req_resp.append({
				"doctype": "Bill Request Response",
				"event": "New Bill Created",
				"payload": payload or {},
				"response": response_data.get("Bill") if response_data else None
			})
			dct_temp_bill["request_and_response"] = lst_req_resp
			
			dct_temp_bill["total_amount"] = float(response_data.get("Bill", {}).get("TotalAmt", None))
			dct_temp_bill["balance_amount"] = float(response_data.get("Bill", {}).get("Balance", None))
			dct_temp_bill["qb_bill_id"] = response_data.get("Bill", {}).get("Id", None)
			dct_temp_bill["sync_token"] = response_data.get("Bill", {}).get("SyncToken", None)

			frappe.get_doc(dct_temp_bill).insert(ignore_permissions=True)
			frappe.db.commit()

			bill_id = frappe.db.exists("Bill", {"qb_bill_id": dct_temp_bill.get("qb_bill_id")},"name")

			existing_bill = frappe.get_doc("Bill", bill_id)
			# existing_bill.set("bill_attachments", [])
			attachments = frappe.request.files.getlist('attachments')  
			lst_attachments = []
			
			for file_obj in attachments:
				file_content = file_obj.stream.read()
				file_name = file_obj.filename

				file_doc = frappe.get_doc({
					"doctype": "File",
					"file_name": file_name,
					"is_private": 0,
					"attached_to_doctype": "Bill",
					"attached_to_name": bill_id,
					"content": file_content,
				}).insert(ignore_permissions=True)

				if file_doc:
					existing_bill.append("bill_attachments", {
						"file_url": file_doc.file_url,
						"file": os.path.basename(file_doc.file_url)
					})
					lst_attachments.append({
						"file": file_doc.file_url,
						"file_name": os.path.basename(file_doc.file_url),
						"content_type": mimetypes.guess_type(file_doc.file_url)[0] or "application/octet-stream"
					})

			existing_bill.save(ignore_permissions=True)
			frappe.db.commit()
			dct_upload_status = {}
			if lst_attachments:
				upload_response = quickbooks_attach_files(dct_temp_bill.get("qb_bill_id"), lst_attachments, bill_id)
				
				if not upload_response:
					dct_upload_status = {
						"status_code": upload_response.get("status_code") or "400",
						"message": upload_response.get("message") or "Failed to upload attachments to QuickBooks.",
						"data": upload_response.get("data") or {}
					}
				else:
					dct_upload_status = {
						"status_code": upload_response.get("status_code"),
						"message": upload_response.get("message"),
						"data": upload_response.get("data")
					}

			frappe.response["message"] = "QuickBooks bill created successfully."
			frappe.response["status_code"] = "200"
			frappe.response["file_upload_status"] = dct_upload_status
			frappe.response["data"] = response_data


	except Exception:
		frappe.db.rollback()
		frappe.response['status_code'] = "500"
		frappe.response['message'] = "Something went wrong while creating the bill."
		frappe.log_error(frappe.get_traceback(), "QuickBooks Bill API Error")
		frappe.throw("Something went wrong while creating the bill.")




@frappe.whitelist()
def update_quickbooks_bill():
	try:
		json_data = frappe.form_dict.get("data")
		if not json_data:
			frappe.response['error'] = ("Missing JSON payload in 'data' field.")
		try:
			dct_payload_data = json.loads(json_data)
		except Exception as e:
			frappe.response['error'] = "Invalid JSON payload in 'data' field."

		dct_temp_bill = {}
		lst_lines = []
		
		if not dct_payload_data.get("bill_id"):
			frappe.response['error'] = "No data received to create the bill."

		doc_bill = frappe.get_doc("Bill", {"qb_bill_id":dct_payload_data.get("bill_id")} )
		if not doc_bill:
			frappe.response['status_code'] = "400"
			frappe.response['message'] = f"No Bill found with ID: {dct_payload_data.get('bill_id')}"
			return
		
		dct_temp_bill["bill_date"] = dct_payload_data.get("bill_date", None)
		if not dct_temp_bill["bill_date"]:
			dct_temp_bill["bill_date"] = frappe.utils.nowdate()
		if not doc_bill.document_number:
			dct_temp_bill["document_number"] = get_document_number("Quickbook Bill","BL")
		else:
			dct_temp_bill["document_number"] = doc_bill.document_number
		dct_temp_bill["sub_total"] = dct_payload_data.get("sub_total", None)
		dct_temp_bill["tax"] = dct_payload_data.get("tax", None)
		dct_temp_bill["paid_amount"] = 0
		dct_temp_bill["origin_port"] = dct_payload_data.get("origin_port", None)
		dct_temp_bill["destination_port"] = dct_payload_data.get("destination_port", None)
		dct_temp_bill["vendor"] = dct_payload_data.get("vendor", None)
		dct_temp_bill["vendor_bill_no"] = dct_payload_data.get("vendor_bill_no", None)
		dct_temp_bill["bol_number"] = dct_payload_data.get("bol_number", None)
		dct_temp_bill["shipping_date"] = dct_payload_data.get("shipping_date", None)
		dct_temp_bill["due_date"] = dct_payload_data.get("due_date", None)
		dct_temp_bill["booking_id"] = dct_payload_data.get("booking_id", None)
		dct_temp_bill["hs_code"] = dct_payload_data.get("hs_code", None)
		dct_temp_bill["memo"] = dct_payload_data.get("memo", None)
		dct_temp_bill["qb_bill_to"] = dct_payload_data.get("vendor_billTo", "N/A")
		dct_temp_bill["qb_vendor_email"] = dct_payload_data.get("vendor_email", "N/A")
		dct_temp_bill["qb_vendor_contact"] = dct_payload_data.get("vendor_contact", "N/A")
		dct_vendor = dct_payload_data.get("quickbooks_vendor", {})
		dct_temp_bill["quickbooks_vendor"] = dct_vendor
		

		lst_categories = []
		if dct_payload_data.get("category_items", []):
			doc_bill.set("category_details", [])
			for data in dct_payload_data.get("category_items", []):
				lst_categories.append({
					"doctype": "Bill Category Details",
					"category": data.get("category_name"),
					"description": data.get("category_description"),
					"amount": data.get("amount"),
					"billable": data.get("billable"),
					"taxable": data.get("taxable"),
					"quickbooks_customer_id": data.get("quickbooks_customer_id"),
					"quickbooks_category_id": data.get("quickbooks_category_id")
				})
				if data.get("quickbooks_category_id"):
					dct_category = {
						"Amount": data.get("amount"),
						"DetailType": "AccountBasedExpenseLineDetail",
						"AccountBasedExpenseLineDetail": {
							"AccountRef": {
								"value": data.get("quickbooks_category_id")
							}
						},
						"Description": data.get("category_description")
					}
					if data.get("billable"):
						dct_category["AccountBasedExpenseLineDetail"]["BillableStatus"] = "Billable"
					if data.get("taxable"):
						dct_category["AccountBasedExpenseLineDetail"]["TaxCodeRef"] = {
							"value": "TAX" 
						}
					if data.get("quickbooks_customer_id"):
						dct_category["AccountBasedExpenseLineDetail"]["CustomerRef"] = {
							"value": data.get("quickbooks_customer_id")
						}
					lst_lines.append(dct_category)
			dct_temp_bill["category_details"] = lst_categories

		lst_items = []
		
		if dct_payload_data.get("items_details", []):
			doc_bill.set("item_details", [])
			for data in dct_payload_data.get("items_details", []):
				lst_items.append({
					"doctype": "Bill Item Details",
					"product_service": data.get("item_name"),
					"description": data.get("item_description"),
					"quantity": data.get("qty"),
					"uom": data.get("uom"),
					"rate": data.get("rate"),
					"amount": data.get("amount"),
					"billable": data.get("billable"),
					"taxable": data.get("taxable"),
					"quickbooks_item_id": data.get("quickbooks_item_id"),
					"quickbooks_customer_id": data.get("quickbooks_customer_id")
				})
				if data.get("quickbooks_item_id"):
					dct_item = {
						"Amount": data.get("amount"),
						"DetailType": "ItemBasedExpenseLineDetail",
						"ItemBasedExpenseLineDetail": {
							"ItemRef": {
								"value": data.get("quickbooks_item_id"),
								"name": data.get("item_name")
							},
							"Qty": data.get("qty"),
							"UnitPrice": data.get("rate")
						},
						"Description": data.get("item_description")
					}
					if data.get("billable"):
						dct_item["ItemBasedExpenseLineDetail"]["BillableStatus"] = "Billable"
					if data.get("taxable"):
						dct_item["ItemBasedExpenseLineDetail"]["TaxCodeRef"] = {
							"value": "TAX" 
						}
					if data.get("quickbooks_customer_id"):
						dct_item["ItemBasedExpenseLineDetail"]["CustomerRef"] = {
							"value": data.get("quickbooks_customer_id")
						}
					lst_lines.append(dct_item)

			dct_temp_bill["item_details"] = lst_items

		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']
		
		bill_id = dct_payload_data.get("bill_id")
		headers = {
			"Authorization": f"Bearer {access_token}",
			"Accept": "application/json",
			"Content-Type": "application/json"
		}
		sync_token = None
		if not doc_bill.qb_bill_id and doc_bill.status == "QB Failed":
			url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill?minorversion={MINOR_VERSION}"

			# ===== Quickbook Bill Payload =====#
			payload = {
				"VendorRef": {
					"value": dct_vendor.get("Id")
				},
				"DueDate": dct_payload_data.get("due_date", None),
				"DocNumber": dct_temp_bill.get("document_number"),
				"Line": lst_lines,
				"TxnDate": frappe.utils.nowdate(),
				"PrivateNote": dct_payload_data.get("memo", "Created via Frappe"),
			}
			response = requests.post(url, headers=headers, data=json.dumps(payload))
		else:
			get_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill/{bill_id}"

			get_response = requests.get(get_url, headers=headers)
			bill_data = get_response.json().get("Bill")

			if bill_data and bill_data.get("SyncToken"):
				sync_token = bill_data.get("SyncToken")
			else:
				return {
					"status": "error",
					"message": "SyncToken not found for the bill.",
					"status_code": 400
				}

			url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill"
			
			# ===== Quickbook Update Bill Payload =====#
			payload = {
				"Id": bill_id,
				"SyncToken": sync_token,
				"domain": "QBO",
				"DocNumber": doc_bill.document_number if doc_bill.document_number else dct_temp_bill["document_number"],
				"sparse": True,
				"VendorRef": {
					"value": dct_vendor.get("Id")
				},
				"DueDate": dct_payload_data.get("due_date", None),
				"Line": lst_lines,
				"PrivateNote": dct_payload_data.get("memo", "Created via Frappe"),
			}

			response = requests.post(url, headers=headers, data=json.dumps(payload))

		if response.status_code != 200:
			frappe.db.rollback()
			frappe.response['message'] = response.json().get('Fault').get('Error', [{}])[0]
			frappe.response['status_code'] = "400"

		response_data = response.json()
		
		if response_data.get("Bill", {}):
			dct_request_and_response = {
				"doctype": "Bill Request Response",
				"event": "Bill Updated",
				"payload": payload or {},
				"response": response_data.get("Bill") if response_data else None
			}
			doc_bill.append("request_and_response", dct_request_and_response)
			dct_temp_bill["total_amount"] = float(response_data.get("Bill", {}).get("TotalAmt", None))
			dct_temp_bill["balance_amount"] = float(response_data.get("Bill", {}).get("Balance", None))
			dct_temp_bill["qb_bill_id"] = response_data.get("Bill", {}).get("Id", None)
			dct_temp_bill["sync_token"] = response_data.get("Bill", {}).get("SyncToken", None)
			int_balance_amount = int(response_data.get("Bill", {}).get("Balance", 0.0)) if response_data.get("Bill", {}).get("Balance", 0.0) else 0
			int_total_amount = int(response_data.get("Bill", {}).get("TotalAmt", 0.0)) if response_data.get("Bill", {}).get("TotalAmt", 0.0) else 0
			dct_temp_bill["balance_amount"] = int_balance_amount
			dct_temp_bill["paid_amount"] = int_total_amount - int_balance_amount if int_balance_amount and int_total_amount else 0

			str_status = ''
			balance = float(response_data.get("Bill", {}).get("Balance", None))
			due_date_str = response_data.get("Bill", {}).get("DueDate", None) 
			due_date = datetime.strptime(due_date_str, "%Y-%m-%d") if due_date_str else None
			today = datetime.today()

			if balance == 0:
				str_status =  "Paid"
			elif due_date and due_date < today:
				str_status = "Overdue"
			else:
				str_status = "Pending"
					
			dct_temp_bill['status'] = str_status

			doc_bill.update(dct_temp_bill).save(ignore_permissions=True)
			frappe.db.commit()

			attachments = frappe.request.files.getlist('attachments')  
			lst_attachments = []
			
			for file_obj in attachments:
				file_content = file_obj.stream.read()
				file_name = file_obj.filename

				file_doc = frappe.get_doc({
					"doctype": "File",
					"file_name": file_name,
					"is_private": 0,
					"attached_to_doctype": "Bill",
					"attached_to_name": bill_id,
					"content": file_content,
				}).insert(ignore_permissions=True)

				if file_doc:
					doc_bill.append("bill_attachments", {
						"file_url": file_doc.file_url,
						"file": os.path.basename(file_doc.file_url)
					})
					lst_attachments.append({
						"file": file_doc.file_url,
						"file_name": os.path.basename(file_doc.file_url),
						"content_type": mimetypes.guess_type(file_doc.file_url)[0] or "application/octet-stream"
					})

				try:
					if file_name:
						frappe.db.set_value("Google Drive Invoice File", {"file_name": file_name}, "status", "Processed")
				except:
					frappe.log_error(frappe.get_traceback(), "Error in updating Google Drive Invoice File status-{file_name}, bill_id-{bill_id}")


			doc_bill.save(ignore_permissions=True)
			frappe.db.commit()
			dct_upload_status = {}
			if lst_attachments:
				upload_response = quickbooks_attach_files(dct_temp_bill.get("qb_bill_id"), lst_attachments, bill_id)
				
				if not upload_response:
					dct_upload_status = {
						"status_code": upload_response.get("status_code") or "400",
						"message": upload_response.get("message") or "Failed to upload attachments to QuickBooks.",
						"data": upload_response.get("data") or {}
					}
				else:
					dct_upload_status = {
						"status_code": upload_response.get("status_code"),
						"message": upload_response.get("message"),
						"data": upload_response.get("data")
					}
			if dct_payload_data.get("lst_deleted_files"):
				try:
					qb_delete_result = delete_quickbooks_attachments(doc_bill.name, dct_payload_data.get("bill_id"),dct_payload_data.get("lst_deleted_files"))
				except Exception as e:
					frappe.log_error(frappe.get_traceback(), "QuickBooks Attachment Deletion Error")

			frappe.response["message"] = "QuickBooks bill updated successfully.!!"
			frappe.response["status_code"] = "200"
			frappe.response["file_upload_status"] = dct_upload_status
			frappe.response["data"] = response_data


	except Exception:
		frappe.db.rollback()
		frappe.response['status_code'] = "500"
		frappe.response['message'] = "Something went wrong while creating the bill."
		frappe.log_error(frappe.get_traceback(), "QuickBooks Bill API Error")
		frappe.throw("Something went wrong while creating the bill.")



@frappe.whitelist()
def make_bill_payment(bill_id, payment_type = "Cash"):
	try:
		payload_data = json.loads(frappe.form_dict.get("payload"))
		str_privet_note = payload_data.get("privet_note")
		doc_bill = frappe.get_doc("Bill", {"qb_bill_id":bill_id})
		if not doc_bill.qb_bill_id:
			return{
				"status_code": "400",
				"message": "Bill is not created in QuickBooks."
			}
		
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']	

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Content-Type": "application/json",
			"Accept": "application/json"
		}

		vendor_id = json.loads(doc_bill.quickbooks_vendor).get("Id")
		
		if payment_type in ["Check", "Cash"]:
			query = "SELECT * FROM Account WHERE AccountType = 'Bank'"
		elif payment_type == "CreditCard":
			query = "SELECT * FROM Account WHERE AccountType = 'Credit Card'"
		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"

		response = requests.get(url, headers=headers)
		dct_response = response.json()
		account_id = dct_response.get("QueryResponse", {}).get("Account", [{}])[0].get("Id") if dct_response.get("QueryResponse", {}).get("Account", [{}]) else account_id

		if not account_id:
			return{
				"status_code": "400",
				"message": "No account found for the given payment type."
			}
		if not vendor_id:
			return{
				"status_code": "400",
				"message": "No vendor found for the given bill."
			}
		
		payment_payload = {
			"VendorRef": {"value": vendor_id},
			"TotalAmt": int(doc_bill.total_amount),
			
		}

		if payment_type == "CreditCard":
			payment_payload["PayType"] = "CreditCard"
			payment_payload["CreditCardPayment"] = {
				"CCAccountRef": {"value": account_id}
			}
			payment_payload["Line"] = [
				{
					"Amount": int(doc_bill.total_amount),
					"LinkedTxn": [
						{
							"TxnId": str(doc_bill.qb_bill_id),
							"TxnType": "Bill"
						}
					]
				}
			]
		elif payment_type == "Check":
			payment_payload["CheckPayment"] = {
				"BankAccountRef": {"value": account_id}
			}
			payment_payload["Line"] = [
				{
					"Amount": int(doc_bill.total_amount),
					"LinkedTxn": [
						{
							"TxnId": str(doc_bill.qb_bill_id),
							"TxnType": "Bill"
						}
					]
				}
			]
		elif payment_type == "Cash":
			payment_payload["CheckPayment"] = {
				"BankAccountRef": {"value": account_id}
			}
			payment_payload["Line"] = [
				{
					"Amount": int(doc_bill.total_amount),
					"LinkedTxn": [
						{
							"TxnId": str(doc_bill.qb_bill_id),
							"TxnType": "Bill"
						}
					]
				}
			]


		payment_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/billpayment"
		payment_response = requests.post(payment_url, headers=headers, data=json.dumps(payment_payload))

		if not payment_response.ok:
			return{
				"status_code": "400",
				"message": "Failed to mark bill as paid in QuickBooks.",
				"data": payment_response.json().get('Fault', {}).get('Error', [{}])[0]
			}
		else:
			doc_bill.append("request_and_response", {
				"event": "Bill Payment",
				"payload": payment_payload if payment_payload else "",
				"response": payment_response.json() if payment_response else ""
			})
			doc_bill.payment_type = payment_type
			doc_bill.payment_note = str_privet_note
			doc_bill.status = "Paid"
			doc_bill.paid_amount = float(doc_bill.total_amount)
			doc_bill.balance_amount = 0
			doc_bill.save(ignore_permissions=True)
			frappe.db.commit()
			return{
				"status_code": "200",
				"message": "Bill marked as paid in QuickBooks successfully.",
				"data": payment_response.json()
			}

	except Exception:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Bill Payment API Error")
		frappe.throw("Something went wrong while marking the bill as paid.")



@frappe.whitelist(allow_guest=True)
def upload_local_file_to_frappe(filepath, attached_to_doctype=None, attached_to_name=None,):
	try:
		if not os.path.exists(filepath):
			frappe.throw(f"File does not exist: {filepath}")

		filename = os.path.basename(filepath)

		content_type, _ = mimetypes.guess_type(filepath)
		if not content_type:
			content_type = "application/octet-stream"  

		with open(filepath, "rb") as filedata:
			file_content = filedata.read()

		file_doc = save_file(
			fname=filename,
			content=file_content,
			dt=attached_to_doctype,
			dn=attached_to_name,
			folder=None,
			is_private=0,  
		)

		frappe.db.commit()
		return file_doc.file_url

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Upload File Failed")
		raise


@frappe.whitelist()
def quickbooks_attach_files(qb_bill_id, lst_attachments, bill_id):
	"""
	Attach files to the QuickBooks bill using the QuickBooks API.
	"""

	try:
		if not qb_bill_id:
			frappe.throw("Bill ID is missing. Cannot attach files.")
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']
		bill_doc = frappe.get_doc("Bill", {"qb_bill_id": qb_bill_id})
		if not lst_attachments:
			frappe.throw("No attachments provided to upload.")
		for file_url in lst_attachments:
			file_doc = frappe.get_doc("File", {"file_url": file_url['file']})
			file_path = file_doc.get_full_path()

			with open(file_path, "rb") as f:
				mime_type = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

				files = {
					"file_content_0": (file_doc.file_name, f, mime_type),

					"file_metadata_0": (
						"metadata.json",  
						json.dumps({
							"AttachableRef": [{
								"EntityRef": {
									"type": "Bill",
									"value": qb_bill_id
								}
							}],
							"Category": "Document",
							"Tag": "Bill Attachment"
						}),
						"application/json"
					)
				}

				attach_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/upload?minorversion={MINOR_VERSION}"
				headers = {
					"Authorization": f"Bearer {access_token}",
					"Accept": "application/json"
				}
				response = requests.post(
					attach_url,
					headers=headers,
					files=files
				)

				if response.status_code != 200:
					frappe.db.rollback()
					return{
						"status_code": "400",
						"message": "Failed to attach files to QuickBooks bill.",
						"data": response.json().get('Fault', {}).get('Error', [{}])[0]
					}
				for row in bill_doc.bill_attachments:
					if row.file_url == file_url['file']:  
						row.attachment_response = response.text
						break

				bill_doc.append("request_and_response", {
					"event": "File Attach",
					"payload": {},
					"response": response.json() if response else ""
				})

		bill_doc.save(ignore_permissions=True)
		frappe.db.commit()
		return{
			"status_code": "200",
			"message": "Files attached successfully to QuickBooks bill.",
			"data": response.json(),
			"file":files
		}
	
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Attach Files Error")
		frappe.throw("Something went wrong while attaching files to QuickBooks bill.")
		return False


@frappe.whitelist()
def list_bills(page=1, page_length=20):
	"""
	List all bills from Our Database with pagination.
	1. Admin can see all bills.
	2. Role based vendor filtering.
	3. Overdue auto-update.
	4. bills query with pagination.
	"""
	try:
		user = frappe.session.user
		roles = frappe.get_roles(user)

		vendor = frappe.form_dict.get("vendor")
		bill_date = frappe.form_dict.get("bill_date")
		status = frappe.form_dict.get("status")
		search = frappe.form_dict.get("search")

		page = int(frappe.form_dict.get("page") or 1)
		page_length = int(frappe.form_dict.get("page_length") or 20)
		start = (page - 1) * page_length

		filters = {}
		# 1
		if user in ["Administrator", "Admin"] or "System Manager" in roles:
			if vendor:
				filters["vendor"] = vendor
		else:
			# 2
			if "Vendor" in roles:
				vendor_name = frappe.db.get_value("Vendor", {"user_id": user}, "name")
				if vendor_name:
					filters["vendor"] = vendor_name
				if vendor and vendor != vendor_name:
					frappe.response["http_status_code"] = "403"
					frappe.response["message"] = "Unauthorized to access this resource."
					return

		filters["is_active"] = 1
		if bill_date:
			filters["bill_date"] = bill_date
		if status:
			filters["status"] = status
		if search:
			or_filters = [
				["document_number", "like", f"%{search}%"],
				["booking_id", "like", f"%{search}%"],
				["qb_bill_id", "like", f"%{search}%"],
				["job_id", "like", f"%{search}%"],
				["vendor_bill_no", "like", f"%{search}%"],
				["bol_number", "like", f"%{search}%"],
			]
		else:
			or_filters = []
			
			

		# 3
		try:
			overdue_bills = frappe.get_all(
				"Bill",
				filters={
					"due_date": ["<", datetime.now().date()],
					"status": ["not in", ["Overdue", "Paid","QB Failed"]]
				},
				fields=["name"]
			)

			for bill in overdue_bills:
				frappe.db.set_value("Bill", bill["name"], "status", "Overdue")

			if overdue_bills:
				frappe.db.commit()
		except Exception:
			frappe.log_error(frappe.get_traceback(), "Bills overdue status updation Error")

		# 4
		bills = frappe.get_all(
			"Bill",
			filters=filters,
			or_filters=or_filters,
			fields=[
				"name", "bill_date", "document_number", "vendor",
				"total_amount", "quickbooks_vendor", "qb_bill_id",
				"status", "job_id","vendor_bill_no","origin","creation","modified","booking_id"
			],
			order_by="creation desc",
			limit_start=start,
			limit_page_length=page_length
		)

		
		total_count = frappe.db.count("Bill", filters=filters)

		if not bills:
			frappe.response["message"] = "No bills found."
			frappe.response["status_code"] = "404"
			frappe.response["data"] = []
			frappe.response["pagination"] = {
				"page": page,
				"page_length": page_length,
				"total_count": total_count,
				"total_pages": (total_count + page_length - 1) // page_length
			}
			return

		lst_data = []
		for bill in bills:
			dict_bill = {
				"name": bill.name,
				"bill_date": bill.bill_date,
				"total_amount": "{:.2f}".format(float(bill.total_amount)) if bill.total_amount else 0,
				"status": bill.status or "Pending",
				"qb_bill_id": bill.qb_bill_id,
				"document_number": bill.document_number,
				"job_id": bill.job_id,
				"vendor_bill_no": bill.vendor_bill_no,
				"origin": bill.origin,
				"creation": bill.creation.strftime("%m-%d-%Y"),
				"modified": bill.modified.strftime("%m-%d-%Y"),
				"booking_id": bill.booking_id
			}

			if bill.vendor:
				first_name = frappe.get_value("Vendor", bill.vendor, "vendor_name") or ""
				dict_bill["vendor"] = f"{first_name}".strip() or "N/A"
			else:
				dict_bill["vendor"] = "N/A"

			if bill.quickbooks_vendor:
				try:
					dict_bill["quickbooks_vendor"] = json.loads(bill.quickbooks_vendor).get("DisplayName", "N/A")
				except Exception:
					dict_bill["quickbooks_vendor"] = "Invalid JSON"
			else:
				dict_bill["quickbooks_vendor"] = "N/A"

			lst_data.append(dict_bill)

		frappe.response["status_code"] = "200"
		frappe.response["data"] = lst_data
		frappe.response["pagination"] = {
			"page": page,
			"page_length": page_length,
			"total_count": total_count,
			"total_pages": (total_count + page_length - 1) // page_length
		}

	except Exception:
		frappe.log_error(frappe.get_traceback(), "QuickBooks List Bills Error")
		frappe.throw("Something went wrong while listing bills from QuickBooks.")



@frappe.whitelist()
def get_bill_details(bill_id=None,print_bill = None):
	"""
	Get details of a specific bill from Our Database.
	"""
	try:
		if not bill_id:
			frappe.response['status_code'] = "400"
			frappe.response['message'] = "Bill ID is required to fetch details."
			return
		
		details_pdf = {}
		bill_name = frappe.db.get_value("Bill", {"name": bill_id}) or frappe.db.get_value("Bill", {"qb_bill_id": bill_id})

		if not bill_name:
			frappe.response['status_code'] = "404"
			frappe.response['message'] = f"No Bill found with ID or QB ID: {bill_id}"
			return

		bill = frappe.get_doc("Bill", bill_name)

		if not bill: 
			frappe.response['status_code'] = "404"
			frappe.response['message'] = f"No Bill found with ID: {bill_id}"
			return

		def clean_child_table(child_table):
			unwanted_fields = {
				"doctype", "idx", "modified_by", "modified", "owner",
				"parent", "parentfield", "parenttype", "docstatus", "attachment_response","full_category_data"
			}
			cleaned = []
			for d in child_table:
				row = {k: v for k, v in d.as_dict().items() if k not in unwanted_fields}
				if "amount" in row and row["amount"] is not None:
					try:
						row["amount"] = "{:.2f}".format(float(row["amount"])) if row["amount"] else 0
					except (ValueError, TypeError):
						pass  
				cleaned.append(row)
			return cleaned
			
		if bill.vendor:
			vendor_name = frappe.get_value("Vendor", bill.vendor, "vendor_name") or ""
			str_vendor_name = f"{vendor_name} ".strip() if (vendor_name) else "N/A"
		else:
			str_vendor_name = "N/A"

		dct_destination_port = frappe.get_value("UNLOCODE Locations", bill.destination_port, ["name", "locode", "location_name", "country","sub_division"], as_dict=True) if bill.destination_port else None
		str_destination_port_name = "N/A"
		if dct_destination_port:
			str_destination_port_name = f"{dct_destination_port.get('location_name', 'N/A')},{dct_destination_port.get('country', 'N/A')} ({dct_destination_port.get('locode', 'N/A')})"
		dct_origin_port = frappe.get_value("UNLOCODE Locations", bill.origin_port, ["name", "locode", "location_name", "country","sub_division"], as_dict=True) if bill.origin_port else None
		str_origin_port_name = "N/A"
		if dct_origin_port:
			str_origin_port_name = f"{dct_origin_port.get('location_name', 'N/A')},{dct_origin_port.get('country', 'N/A')} ({dct_origin_port.get('locode', 'N/A')})"

		details = {
			"name": bill.name,
			"document_number": bill.document_number,
			"bill_date": bill.bill_date,
			"qb_bill_id": bill.qb_bill_id,
			"vendor_id": bill.vendor,
			"vendor": str_vendor_name,
			"vendor_bill_no": bill.vendor_bill_no,
			"bol_number": bill.bol_number,
			"total_amount": "{:.2f}".format(float(bill.total_amount)) if bill.total_amount else 0,
			"shipping_date": bill.shipping_date,
			"due_date": bill.due_date,
			"booking_id": bill.booking_id,
			"hs_code": bill.hs_code,
			"destination_port": str_destination_port_name,
			"origin_port": str_origin_port_name,
			"dct_destination_port":dct_destination_port,
			"dct_origin_port":dct_origin_port,
			"status": bill.status or "Pending",
			"memo": bill.memo,
			"quickbooks_vendor_id": json.loads(bill.quickbooks_vendor).get("Id") if bill.quickbooks_vendor else "N/A",
			"quickbooks_vendor_display_name": json.loads(bill.quickbooks_vendor).get("DisplayName") if bill.quickbooks_vendor else "N/A",
			"qb_bill_to": bill.qb_bill_to or "N/A",
			"qb_vendor_email": bill.qb_vendor_email or "N/A",
			"qb_vendor_contact": bill.qb_vendor_contact or "N/A",
			"category_details": clean_child_table(bill.category_details) if bill.category_details else [],
			"item_details": clean_child_table(bill.item_details) if bill.item_details else [],
			"attachments": clean_child_table(bill.bill_attachments) if bill.bill_attachments else [],
			"payment_note": bill.payment_note,
			"payment_type": bill.payment_type
		}

		if print_bill:
			doc_shipper = frappe.get_doc("Shipper", {"shipper_code":"WESELL"},as_dict=True) 
			details_pdf = {
				"name": bill.name,
				"shipper_name": doc_shipper.get("shipper_name"),
				"shipper_address": doc_shipper.get("custom_address"),
				"shipper_phone": doc_shipper.get("phone"),
				"shipper_email": doc_shipper.get("email"),
				"document_number": bill.document_number,
				"bill_date": bill.bill_date.strftime("%b-%d-%Y") if bill.bill_date else "",
				"qb_bill_id": bill.qb_bill_id,
				"vendor": frappe.get_doc("Vendor", bill.vendor),
				"vendor_bill_no": bill.vendor_bill_no,
				"total_amount": "{:.2f}".format(float(bill.total_amount)) if bill.total_amount else 0,
				"balance_amount": "{:.2f}".format(float(bill.balance_amount)) if bill.balance_amount else 0,
				"paid_amount": "{:.2f}".format(float(bill.paid_amount)) if bill.paid_amount else 0,
				"shipping_date": bill.shipping_date,
				"due_date": bill.due_date.strftime("%b-%d-%Y") if bill.due_date else "",
				"status": bill.status or "Pending",
				"memo": bill.memo,
				"quickbooks_vendor_display_name": json.loads(bill.quickbooks_vendor).get("DisplayName") if bill.quickbooks_vendor else "N/A",
				"qb_bill_to": bill.qb_bill_to or "N/A",
				"qb_vendor_email": bill.qb_vendor_email or "N/A",
				"qb_vendor_contact": bill.qb_vendor_contact or "N/A",
				"category_details": clean_child_table(bill.category_details) if bill.category_details else [],
				"item_details": clean_child_table(bill.item_details) if bill.item_details else [],
				"payment_note": bill.payment_note,
				"payment_type": bill.payment_type,
				"BillAddr": json.loads(bill.quickbooks_vendor).get("BillAddr") if bill.quickbooks_vendor else "N/A"
			}
			print(details_pdf)
			html = frappe.render_template("westside/templates/docket_attachments/bill_new.html", {"doc": details_pdf})
			pdf_data = get_pdf(html)

			if pdf_data:
				file_doc = frappe.get_doc({
					"doctype": "File",
					"file_name": f"Bill_{bill.name}.pdf",
					"is_private": 0,
					"attached_to_doctype": "Bill",
					"attached_to_name": bill.name,
					"content": pdf_data,
				}).insert(ignore_permissions=True)
				frappe.db.commit()
				frappe.response["status_code"] = "200"
				frappe.response["file_url"] = file_doc.file_url
				frappe.response["file_name"] = file_doc.file_name
				frappe.response["file_path"] = file_doc.get_full_path()
				return
			else:
				frappe.response["status_code"] = "500"
				frappe.response["message"] = "Failed to generate PDF."
				return

		frappe.response["status_code"] = "200"
		frappe.response["data"] = details
		return

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Get Bill Details Error")
		frappe.response["status_code"] = "500"
		frappe.response["message"] = "Something went wrong while fetching bill details from QuickBooks."
		return



@frappe.whitelist()
def create_bill_from_job(job_id=None):
	try:
		if not job_id:
			frappe.response['status_code'] = '401'
			frappe.response['message'] = 'No Data found for this booking..!!'
			return
		doc_job = frappe.get_value(
			"Job",
			job_id,
			["name", "booking_id", "vendor_name", "hs_code", "doc_cut_of_date", "port_cut_of_date", "port_of_origin"],
			as_dict=True
		)

		dct_unit_data = {}
		if not doc_job:
			frappe.response['status_code'] = '401'
			frappe.response['message'] = 'No Data found for this booking..!!'
			return
		else:
			dct_unit_data['name'] = doc_job.name
			dct_unit_data['hs_code'] = doc_job.hs_code

			if doc_job.get("booking_id"):
				dct_unit_data['carrier_booking_num'] = frappe.get_value("Booking Request",doc_job.get("booking_id"), "carrier_booking_number")
				dct_unit_data['booking_id'] = doc_job.booking_id
				if dct_unit_data['carrier_booking_num']:
					dct_unit_data['bol_number'] = frappe.get_value("Bill of Lading",{"carrier_booking_number":dct_unit_data['carrier_booking_num']}, "bol_number")

			if doc_job.get("vendor_name"):
				doc_vendor = frappe.get_value("Vendor",doc_job.get("vendor_name"),["name","vendor_name"])
				dct_unit_data['vendor_id'] = doc_vendor[0]
				dct_unit_data['vendor_name']  = doc_vendor[1]
			if doc_job.get("port_of_origin"):
				dct_unit_data['port_of_origin'] = frappe.get_value("UNLOCODE Locations",doc_job.get("port_of_origin"), ["name","location_name","country","locode"], as_dict=True)
			

		frappe.response['status_code'] = '200'
		frappe.response['message'] = 'Data fetched successfully..!!'
		frappe.response['data'] = dct_unit_data
		return
		

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Get Bill Details Error")
		frappe.response['status_code'] = '500'
		frappe.response['message'] = ("Something went wrong while fetching bill details from QuickBooks.")
		return



@frappe.whitelist()
def get_quickbooks_bill(bill_id):
	try:
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']
		
		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill/{bill_id}"
		headers = {
			"Authorization": f"Bearer {access_token}",
			"Accept": "application/json",
			"Content-Type": "application/json"
		}

		response = requests.get(url, headers=headers)

		if response.status_code == 200:
			return response.json()["Bill"]
		else:
			raise Exception(f"Failed to fetch Bill: {response.status_code} - {response.text}")
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Get Bill Details Error")
		frappe.response['status_code'] = '500'
		frappe.response['message'] = ("Something went wrong while fetching bill details from QuickBooks.")
		return
	

@frappe.whitelist()
def update_quickbook_bill_webhook(lst_bill_id):
	try:
		if lst_bill_id:
			for bill_id in lst_bill_id:
				bill_data = get_quickbooks_bill(bill_id)
				if bill_data:
					temp_data_update = {}
					
					str_status = ''
					balance = bill_data.get("Balance", 0.0)
					due_date_str = bill_data.get("DueDate")
					due_date = datetime.strptime(due_date_str, "%Y-%m-%d") if due_date_str else None
					today = datetime.today()

					if not balance or balance == 0:
						str_status =  "Paid"
					elif due_date and due_date < today:
						str_status = "Overdue"
					else:
						str_status = "Pending"
					
					temp_data_update['status'] = str_status

					if bill_data.get("TotalAmt") or None:
						temp_data_update['total_amount'] = float(bill_data.get("TotalAmt"))
						temp_data_update['balance_amount'] = float(balance)
						temp_data_update['paid_amount'] = float(bill_data.get("TotalAmt")) - float(balance)

					if bill_data.get("docNumber") or None:
						temp_data_update['document_number'] = bill_data.get("docNumber")

					
					doc_update = frappe.get_doc("Bill", {"qb_bill_id": bill_id})
					if doc_update.response_json:
						try:
							existing_response = json.loads(doc_update.response_json)
							if not isinstance(existing_response, list):
								existing_response = [existing_response]
						except Exception:
							existing_response = []

					existing_response.append(bill_data)
					temp_data_update['response_json'] = json.dumps(existing_response, indent=2)

					doc_update.update(temp_data_update)
					doc_update.save(ignore_permissions=True) 
					frappe.db.commit()

		return True
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Get Bill Details Error")
		frappe.response['status_code'] = '500'
		frappe.response['message'] = ("Something went wrong while fetching bill details from QuickBooks.")
		return 
	


@frappe.whitelist()
def delete_quickbooks_attachments(bill_id, qb_bill_id, attachable_ids):
	"""
    Delete multiple attachments linked to a QuickBooks Bill.
    :param bill_id: QuickBooks Bill ID (optional for your context)
    :param attachable_ids: List of attachable IDs to delete
    """

	try:
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']
		
		headers = {
			"Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
		}

		get_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill/{qb_bill_id}"

		get_response = requests.get(get_url, headers=headers)
		bill_data = get_response.json().get("Bill")

		sync_token = bill_data.get("SyncToken")
		frappe.db.set_value("Bill", bill_id, "sync_token", sync_token)
		
		attachments = frappe.get_all("Bill Attachments",filters={"parent": bill_id, "file_url": ["in", attachable_ids]},fields=["name", "file_url", "file", "attachment_response"])

		if attachments:
			attachable_ids = []
			for attachment in attachments:
				if attachment.attachment_response:
					attachable_id = json.loads(attachment.attachment_response).get("AttachableResponse", [{}])[0].get("Attachable", {}).get("Id")
					if attachable_id:
						attachable_ids.append(attachable_id)
			if attachable_ids:
				failed_deletions = []
				for attachable_id in attachable_ids:
					delete_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/attachable?operation=delete&minorversion={MINOR_VERSION}"
					payload = 	{
						"SyncToken": sync_token, 
						"domain": "QBO", 
						"AttachableRef": [
							{
							"IncludeOnSend": False, 
							"EntityRef": {
								"type": "Bill", 
								"value": qb_bill_id
							}
							}
						], 
						"Note": "This is an attached note.", 
						"sparse": True,  
						"Id": attachable_id
						
					}
					response = requests.post(delete_url, headers=headers, json=payload)
					dct_response = response.json()
					
					doc_bill = frappe.get_doc("Bill", bill_id)
					doc_bill.append("request_and_response", {
						"event": "File Delete",
						"payload": payload or {},
						"response": response.json() if response else ""
					})

					doc_bill.save(ignore_permissions=True)
					frappe.db.commit()

					if dct_response.get("AttachableResponse", [{}])[0].get("Attachable", {}).get("status") == "Deleted":
						frappe.db.delete("Bill Attachments", {"parent": bill_id,"file_url": attachment.file_url})
					elif dct_response.get("Attachable", {}).get("status") == "Deleted":
						frappe.db.delete("Bill Attachments", {"parent": bill_id,"file_url": attachment.file_url})
					else:
						frappe.db.set_value("Bill Attachments", attachment.name, "attachment_response", json.dumps(dct_response))

				if failed_deletions:
					frappe.throw(_("Some attachments could not be deleted: {0}").format(failed_deletions))

			return {"status": "success", "deleted": attachable_ids}
		
		return {"status": "success", "message": "No attachments to delete."}
	
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Attachment Deletion Error")
		frappe.throw(_("Failed to delete attachments: {0}").format(str(e)))


@frappe.whitelist()
def delete_quickbooks_bill(qb_bill_id):
	try:
		if not qb_bill_id:
			frappe.response['status_code'] = "500"
			frappe.response['message'] = "No Bill found to delete.!!"
		
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			resp_token = get_quickbooks_access_token()
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Token refresh"
			access_token = resp_token['access_token']
		
		headers = {
			"Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
		}

		get_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill/{qb_bill_id}"

		get_response = requests.get(get_url, headers=headers)
		bill_data = get_response.json().get("Bill")

		sync_token = bill_data.get("SyncToken")
		frappe.db.set_value("Bill", {"qb_bill_id": qb_bill_id}, "sync_token", sync_token)

		delete_url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill?operation=delete&minorversion={MINOR_VERSION}"

		payload = {
			"Id": qb_bill_id,
			"SyncToken": sync_token
		}

		response = requests.post(delete_url, headers=headers, json=payload)
		
		doc_bill = frappe.get_doc("Bill", {"qb_bill_id": qb_bill_id})
		doc_bill.append("request_and_response", {
			"event": "Delete Bill",
			"payload": payload or {},
			"response": response.json() if response else ""
		})

		doc_bill.save(ignore_permissions=True)
		frappe.db.commit()
		if response.status_code == 200:
			frappe.db.set_value("Bill", {"qb_bill_id": qb_bill_id}, "is_active", 0)
			frappe.db.commit()
			return {"status_code":200,"status": "success", "message": "Bill deleted successfully.", "data": response.json()}
		else:
			return {"status_code":500,"status": "error", "message": "Failed to delete bill.", "data": response.json()}

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Bill Deletion Error")
		frappe.response['status_code'] = "500"
		frappe.response['message'] = "QuickBooks Bill Deletion Error"




@frappe.whitelist()
def create_bill_from_ocr_data_manual():
	dct_data = frappe.form_dict.get("data")
	create_bill_from_ocr_data(dct_data)

@frappe.whitelist()
def create_bill_from_ocr_data(dct_data):
	try:

		if isinstance(dct_data, str):
			try:
				# dct_data = json.dumps(dct_data)
				dct_data = json.loads(dct_data)
			except:
				# Fall back to Python literal eval for single-quoted strings
				dct_data = ast.literal_eval(dct_data)
		
		if not dct_data:
			return {"status_code": 500, "status": "error", "message": "No data received to create the bill."}
		print(dct_data,'dct_data')
		access_token = frappe.cache().get_value("quickbooks_access_token")
		if not access_token:
			start_time = time.time()
			resp_token = get_quickbooks_access_token()
			duration = time.time() - start_time
			print(f"QuickBooks token fetch took {duration:.2f} seconds","time duration for token fetch")
			if not resp_token:
				frappe.local.response['status_code'] = "401"
				frappe.local.response['message'] = "Something went wrong in Toke refresh"
			access_token = resp_token['access_token']
			

		headers = {
			"Authorization": f"Bearer {access_token}",
			"Content-Type": "application/json",
			"Accept": "application/json"
		}
		vendors = {}
		if dct_data.get("issuer_company"):
			issuer_company = dct_data.get('issuer_company').upper()
			query = f"SELECT * FROM Vendor WHERE DisplayName LIKE '%{issuer_company}%'"
			url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/query?query={query}&minorversion={MINOR_VERSION}"
			response = requests.get(url, headers=headers)
			if response.status_code != 200:
				frappe.log_error(response.text, "QuickBooks Vendor Fetch Error")
				frappe.throw("Failed to retrieve vendors from QuickBooks")

			dct_vendors = response.json()
			vendors = dct_vendors.get("QueryResponse", {}).get("Vendor", [{}])[0]
		
		dct_bill_data = {}
		dct_bill_data["doctype"] = "Bill"
		dct_bill_data["origin"] = "OCR"
		dct_bill_data["document_number"] = get_document_number("Quickbook Bill","BL")
		dct_bill_data["is_active"] = 1
		dct_bill_data["vendor_bill_no"] = dct_data.get("invoice_number")
		dct_bill_data["bill_date"] = dct_data.get("bill_date") or frappe.utils.nowdate()
		dct_bill_data["docbooking_idtype"] = dct_data.get("booking_id")
		dct_bill_data["shipping_date"] = dct_data.get("shipping_date")
		dct_bill_data["due_date"] = dct_data.get("due_date")
		dct_bill_data["bol_number"] = dct_data.get("bol_number")
		dct_bill_data["hs_code"] = dct_data.get("hs_code") or dct_data.get("commodity_code") or 400400
		dct_bill_data["origin_port"] = dct_data.get("origin_port")
		dct_bill_data["destination_port"] = dct_data.get("destination_port")
		dct_bill_data["booking_id"] = dct_data.get("booking_id")
		dct_bill_data["memo"] = ", ".join(dct_data.get("container_numbers", []))
		

		vendor_result = frappe.db.sql("""
			SELECT name 
			FROM `tabVendor` 
			WHERE LOWER(vendor_name) = LOWER(%s)
			LIMIT 1
		""", (dct_data.get("issuer_company"),))

		dct_bill_data["vendor"] = vendor_result[0][0] if vendor_result else None
		
		dct_bill_data["quickbooks_vendor"] = vendors
		response_products = fetch_all_quickbooks_products()
		if dct_data.get('line_items', []):
			dct_common = {}
			dct_late_fee = {}
			dct_amendment_fee = {}
			dbl_total_amount_latefee = 0
			dbl_total_amount_amendment = 0
			dbl_total_amount_common = 0
			lst_line_items = []
			lst_items = []
			for item in dct_data.get('line_items', []):
				if item.get('description').upper() in ["LATE FEE","LATE PAYMENT FEE"]:
					for data in response_products:
						if data.get("Name").upper() == "LATE FEE":
							dct_late_fee["late_fee_item_id"] = data.get("Id")
							dct_late_fee["late_fee_item_name"] = data.get("Name")
							dct_late_fee["late_fee_item_desc"] = data.get("Description")
							dct_late_fee["quantity"] = item.get("quantity")
							dct_late_fee["unit"] = item.get("unit")
							dbl_total_amount_latefee += float(item.get("amount"))
							dct_late_fee["unit_price"] = dbl_total_amount_latefee
							
				elif item.get('description').upper() == "AMENDMENT FEE": 
					for data in response_products:
						if data.get("Name").upper() == "AMENDMENT FEE":
							dct_amendment_fee["amendment_fee_item_id"] = data.get("Id")
							dct_amendment_fee["amendment_fee_item_name"] = data.get("Name")
							dct_amendment_fee["amendment_fee_item_desc"] = data.get("Description")
							dct_amendment_fee["quantity"] = 1
							dct_amendment_fee["unit"] = item.get("unit")
							dbl_total_amount_amendment += float(item.get("amount"))
							dct_amendment_fee["unit_price"] = dbl_total_amount_amendment
				else:
					for data in response_products:
						if data.get("Name").upper() == "SHIPPING":
							dct_common["common_item_id"] = data.get("Id")
							dct_common["common_item_name"] = data.get("Name")
							dct_common["common_item_desc"] = data.get("Description")
							dct_common["quantity"] = 1
							dct_common["unit"] = item.get("unit")
							dbl_total_amount_common += float(item.get("amount"))
							dct_common["unit_price"] = dbl_total_amount_common
			
			if dct_late_fee:
				lst_items.append({
						"doctype": "Bill Item Details",
						"product_service": dct_late_fee.get("late_fee_item_name"),
						"description": dct_late_fee.get("late_fee_item_desc"),
						"quantity": 1,
						"uom": data.get("uom"),
						"rate": dbl_total_amount_latefee,
						"amount": dbl_total_amount_latefee,
						"quickbooks_item_id": dct_late_fee.get("late_fee_item_id"),
					})
				dct_item = {
					"Amount": dbl_total_amount_latefee,
					"DetailType": "ItemBasedExpenseLineDetail",
					"ItemBasedExpenseLineDetail": {
						"ItemRef": {
							"value": dct_late_fee.get("late_fee_item_id"),
							"name": dct_late_fee.get("late_fee_item_name")
						},
						"Qty": 1,
						"UnitPrice": dbl_total_amount_latefee
					},
					"Description": dct_late_fee.get("late_fee_item_desc")
				}
				lst_line_items.append(dct_item)

			if dct_amendment_fee:
				lst_items.append({
						"doctype": "Bill Item Details",
						"product_service": dct_amendment_fee.get("amendment_fee_item_name"),
						"description": dct_amendment_fee.get("amendment_fee_item_desc"),
						"quantity": 1,
						"uom": data.get("uom"),
						"rate": dbl_total_amount_amendment,
						"amount": dbl_total_amount_amendment,
						"quickbooks_item_id": dct_amendment_fee.get("amendment_fee_item_id"),
					})
				dct_item = {
					"Amount": dbl_total_amount_amendment,
					"DetailType": "ItemBasedExpenseLineDetail",
					"ItemBasedExpenseLineDetail": {
						"ItemRef": {
							"value": dct_amendment_fee.get("amendment_fee_item_id"),
							"name": dct_amendment_fee.get("amendment_fee_item_name")
						},
						"Qty": 1,
						"UnitPrice": dbl_total_amount_amendment
					},
					"Description": dct_amendment_fee.get("amendment_fee_item_desc")
				}
				lst_line_items.append(dct_item)
			if dct_common:
				lst_items.append({
						"doctype": "Bill Item Details",
						"product_service": dct_common.get("common_item_name"),
						"description": dct_common.get("common_item_desc"),
						"quantity": 1,
						"uom": data.get("uom"),
						"rate": dbl_total_amount_common,
						"amount": dbl_total_amount_common,
						"quickbooks_item_id": dct_common.get("common_item_id"),
					})
				dct_item = {
					"Amount": dbl_total_amount_common,
					"DetailType": "ItemBasedExpenseLineDetail",
					"ItemBasedExpenseLineDetail": {
						"ItemRef": {
							"value": dct_common.get("common_item_id"),
							"name": dct_common.get("common_item_name")
						},
						"Qty": 1,
						"UnitPrice": data.get("rate")
					},
					"Description": dct_common.get("common_item_desc")
				}
				lst_line_items.append(dct_item)
		dct_bill_data["item_details"] = lst_items
		headers = {
			"Authorization": f"Bearer {access_token}",
			"Accept": "application/json",
			"Content-Type": "application/json"
		}

		url = f"{QB_API_URL}/v3/company/{QB_REALM_ID}/bill?minorversion={MINOR_VERSION}"

		# ===== Quickbook Bill Payload =====#
		payload = {
			"VendorRef": {
				"value": vendors.get("Id")
			},
			"DueDate": dct_data.get("due_date", None),
			"DocNumber": dct_bill_data["document_number"],
			"Line": lst_line_items,
			"TxnDate": frappe.utils.nowdate(),
			"PrivateNote": dct_data.get("memo", "Created via Frappe"),
		}
		response = requests.post(url, headers=headers, data=json.dumps(payload))
		existing_bill = frappe.db.exists("Bill", {"vendor_bill_no": dct_data.get("invoice_number")})

		if response.status_code != 200:
			dct_bill_data["status"] = "QB Failed"

			drive_id = (dct_data.get("file_data") or {}).get("attached_to_name")
			if drive_id:
				frappe.db.set_value("Google Drive Invoice File", drive_id, "status", "QB Failed")

			file_data = dct_data.get("file_data") or {}
			file_name = file_data.get("file_name")

			if existing_bill:
				bill_doc = frappe.get_doc("Bill", existing_bill)

				for key, value in dct_bill_data.items():
					bill_doc.set(key, value)

				if file_name:
					file_doc_name = frappe.db.exists("File", {"file_name": file_name})
					if file_doc_name:
						file_doc = frappe.get_doc("File", file_doc_name)

						already_attached = any(
							getattr(att, "file_url", None) == file_doc.file_url
							for att in getattr(bill_doc, "bill_attachments", [])
						)
						if not already_attached:
							bill_doc.append("bill_attachments", {
								"file_url": file_doc.file_url,
								"file": os.path.basename(file_doc.file_url)
							})
				bill_doc.save(ignore_permissions=True)
				frappe.db.commit()

			else:
				bill_doc = frappe.get_doc(dct_bill_data)
				bill_doc.insert(ignore_permissions=True)

				file_name_for_log = file_name or (dct_data.get("file_data") or {}).get("file_name")
				frappe.log_error(response.text, f"QuickBooks Bill Create Error --- {file_name_for_log}")

				if file_name:
					file_doc_name = frappe.db.exists("File", {"file_name": file_name})
					if file_doc_name:
						file_doc = frappe.get_doc("File", file_doc_name)

						already_attached = any(
							getattr(att, "file_url", None) == file_doc.file_url
							for att in getattr(bill_doc, "bill_attachments", [])
						)
						if not already_attached:
							bill_doc.append("bill_attachments", {
								"file_url": file_doc.file_url,
								"file": os.path.basename(file_doc.file_url)
							})

				bill_doc.save(ignore_permissions=True)
				frappe.db.commit()

			return False


		response_data = response.json()
		if response_data.get("Bill", {}):
			lst_req_resp = []
			lst_req_resp.append({
				"doctype": "Bill Request Response",
				"event": "New Bill Created",
				"payload": payload or {},
				"response": response_data.get("Bill") if response_data else None
			})
			dct_bill_data["request_and_response"] = lst_req_resp
			
			dct_bill_data["total_amount"] = float(response_data.get("Bill", {}).get("TotalAmt", None))
			dct_bill_data["balance_amount"] = float(response_data.get("Bill", {}).get("Balance", None))
			dct_bill_data["qb_bill_id"] = response_data.get("Bill", {}).get("Id", None)
			dct_bill_data["sync_token"] = response_data.get("Bill", {}).get("SyncToken", None)
			if existing_bill:
				bill_doc = frappe.get_doc("Bill", existing_bill)
				for key, value in dct_bill_data.items():
					bill_doc.set(key, value)
				bill_doc.save(ignore_permissions=True)
				frappe.db.commit()
			else:
				frappe.get_doc(dct_bill_data).insert(ignore_permissions=True)
				frappe.db.commit()

			bill_id = frappe.db.exists("Bill", {"qb_bill_id": dct_bill_data.get("qb_bill_id")},"name")

			existing_bill = frappe.get_doc("Bill", bill_id)
			# existing_bill.set("bill_attachments", [])
			attachments = frappe.request.files.getlist('attachments')  
			lst_attachments = []
			
			if dct_data.get("file_data") or {}:
				file_name = dct_data.get("file_data").get("file_name")

				# Check if file already in file doc, get it file url with file name
				file_doc = frappe.get_doc("File", {"file_name": file_name})

				if file_doc:
					existing_bill.append("bill_attachments", {
						"file_url": file_doc.file_url,
						"file": os.path.basename(file_doc.file_url)
					})
					lst_attachments.append({
						"file": file_doc.file_url,
						"file_name": os.path.basename(file_doc.file_url),
						"content_type": mimetypes.guess_type(file_doc.file_url)[0] or "application/octet-stream"
					})
				
				drive_id = (dct_data.get("file_data") or {}).get("attached_to_name")
				if drive_id:
					frappe.db.set_value("Google Drive Invoice File", drive_id, "status", "Processed")

			existing_bill.save(ignore_permissions=True)
			frappe.db.commit()
			dct_upload_status = {}
			if lst_attachments:
				upload_response = quickbooks_attach_files(dct_bill_data.get("qb_bill_id"), lst_attachments, bill_id)
				
				if not upload_response:
					dct_upload_status = {
						"status_code": upload_response.get("status_code") or "400",
						"message": upload_response.get("message") or "Failed to upload attachments to QuickBooks.",
						"data": upload_response.get("data") or {}
					}
				else:
					dct_upload_status = {
						"status_code": upload_response.get("status_code"),
						"message": upload_response.get("message"),
						"data": upload_response.get("data")
					}

			frappe.response["message"] = "QuickBooks bill created successfully."
			frappe.response["status_code"] = "200"
			frappe.response["file_upload_status"] = dct_upload_status
			frappe.response["data"] = response_data
		
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "QuickBooks Bill Creation Error")
		frappe.response['status_code'] = "500"
		frappe.response['message'] = "QuickBooks Bill Creation Error"