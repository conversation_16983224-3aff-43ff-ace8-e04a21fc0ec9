{"actions": [], "allow_rename": 1, "autoname": "DKT-.####", "creation": "2025-01-28 11:38:41.891101", "doctype": "DocType", "engine": "InnoDB", "field_order": ["status", "booking", "customer", "customer_name", "column_break_biau", "revision_number", "comment", "files_informations_tab", "shipper_name", "shipper", "material", "column_break_gyoi", "consignee", "shipping_date", "invoice", "hs_code", "customer_invoice_id", "section_break_xcux", "origin", "blno", "telephone", "origin_of_goods", "contact", "main_status", "column_break_adfs", "destination", "destination_contact", "terms", "weight", "containers", "shipline", "attchments_tab", "packing_list", "certificate_of_origin", "send_doc_date", "column_break_ijyq", "form_9", "form_6", "amended_from", "docket_revisions", "customer_id", "country_of_import_export", "package_count", "package_type", "category", "carrier_booking_number", "shipper_id"], "fields": [{"fieldname": "booking", "fieldtype": "Link", "in_list_view": 1, "label": "Booking", "options": "Booking Request"}, {"fieldname": "customer", "fieldtype": "Data", "in_list_view": 1, "label": "Customer"}, {"fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name"}, {"default": "0", "fieldname": "revision_number", "fieldtype": "Data", "label": "Revision Number"}, {"allow_on_submit": 1, "default": "New", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "New\nOpen\n<PERSON><PERSON>\nAcknowledged\nRejected\nAccepted\nReopen"}, {"fieldname": "column_break_biau", "fieldtype": "Column Break"}, {"fieldname": "comment", "fieldtype": "Small Text", "label": "Comment"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Docket DB", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "files_informations_tab", "fieldtype": "Tab Break", "label": "Files Informations"}, {"fieldname": "shipper", "fieldtype": "Small Text", "label": "Shipper"}, {"fieldname": "material", "fieldtype": "Small Text", "label": "Material"}, {"fieldname": "column_break_gyoi", "fieldtype": "Column Break"}, {"fieldname": "consignee", "fieldtype": "Small Text", "label": "Consignee"}, {"fieldname": "shipping_date", "fieldtype": "Date", "label": "Shipping Date"}, {"fieldname": "invoice", "fieldtype": "Data", "label": "Invoice"}, {"fieldname": "hs_code", "fieldtype": "Link", "label": "HS Code", "options": "HS Code"}, {"fieldname": "section_break_xcux", "fieldtype": "Section Break"}, {"fieldname": "origin", "fieldtype": "Data", "label": "Origin"}, {"fieldname": "blno", "fieldtype": "Data", "label": "BLNO"}, {"fieldname": "telephone", "fieldtype": "Data", "label": "Telephone"}, {"fieldname": "origin_of_goods", "fieldtype": "Data", "label": "Origin of goods"}, {"fieldname": "contact", "fieldtype": "Data", "label": "Contact"}, {"fieldname": "column_break_adfs", "fieldtype": "Column Break"}, {"fieldname": "destination", "fieldtype": "Data", "label": "Destination"}, {"fieldname": "destination_contact", "fieldtype": "Data", "label": "Destination Contact"}, {"fieldname": "terms", "fieldtype": "Data", "label": "Terms"}, {"fieldname": "weight", "fieldtype": "Data", "label": "Weight"}, {"fieldname": "containers", "fieldtype": "Data", "label": "Containers"}, {"fieldname": "shipline", "fieldtype": "Data", "label": "Shipline"}, {"fieldname": "attchments_tab", "fieldtype": "Tab Break", "label": "Attachments"}, {"fieldname": "packing_list", "fieldtype": "Data", "label": "PACKING LIST"}, {"fieldname": "certificate_of_origin", "fieldtype": "Data", "label": "    CERTIFICATE OF ORIGIN"}, {"fieldname": "column_break_ijyq", "fieldtype": "Column Break"}, {"fieldname": "form_9", "fieldtype": "Data", "label": "FORM 9"}, {"fieldname": "form_6", "fieldtype": "Data", "label": "FORM 6"}, {"fieldname": "docket_revisions", "fieldtype": "Table", "label": "Docket Revisions", "options": "Docket Revision"}, {"fieldname": "customer_id", "fieldtype": "Link", "label": "Customer Id", "options": "Customer DB"}, {"fieldname": "country_of_import_export", "fieldtype": "Data", "label": "Country of Import Export"}, {"fieldname": "shipper_name", "fieldtype": "Data", "label": "Shipper Name"}, {"fieldname": "customer_invoice_id", "fieldtype": "Data", "label": "Customer Invoice Id"}, {"fieldname": "package_count", "fieldtype": "Int", "label": "Package count"}, {"fieldname": "package_type", "fieldtype": "Data", "label": "Package type"}, {"fieldname": "category", "fieldtype": "Data", "label": "Category"}, {"fieldname": "carrier_booking_number", "fieldtype": "Data", "label": "Carrier Booking Number", "reqd": 1}, {"fieldname": "shipper_id", "fieldtype": "Link", "label": "Shipper Id", "options": "Shipper"}, {"fieldname": "main_status", "fieldtype": "Select", "label": "Main Status", "options": "\nDraft\nFinal"}, {"fieldname": "send_doc_date", "fieldtype": "Date", "label": "Doc Send Date"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-10-28 12:49:16.261643", "modified_by": "Administrator", "module": "Westside", "name": "Docket DB", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Guest", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}