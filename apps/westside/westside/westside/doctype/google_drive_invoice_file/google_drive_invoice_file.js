// Copyright (c) 2025, faircode and contributors
// For license information, please see license.txt

frappe.ui.form.on("Google Drive Invoice File", {
    refresh(frm) {
        // Add custom button
        frm.add_custom_button("OCR Read", function() {
            // Call your custom API with the current document name
            frappe.call({
                method: "westside.api.invoice_ocr.process_invoice_ocr",  // <-- change this to your actual API method path
                args: {
                    docname: frm.doc.name
                },
                freeze: true,
                freeze_message: "Reading OCR data...",
                callback: function(r) {
                    if (r.message) {
                        frappe.msgprint({
                            title: __("OCR Response"),
                            message: __(r.message),
                            indicator: "green"
                        });
                    } else {
                        frappe.msgprint({
                            title: __("Error"),
                            message: __("No response received from OCR API."),
                            indicator: "red"
                        });
                    }
                }
            });
        }).addClass("btn-primary");
    }
});

