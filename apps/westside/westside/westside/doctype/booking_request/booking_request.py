# Copyright (c) 2025, faircode and contributors
# For license information, please see license.txt

# import frappe
import datetime
import json
from frappe.model.document import Document
import frappe
import requests
import re
import traceback

from westside.www.API.inttra_auth_api import get_inttra_credentials

MODE_MAPPING = {
    "MaritimeTransport": "Maritime Transport",
    "RailTransport": "Rail Transport",
    "RoadTransport": "Road Transport",
    "InlandWaterTransport": "Inland Water Transport",
    "Rail_WaterTransport": "Rail/Water Combined",
    "Rail_RoadTransport": "Rail/Road",
    "Road_WaterTransport": "Road/Water Combined",
}

INTTRA_MOVE_TYPES = {
    "PortToPort": "Port, Ramp, or CY to Port, Ramp, or CY",
    "PortToDoor": "Port, Ramp, or CY to Door",
    "DoorToPort": "Door to Port, Ramp, or CY",
    "DoorToDoor": "Door to Door"
}



class BookingRequest(Document):    
    
    def on_update(self):
        
        if self.flags.get("ignore_inttra"):
            print("update")
            return
        
        if not self.flags.get("inttra_sending"):  
            
            try:
                self.flags.inttra_sending = True  
                # upt_resp = amend_booking(self)
                
                # frappe.local.response["message"] = upt_resp
            finally:
                self.flags.inttra_sending = False  
                

@frappe.whitelist(allow_guest=True)
def send_booking_to_inttra(booking_request):
    """
    Send new booking request to INTTRA.
    """


    try:

        response = get_inttra_credentials()

        if response.get("status_code") != 200:
            frappe.throw("Failed to get INTTRA credentials")
        
        token = response.get("access_token") or ""
        url = f"{response.get('url')}/booking/request/"


        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        doc_carrier = frappe.get_value("Carrier", booking_request.booking_agent, ["partyalias", "partyname1","inttra_id"], as_dict=True)
        
        doc_unlocode = frappe.get_all(
        "UNLOCODE Locations",
        filters={"name": ["in", [booking_request.place_of_carrier_receipt, booking_request.place_of_carrier_delivery]]},
        fields=["name", "locode", "country_code","country","location_name"]
        )
        unlocode_map = {item['name']: item for item in doc_unlocode}

        place_of_reciept = unlocode_map.get(booking_request.place_of_carrier_receipt)
        place_of_delivery = unlocode_map.get(booking_request.place_of_carrier_delivery)
        
        earliest_departure_date = ""
        latest_delivery_date = ""
        
        lst_transaction_locations = []

        dct_place_of_receipt = {
                        "locationType": "PlaceOfReceipt",
                        "identifierType": "UNLOC",
                        "identifierValue": place_of_reciept.get("locode") if place_of_reciept.get("locode") else "",
                        "city": place_of_reciept.get("location_name") if place_of_reciept.get("location_name") else "",
                        "subdivision":"",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": place_of_reciept.get("country_code") if place_of_reciept.get("country_code") else "",
                            "countryName": place_of_reciept.get("country") if place_of_reciept.get("country") else ""
                        }
                    }
        if booking_request.earliest_departure_date:
            if isinstance(booking_request.earliest_departure_date, datetime.date):
                earliest_departure_date = booking_request.earliest_departure_date.strftime("%Y%m%d")
            else:
                date_obj = datetime.datetime.strptime(booking_request.earliest_departure_date, "%Y-%m-%d")
                earliest_departure_date = date_obj.strftime("%Y%m%d")
                dct_place_of_receipt["locationDates"] = [
                    {
                        "type": "EarliestDepartureDate",
                        "dateFormat": "CCYYMMDD",
                        "dateValue": earliest_departure_date
                    }
                ]

        
        lst_transaction_locations.append(dct_place_of_receipt)

        dct_place_of_delivery = {
                        "locationType": "PlaceOfDelivery",
                        "identifierType": "UNLOC",
                        "identifierValue": place_of_delivery.get("locode") if place_of_delivery.get("locode") else "",
                        "city": place_of_delivery.get("location_name") if place_of_delivery.get("location_name") else "",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": place_of_delivery.get("country_code") if place_of_delivery.get("country_code") else "",
                            "countryName": place_of_delivery.get("country") if place_of_delivery.get("country") else ""
                        }
                    }
        
        if booking_request.latest_delivery_date:
            if isinstance(booking_request.latest_delivery_date, datetime.date):
                latest_delivery_date = booking_request.latest_delivery_date.strftime("%Y%m%d")
            else:
                date_obj = datetime.datetime.strptime(booking_request.latest_delivery_date, "%Y-%m-%d")
                latest_delivery_date = date_obj.strftime("%Y%m%d")

                dct_place_of_delivery["locationDates"] = [
                                        {
                                            "type": "LatestDeliveryDate",
                                            "dateFormat": "CCYYMMDD",
                                            "dateValue": latest_delivery_date
                                        }
                                    ]
        

        lst_transaction_locations.append(dct_place_of_delivery)


        main_carriages = frappe.get_all(
        "Booking Main Carriage",
        filters={"parent": booking_request.name},
        fields=["port_of_load", "port_of_discharge", "eta","etd","vessel","voyage"]
        )
        lst_transportLegs = []
        for data in main_carriages:
            
            doc_unlocode_main = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.port_of_load, data.port_of_discharge]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_unlocode_main}

            port_of_load = unlocode_map_main.get(data.port_of_load)
            port_of_discharge = unlocode_map_main.get(data.port_of_discharge)
            date_eta = ""
            date_etd = ""
            if data.eta:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")

            if data.etd:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")



            dct_item = {
                        "stage": "MainCarriage", 
                        "vesselName": data.vessel,
                        "conveyanceNumber":data.voyage,
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        }
                    }
            
            
            lst_transportLegs.append(dct_item)
        


        for data in booking_request.add_pre_carriage if booking_request.add_pre_carriage else []:
           
            doc_pre_carriage = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.start, data.end]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_pre_carriage}

            port_of_load = unlocode_map_main.get(data.start)
            port_of_discharge = unlocode_map_main.get(data.end)
            date_eta = ""
            date_etd = ""
            if data.eta:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
            if data.etd:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")
            


            dct_item = {
                        "stage": "PreCarriage", 
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        },
                    }
            
            
            lst_transportLegs.append(dct_item)

        for data in booking_request.add_on_carriage if booking_request.add_on_carriage else []:
           
            doc_pre_carriage = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.start, data.end]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_pre_carriage}

            port_of_load = unlocode_map_main.get(data.start)
            port_of_discharge = unlocode_map_main.get(data.end)
            date_eta = ""
            date_etd = ""
            if data.eta:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
            if data.etd:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")


            dct_item = {
                        "stage": "OnCarriage", 
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        },
                    }
            
            
            lst_transportLegs.append(dct_item)
        
        
        
        lst_packing = []
        lineNumber = 0
        for cargo in booking_request.cargo:
            lineNumber += 1
            dct_packing = {
                "lineNumber": lineNumber,
                "packageType": "OUTER",
                "goodsDescription": cargo.get("cargo_description", ""),
                "goodsGrossWeight": {
                    "weightType": "KGM",  
                    "weightValue": cargo.get("cargo_gross_weight", "")  
                },
                "goodsNetWeight": {
                    "weightType": "KGM",  
                    "weightValue": cargo.get("net_weight", "")
                },
                "count": cargo.get("package_count", "1"),  
                "typeCode": cargo.get("package_type_code", "EDI"),  
                "typeValue": cargo.get("package_type", "HC"),  
                "typeDescription": "",
                "goodsClassificationType": "WCO",
                "goodsClassificationValue": cargo.get("hs_code", "123333")  
            }
            lst_packing.append(dct_packing)


        lst_equipments = []
        lst_haulge = []
        for data in booking_request.containers:
            doc_container = frappe.get_value(
                "Container Type",
                {"name": str(data.container_quantitytype)},
                ["name", "typecode", "groupcode", "typecategory", "shortdescription"],
                as_dict=True
            )
            
            lst_haulge = []
            if booking_request.move_type == 'Port, Ramp, or CY to Port, Ramp, or CY':
                str_move_type = 'PortToPort'
                str_haulage = "MerchantMerchant"
                if data.haulage_detail if data.haulage_detail else []:
                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail

                    
                    RequestedEmptyPickUpDate = ""
                    if haulage_detail.get("emptyPickUp") and haulage_detail.get("emptyPickUp").get("emptyPickupDate") and haulage_detail.get("emptyPickUp").get("emptyPickUpTime"):

                        RequestedEmptyPickUpDate  = haulage_detail.get("emptyPickUp").get("emptyPickupDate") + " " + haulage_detail.get("emptyPickUp").get("emptyPickUpTime")
                        if isinstance(RequestedEmptyPickUpDate, datetime.date):
                            dt_obj = RequestedEmptyPickUpDate
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedEmptyPickUpDate, "%Y-%m-%d %H:%M")
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                    
                  

                    lst_haulge = [{
                        "haulageParty": {
                        "partyName1": haulage_detail.get("emptyPickUp").get("companyName"),
                            "address": {
                                "unstructuredAddress01": haulage_detail.get("emptyPickUp").get("address")[0:35],
                                "unstructuredAddress02": haulage_detail.get("emptyPickUp").get("address")[35:70],
                                "unstructuredAddress03": haulage_detail.get("emptyPickUp").get("address")[70:105],
                                "street01": "",
                                "postalCode": "",
                                "country": {
                                    "countryCodeType": "ISO",
                                }
                            },
                            "partyRole": "EmptyPickUp"
                            },
                            "dates": [
                                {
                                    "dateValue": RequestedEmptyPickUpDate,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "haulageDateType": "RequestedEmptyPickUpDate"
                                }
                            ],
                            "contacts": [
                                {
                                    "name": haulage_detail.get("emptyPickUp").get("contactName"),
                                    "phones": [
                                        haulage_detail.get("emptyPickUp").get("contactNumber")
                                    ]
                                }
                            ] 
                    }]
            elif booking_request.move_type == 'Door to Port, Ramp, or CY':
                str_move_type = "DoorToPort"
                str_haulage = "CarrierMerchant"

                if data.haulage_detail if data.haulage_detail else []:

                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail

                    empty_positioning_date_time  = ""
                    if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("emptyPositioningDate") and haulage_detail.get("shipFrom").get("emptyPositioningTime"):
                        empty_positioning_date_time  = haulage_detail.get("shipFrom").get("emptyPositioningDate") + " " + haulage_detail.get("shipFrom").get("emptyPositioningTime")
                        if isinstance(empty_positioning_date_time, datetime.date):
                            dt_obj = empty_positioning_date_time
                            empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(empty_positioning_date_time, "%Y-%m-%d %H:%M")
                            empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        
                    full_pickUp_date_time = ""
                    if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("fullPickupDate") and haulage_detail.get("shipFrom").get("fullPickupTime"):
                        full_pickUp_date_time = haulage_detail.get("shipFrom").get("fullPickupDate") + " " + haulage_detail.get("shipFrom").get("fullPickupTime")
                        if isinstance(full_pickUp_date_time, datetime.date):
                            dt_obj = full_pickUp_date_time
                            full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(full_pickUp_date_time, "%Y-%m-%d %H:%M")
                            full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")

                    lst_haulge =  [{
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipFrom").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipFrom").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipFrom").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipFrom").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipFrom"
                                },
                                "dates": [
                                    {
                                        "dateValue": empty_positioning_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "EmptyPositioningDate"
                                    },
                                    {
                                        "dateValue": full_pickUp_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "FullPickUpDateTime"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipFrom").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipFrom").get("contactNumber")
                                        ]
                                    }
                                ] 
                            }]
                    
                

            elif booking_request.move_type == 'Door to Door':
                    str_move_type = "DoorToDoor"
                    str_haulage = "CarrierCarrier"
                    if data.haulage_detail if data.haulage_detail else []:

                        if type(data.haulage_detail) == str:
                            haulage_detail = json.loads(data.haulage_detail)
                        else:
                            haulage_detail = data.haulage_detail

                        empty_positioning_date_time  = ""
                        if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("emptyPositioningDate") and haulage_detail.get("shipFrom").get("emptyPositioningTime"):
                            empty_positioning_date_time  = haulage_detail.get("shipFrom").get("emptyPositioningDate") + " " + haulage_detail.get("shipFrom").get("emptyPositioningTime")
                            if isinstance(empty_positioning_date_time, datetime.date):
                                dt_obj = empty_positioning_date_time
                                empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(empty_positioning_date_time, "%Y-%m-%d %H:%M")
                                empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        
                        full_pickUp_date_time = ""
                        if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("fullPickupDate") and haulage_detail.get("shipFrom").get("fullPickupTime"):
                            full_pickUp_date_time = haulage_detail.get("shipFrom").get("fullPickupDate") + " " + haulage_detail.get("shipFrom").get("fullPickupTime")
                            if isinstance(full_pickUp_date_time, datetime.date):
                                dt_obj = full_pickUp_date_time
                                full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(full_pickUp_date_time, "%Y-%m-%d %H:%M")
                                full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        
                        RequestedDoorDeliveryDate = ""
                        if haulage_detail.get("shipTo") and haulage_detail.get("shipTo").get("deliveryDate") and haulage_detail.get("shipTo").get("deliveryTime"):
                            RequestedDoorDeliveryDate = haulage_detail.get("shipTo").get("deliveryDate") +" " + haulage_detail.get("shipTo").get("deliveryTime")
                            if isinstance(RequestedDoorDeliveryDate, datetime.date):
                                dt_obj = RequestedDoorDeliveryDate
                                RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(RequestedDoorDeliveryDate, "%Y-%m-%d %H:%M")
                                RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")

                        
                        lst_haulge = [
                            {
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipFrom").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipFrom").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipFrom").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipFrom").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipFrom"
                                },
                                "dates": [
                                    {
                                        "dateValue": empty_positioning_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "EmptyPositioningDate"
                                    },
                                    {
                                        "dateValue": full_pickUp_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "FullPickUpDateTime"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipFrom").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipFrom").get("contactNumber")
                                        ]
                                    }
                                ] 
                            },
                            {
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipTo").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipTo").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipTo").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipTo").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipTo"
                                },
                                "dates": [
                                    {
                                        "dateValue": RequestedDoorDeliveryDate,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "RequestedDoorDeliveryDate"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipTo").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipTo").get("contactNumber")
                                        ]
                                    }
                                ] 
                            }
                        ]
                    
                        
            elif booking_request.move_type == 'Port, Ramp, or CY to Door':
                str_move_type = "PortToDoor"
                str_haulage = "MerchantCarrier"
                
                if data.haulage_detail if data.haulage_detail else []:

                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail

                    RequestedEmptyPickUpDate = ""
                    if haulage_detail.get("emptyPickUp") and haulage_detail.get("emptyPickUp").get("emptyPickupDate") and haulage_detail.get("emptyPickUp").get("emptyPickUpTime"):
                        RequestedEmptyPickUpDate  = haulage_detail.get("emptyPickUp").get("emptyPickupDate") + " " + haulage_detail.get("emptyPickUp").get("emptyPickUpTime")
                        if isinstance(RequestedEmptyPickUpDate, datetime.date):
                            dt_obj = RequestedEmptyPickUpDate
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedEmptyPickUpDate, "%Y-%m-%d %H:%M")
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                    
                    RequestedDoorDeliveryDate = ""
                    if haulage_detail.get("shipTo") and haulage_detail.get("shipTo").get("deliveryDate") and haulage_detail.get("shipTo").get("deliveryTime"):
                        RequestedDoorDeliveryDate = haulage_detail.get("shipTo").get("deliveryDate") +" " + haulage_detail.get("shipTo").get("deliveryTime")
                        if isinstance(RequestedDoorDeliveryDate, datetime.date):
                            dt_obj = RequestedDoorDeliveryDate
                            RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedDoorDeliveryDate, "%Y-%m-%d %H:%M")
                            RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")


                lst_haulge = [
                    {
                    "haulageParty": {
                        "partyName1": haulage_detail.get("emptyPickUp").get("companyName"),
                        "address": {
                            "unstructuredAddress01": haulage_detail.get("emptyPickUp").get("address")[0:35],
                            "unstructuredAddress02": haulage_detail.get("emptyPickUp").get("address")[35:70],
                            "unstructuredAddress03": haulage_detail.get("emptyPickUp").get("address")[70:105],
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                            }
                        },
                        "partyRole": "EmptyPickUp"
                        },
                        "dates": [
                            {
                                "dateValue": RequestedEmptyPickUpDate,
                                "dateFormat": "CCYYMMDDHHMM",
                                "haulageDateType": "RequestedEmptyPickUpDate"
                            }
                        ],
                        "contacts": [
                            {
                                "name": haulage_detail.get("emptyPickUp").get("contactName"),
                                "phones": [
                                    haulage_detail.get("emptyPickUp").get("contactNumber")
                                ]
                            }
                        ] 
                    },
                    {
                        "haulageParty": {
                        "partyName1": haulage_detail.get("shipTo").get("companyName"),
                        "address": {
                            "unstructuredAddress01": haulage_detail.get("shipTo").get("address")[0:35],
                            "unstructuredAddress02": haulage_detail.get("shipTo").get("address")[35:70],
                            "unstructuredAddress03": haulage_detail.get("shipTo").get("address")[70:105],
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                            }
                        },
                        "partyRole": "ShipTo"
                        },
                        "dates": [
                            {
                                "dateValue": RequestedDoorDeliveryDate,
                                "dateFormat": "CCYYMMDDHHMM",
                                "haulageDateType": "RequestedDoorDeliveryDate"
                            }
                        ],
                        "contacts": [
                            {
                                "name": haulage_detail.get("shipTo").get("contactName"),
                                "phones": [
                                    haulage_detail.get("shipTo").get("contactNumber")
                                ]
                            }
                        ] 
                    }
                ]

            dct_equipments = {
                            "comments": [data.container_comments],
                            "count": data.number_of_containers,
                            "serviceType": "FCLFCL",
                            "equipmentSizeCode": {
                                "sizeCodeType": "ISO",
                                "sizeCodeValue": doc_container.typecode,
                                "sizeCodeDescription": doc_container.shortdescription
                            },
                            
                            
                            "nonActiveReefer": False
                        }
            
            if str_haulage or lst_haulge:
                dct_equipments["haulage"] = {}
                
                if str_haulage:
                    dct_equipments["haulage"]["arrangement"] = str_haulage

                if lst_haulge:
                    dct_equipments["haulage"]["points"] = lst_haulge

            lst_equipments.append(dct_equipments)
        
        dct_pay = {}
        dct_payement_enum = {"Additional Charges":"Additional", "Basic Freight":"OceanFreight", "Destination Haulage Charges":"DestinationHaulage", "Destination Port Charges":"DestinationTerminalHandling", "Origin Port Charges":"OriginTerminalHandling", "Origin Haulage Charges":"OriginHaulage"}
        dct_pay_terms = {"Pre-paid","Collect","Payable Elsewhere"}
        
        for pay in booking_request.payment if booking_request.payment else []:
            payment_location = pay.get('payment_location')
            doc_payment_location = {}
            if payment_location:
                doc_payment_location = frappe.get_all(
                    "UNLOCODE Locations",
                    filters={"name": payment_location},
                    fields=["name", "locode", "country_code", "country", "location_name", "sub_division"]
                )
                doc_payment_location = doc_payment_location[0] if doc_payment_location else {}

            lst_payment = [
                {
                    "chargeType": dct_payement_enum.get(pay.get('charge_type')),
                    "paymentTerm":"Prepaid",
                    "chargeLocation": {
                        "identifierType": "UNLOC",
                        "identifierValue": doc_payment_location.get("locode", ""),
                        "city": doc_payment_location.get("location_name", ""),
                        "subdivision": doc_payment_location.get("sub_division", ""),
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": doc_payment_location.get("country_code", "")
                        },
                        "locationType": "PlaceOfPayment"
                    }
                }
            ]

            dct_pay[pay.get('payer')] = lst_payment

        doc_shipper = frappe.get_doc("Shipper", booking_request.shipper) if booking_request.shipper else None
        if not doc_shipper:
            frappe.db.set_value("Booking Request", booking_request.name, "booking_status", "FAILED")
            frappe.local.response["status_code"] = 204
            frappe.local.response["message"] = f"Please check the Shipper {booking_request.shipper}"
            return{
                "status_code": 204,
                "message": [{"status": "error", "message": f"Please check the Shipper {booking_request.shipper}"}]
            }

            
        dct_booker = {
                    "partyRole": "Booker",
                    "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                    "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                    "partyName1": doc_shipper.shipper_name[:35] if doc_shipper.shipper_name else "",
                    "contacts": [
                        {
                            "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                            "contactType": "InformationContact",
                            "phones": [
                                doc_shipper.phone if doc_shipper.phone else ""
                            ],
                            "emails": [
                                doc_shipper.email if doc_shipper.email else ""
                            ]
                        }
                    ]    
                }
        charges = dct_pay.get("Booker")
        if charges:
            dct_booker["charges"] = charges
        
        
        if doc_shipper:
        
            dct_shipper = {
                            "partyRole": "Shipper",
                            "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                            "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                            "partyName1": doc_shipper.shipper_name[:35] if doc_shipper.shipper_name else "",
                            "address": {
                                "unstructuredAddress01": doc_shipper.custom_address[:35] if doc_shipper.custom_address else "",
                                "unstructuredAddress02": doc_shipper.custom_address[35:70] if doc_shipper.custom_address else "",
                                "unstructuredAddress03": doc_shipper.custom_address[70:105] if doc_shipper.custom_address else "",
                                "street01": "",
                                "street02": "",
                                "state": "",
                                "postalCode": doc_shipper.postal_code if doc_shipper.postal_code else "",
                                "country": {
                                    "countryCodeType": "ISO",
                                    "countryCodeValue": "US"
                                }
                            },
                            "contacts": [
                                {
                                    "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                    "contactType": "InformationContact",
                                    "phones": [
                                        doc_shipper.phone if doc_shipper.phone else ""
                                    ],
                                    "emails": [
                                        doc_shipper.email if doc_shipper.email else ""
                                    ]
                                }
                            ]
                        }
        elif booking_request.get("shipper_on_booking"):
            dct_shipper_on_booking = booking_request.get("shipper_on_booking")
            dct_shipper = {
                            "partyRole": "Shipper",
                            "partyINTTRACompanyId": "",
                            "partyAlias": "",
                            "partyName1": "",
                            "address": {
                                "unstructuredAddress01": dct_shipper_on_booking[:35] if doc_shipper.custom_address else "",
                                "unstructuredAddress02": dct_shipper_on_booking[35:70] if doc_shipper.custom_address else "",
                                "unstructuredAddress03": dct_shipper_on_booking[70:105] if doc_shipper.custom_address else "",
                                "street01": "",
                                "street02": "",
                                "state": "",
                                "postalCode": doc_shipper.postal_code if doc_shipper.postal_code else "",
                                "country": {
                                    "countryCodeType": "ISO",
                                    "countryCodeValue": "US"
                                }
                            },
                            "contacts": [
                                {
                                    "name": "",
                                    "contactType": "InformationContact",
                                    "phones": [""],
                                    "emails": [""]
                                }
                            ]
                        }
            
        charges = dct_pay.get("Shipper")
        if charges:
            dct_shipper["charges"] = charges
        
        dct_carrier = {
                        "partyRole": "Carrier",
                        "partyAlias": doc_carrier.partyalias[:35] if doc_carrier.partyalias else "",
                        "partyName1": doc_carrier.partyname1[:35] if doc_carrier.partyname1 else "",
                        "partyINTTRACompanyId": doc_carrier.inttra_id if doc_carrier.partyname1 else "",
                        "contacts": [
                            {
                                "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                "contactType": "InformationContact",
                                "phones": [
                                    doc_shipper.phone if doc_shipper.phone else ""
                                ],
                                "emails": [
                                    doc_shipper.email if doc_shipper.email else ""
                                ]
                            }
                        ]
                    }
        charges = dct_pay.get("Carrier")
        if charges:
            dct_carrier["charges"] = charges
        
        dct_notify_party= {}
        if booking_request.get("notify_parties"):
            notify_party_doc = frappe.get_doc("Customer DB", booking_request.notify_parties)
            address = notify_party_doc.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party = {
                "partyRole": "MainNotifyParty",
                "partyName1": notify_party_doc.get("customer_name", "")[:35] if notify_party_doc.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_doc.get("country", ""),
                    "postalCode": notify_party_doc.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking_request.get("notify_party_on_booking"):
            notify_party_on_booking = booking_request.get("notify_party_on_booking")
            dct_notify_party = {
                "partyRole": "MainNotifyParty",
                "partyName1": notify_party_on_booking[:35] if notify_party_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_on_booking[0:35] if notify_party_on_booking else "",
                    "unstructuredAddress02": notify_party_on_booking[35:70] if notify_party_on_booking else "",
                    "unstructuredAddress03": notify_party_on_booking[70:105] if notify_party_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }

        dct_notify_party_one= {}
        if booking_request.get("notify_party_1"):
            notify_party_1 = frappe.get_doc("Customer DB", booking_request.notify_party_1)
            address = notify_party_1.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party_one = {
                "partyRole": "FirstAdditionalNotifyParty",
                "partyName1": notify_party_1.get("customer_name", "")[:35] if notify_party_1.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_1.get("country", ""),
                    "postalCode": notify_party_1.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking_request.get("notify_party_1_on_booking"):
            notify_party_1_on_booking = booking_request.get("notify_party_1_on_booking")
            dct_notify_party_one = {
                "partyRole": "FirstAdditionalNotifyParty",
                "partyName1": notify_party_1_on_booking[:35] if notify_party_1_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_1_on_booking[0:35] if notify_party_1_on_booking else "",
                    "unstructuredAddress02": notify_party_1_on_booking[35:70] if notify_party_1_on_booking else "",
                    "unstructuredAddress03": notify_party_1_on_booking[70:105] if notify_party_1_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }


        dct_notify_party_two= {}
        if booking_request.get("notify_party_2"):
            notify_party_2 = frappe.get_doc("Customer DB", booking_request.notify_party_2)
            address = notify_party_2.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party_two = {
                "partyRole": "SecondAdditionalNotifyParty",
                "partyName1": notify_party_2.get("customer_name", "")[:35] if notify_party_2.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_2.get("country", ""),
                    "postalCode": notify_party_2.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking_request.get("notify_party_2_on_booking"):
            notify_party_2_on_booking = booking_request.get("notify_party_2_on_booking")
            dct_notify_party_two = {
                "partyRole": "SecondAdditionalNotifyParty",
                "partyName1": notify_party_2_on_booking[:35] if notify_party_2_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_2_on_booking[0:35] if notify_party_2_on_booking else "",
                    "unstructuredAddress02": notify_party_2_on_booking[35:70] if notify_party_2_on_booking else "",
                    "unstructuredAddress03": notify_party_2_on_booking[70:105] if notify_party_2_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_consignee = {}
        if booking_request.get("consignee"):
            consignee_doc = frappe.get_doc("Customer DB", booking_request.consignee)
            address = consignee_doc.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_consignee = {
                "partyRole": "Consignee",
                "partyName1": consignee_doc.get("customer_name", "")[:35] if consignee_doc.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": consignee_doc.get("country", ""),
                    "postalCode": consignee_doc.get("customer_zip", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking_request.get("consignee_on_booking"):
            consignee_on_booking = booking_request.get("consignee_on_booking")
            dct_consignee = {
                "partyRole": "Consignee",
                "partyName1": consignee_on_booking[:35] if consignee_on_booking else "",
                "address": {
                    "unstructuredAddress01": consignee_on_booking[0:35] if consignee_on_booking else "",
                    "unstructuredAddress02": consignee_on_booking[35:70] if consignee_on_booking else "",
                    "unstructuredAddress03": consignee_on_booking[70:105] if consignee_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_contract_party = {}
        if booking_request.get("contract_party"):
            try:
                contract_party = frappe.get_doc("Shipper", booking_request.contract_party)
                if contract_party:
                    
                    dct_contract_party = {
                        "partyRole": "ContractParty",
                        "partyName1": contract_party.shipper_name[:35] if contract_party.shipper_name else "",
                        "address": {
                            "unstructuredAddress01": contract_party.custom_address[0:35] if contract_party.custom_address else "",
                            "unstructuredAddress02": contract_party.custom_address[35:70] if contract_party.custom_address else "",
                            "unstructuredAddress03": contract_party.custom_address[70:105] if contract_party.custom_address else "",
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                                
                            }
                        }
                    } 
                else:
                    contract_party = frappe.get_doc("Customer DB", booking_request.contract_party)
                    address = contract_party.customer_address or ""
                    if address:
                        address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                        address_parts = (address_parts + [""] * 3)[:3]
                    else:
                        address_parts = ["", "", ""]

                    dct_contract_party = {
                        "partyRole": "ContractParty",
                        "partyName1": contract_party.get("customer_name", "")[:35] if contract_party.get("customer_name", "") else "",
                        "address": {
                            "unstructuredAddress01": address_parts[0],
                            "unstructuredAddress02": address_parts[1],
                            "unstructuredAddress03": address_parts[2],
                            "street01": contract_party.get("country", ""),
                            "postalCode": contract_party.get("customer_zip", ""),
                            "country": {
                                "countryCodeType": "ISO",
                                
                            }
                        }
                    }
            except Exception as e:
                pass
        elif booking_request.get("contract_party_on_booking"):
            try:
                contract_party_on_booking = booking_request.get("contract_party_on_booking")
                dct_contract_party = {
                    "partyRole": "ContractParty",
                    "partyName1": contract_party_on_booking[:35] if contract_party_on_booking else "",
                    "address": {
                        "unstructuredAddress01": contract_party_on_booking[0:35] if contract_party_on_booking else "",
                        "unstructuredAddress02": contract_party_on_booking[35:70] if contract_party_on_booking else "",
                        "unstructuredAddress03": contract_party_on_booking[70:105] if contract_party_on_booking else "",
                        "street01": "",
                        "postalCode": "",
                        "country": {
                            "countryCodeType": "ISO",
                            
                        }
                    }
                }
            except Exception as e:
                pass

        dct_notification_party = {}
        if booking_request.get("partner_email_notifications"):           
            try:
                emails_raw = booking_request.get("partner_email_notifications") or ""
                email_list = [email.strip() for email in re.split(r'[\n,]+', emails_raw) if email.strip()]
                lst_noficn = []
                for email in email_list:
                    name = email.split('@')[0] if '@' in email else email

                    dct_notifn = {                                           
                                    "contactType": "NotificationContact",
                                    "name": name,
                                    "emails": [
                                        email
                                    ]
                                }
                    lst_noficn.append(dct_notifn)
        


                dct_notification_party = {
                                        "partyRole": "MessageRecipient",
                                        "partyName1": "MRMR and Address 35 charactersfield",
                                        "contacts": lst_noficn
                                    }
            except Exception as e:
                pass
        
        dct_bookingoffice_party = {}

        final_party_list = []

        for party_dict in [dct_booker, dct_shipper, dct_carrier, dct_notify_party,dct_consignee,dct_contract_party,dct_notification_party,dct_bookingoffice_party,dct_notify_party_two,dct_notify_party_one ]:
            if party_dict:  
                final_party_list.append(party_dict)  

        
        
        if booking_request.booking_office:
            booking_office =  frappe.get_all(
                            "UNLOCODE Locations",
                            filters={"name": ["in", [booking_request.booking_office]]},
                            fields=["name", "locode", "country_code","country","location_name"]
                        )
            if booking_office:
                booking_office = booking_office[0]
                dct_booking_office = {
                        "locationType": "BookingOffice",
                        "identifierType": "UNLOC",
                        "identifierValue": booking_office.get("locode") if booking_office.get("locode") else "",
                        "city": booking_office.get("location_name") if booking_office.get("location_name") else "",
                        "subdivision": booking_office.get("sub_division") if booking_office.get("sub_division") else "",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": booking_office.get("country_code") if booking_office.get("country_code") else "",
                            "countryName": booking_office.get("country") if booking_office.get("country") else ""
                        }
                    }     
                lst_transaction_locations.append(dct_booking_office)
        
        lst_reference = []
        if booking_request.contract_number:
            dct_ref = {
            "referenceType": "ContractNumber",
            "referenceValue": booking_request.contract_number
            }
            lst_reference.append(dct_ref)
        if booking_request.tariff_number:
            dct_ref =    {
                "referenceType": "FreightTariffNumber",
                "referenceValue": booking_request.tariff_number
                }
            lst_reference.append(dct_ref)  

        if booking_request.shippers_reference_numbers:
            dct_ref =    {
                "referenceType": "ShipperReferenceNumber",
                "referenceValue": booking_request.shippers_reference_numbers
                }
            lst_reference.append(dct_ref)    
        if booking_request.forwarders_reference_numbers:
            dct_ref =    {
                "referenceType": "FreightForwarderRefNumber",
                "referenceValue": booking_request.forwarders_reference_numbers
                }
            lst_reference.append(dct_ref)    
        if booking_request.bl_reference_numbers:
            dct_ref =    {
                "referenceType": "BillOfLadingNumber",
                "referenceValue": booking_request.bl_reference_numbers
                }
            lst_reference.append(dct_ref)      

        if booking_request.purchase_order_numbers:
            dct_ref =    {
                "referenceType": "PurchaseorderNumber",
                "referenceValue": booking_request.purchase_order_numbers
                }
            lst_reference.append(dct_ref)    
        
        
        if booking_request.contract_party_reference_numbers:
            dct_ref =    {
                "referenceType": "ContractPartyReferenceNumber",
                "referenceValue": booking_request.contract_party_reference_numbers
                }
            lst_reference.append(dct_ref) 

        if booking_request.consignees_reference_numbers:
            dct_ref =    {
                "referenceType": "ConsigneeReferenceNumber",
                "referenceValue": booking_request.consignees_reference_numbers
                }
            lst_reference.append(dct_ref) 

        
        shipmentId =  generate_unique_id()
        payload = {
                    
                "bookingState": "REQUEST",
                "moveType": str_move_type,
                "parties": final_party_list,
                "references": lst_reference,
                "messageDate": {
                    "dateFormat": "CCYYMMDDHHMM",
                    "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
                },
                "creationDate": {
                    "dateFormat": "CCYYMMDDHHMM",
                    "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
                },
                "shipmentId": shipmentId,
                "transactionContact": {
                    "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                    "contactType": "InformationContact",
                    "emails": [
                        doc_shipper.email if doc_shipper.email else ""
                    ],
                    "phones": [
                        doc_shipper.phone if doc_shipper.phone else ""
                    ]
                },
                "customerChangeSummary": [
                    booking_request.customer_comments
                ],
                "cargoHazardousIndicator": False,
                "cargoEnvironmentalPollutantIndicator": False,
                "cargoOutofGaugeIndicator": False,
                "notifyMe": True,
                
                "transactionLocations": lst_transaction_locations,
                "transportLegs": lst_transportLegs,
                "packageDetails": lst_packing,

                "equipments": lst_equipments,
                
                "perContainerReleaseFlag": False
                }
        

        print(payload)
        
        if not booking_request.inttra_booking_id and not booking_request.inttra_reference and booking_request.inttra_response_status != 200:
            response = requests.post(url, json=payload, headers=headers)

            if response.status_code == 200:
                response_data = response.json()

                booking_details = response_data.get("booking", {}).get("details", [])
                booking_id = booking_details[0].get("bookingId")

                if booking_details and booking_id:

                    return{
                        "response": response_data,
                        "status_code": response.status_code,
                        "message": "Booking successfully sent to INTTRA",
                        "shipmentId": shipmentId,
                        "payload": payload,
                    }
                else:
                    frappe.msgprint("Failed to send booking to INTTRA")
                    frappe.local.response["status_code"] = response.status_code
                    frappe.local.response["message"] = f"Failed to send booking to INTTRA: {response.text}"
                    return{
                        "status_code": response.status_code,
                        "message": f"Failed to send booking to INTTRA: {response.text}"
                    }
            else:
                frappe.local.response["status_code"] = response.status_code
                resp_data = json.loads(response.text)
                if resp_data and resp_data[0].get('message'):
                    frappe.msgprint(resp_data[0].get('message'))
                    frappe.local.response["message"] = resp_data[0].get('message')
                return {
                    "status_code": response.status_code,
                    "message": resp_data,
                    "payload": payload,
                }
                
           
        else:
            frappe.msgprint("Booking updated successfully")
            frappe.local.response["status_code"] = 200
            frappe.local.response["message"] = "Booking updated successfully"
            return {
                "status_code": 200,
                "message": "Booking updated successfully"
            }

    except Exception as e:
        frappe.throw(f"Failed to send booking to INTTRA: {e}")
        frappe.local.response["status_code"] = 500
        frappe.local.response["message"] = f"Failed to send booking to INTTRA: {e}"
        return {
            "status_code": 500,
            "message": f"Failed to send booking to INTTRA: {e}"            
        }
    


@frappe.whitelist()
def check_booking_status(booking_id):   
    """
    Check the status of the booking request on INTTRA by inttraReferenceId.
    """

    try:
        

        booking = frappe.get_doc("Booking Request", booking_id)

        if not booking.inttra_reference:
            frappe.throw(("INTTRA Reference ID is missing."))

        
        auth_response = get_inttra_credentials()

        if auth_response.get("status_code") != 200:
            frappe.throw("Failed to get INTTRA credentials")
        
        token = auth_response.get("access_token") or ""
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        
        url = f"{auth_response.get('url')}/booking/{booking.inttra_reference}/"

       
        status_response = requests.get(url, headers=headers)
        response_data = status_response.json()

        if status_response.status_code == 200:
            
            booking_status = response_data[len(response_data)-1]['payload']['bookingState']

            booking.db_set('booking_status', booking_status)
            frappe.db.commit()

            api_log = frappe.get_doc({
                'doctype': 'Inttra API Logs',
                'description': 'Booking Status Check',
                'booking_id': booking_id,
                'inttra_refference_id': booking.inttra_reference,
                'booking_status': booking_status,
                'response': json.dumps(response_data) if response_data else ""
            })
            api_log.insert(ignore_permissions=True)
            frappe.db.commit()
            frappe.local.response["status_code"] = 200
            frappe.local.response["message"] = f"Booking Status updated to: {booking_status}"

            frappe.msgprint(f"Booking Status updated to: {booking_status}")
        else:
            api_log = frappe.get_doc({
                'doctype': 'Inttra API Logs',
                'description': 'Booking Status Check',
                'booking_id': booking_id,
                'inttra_refference_id': booking.inttra_reference,
                'booking_status': "FAILED",
                'response': json.dumps(response_data) if response_data else ""
            })
            api_log.insert(ignore_permissions=True)
            frappe.db.commit()
            frappe.local.response["status_code"]= status_response.status_code
            frappe.local.response["message"] = response_data.get('message', 'Failed to fetch booking status.')
            frappe.throw(response_data.get('message', 'Failed to fetch booking status.'))

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Status Check Error")
        frappe.local.response["status_code"] = 500
        frappe.local.response["message"] = f"An error occurred: {str(e)}"
        frappe.throw(f"An error occurred: {str(e)}")


@frappe.whitelist()
def save_booking_as_template(template_name, booking_data):
    try:
        json_data = json.dumps(json.loads(booking_data))

        template_doc = frappe.get_doc({
            "doctype": "Booking Template",
            "template_name": template_name,
            "booking_data": json_data  
        })
        template_doc.insert(ignore_permissions=True)

        frappe.db.commit()
        return "Template saved successfully."

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Save Booking as Template Error")
        frappe.throw(f"Error saving template: {str(e)}")




@frappe.whitelist(allow_guest=True)
def cancel_booking():   
    """
    Cancel booking request on INTTRA by inttraReferenceId.
    """

    try:
        booking_id = frappe.form_dict.get("name")
        booking = frappe.get_doc("Booking Request", booking_id)

        if not booking.inttra_reference:
            frappe.throw(("INTTRA Reference ID is missing."))

        
        auth_response = get_inttra_credentials()

        if auth_response.get("status_code") != 200:
            frappe.throw("Failed to get INTTRA credentials")
        
        token = auth_response.get("access_token") or ""

        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        booking = frappe.get_doc("Booking Request", booking_id)

        doc_carrier = frappe.get_value("Carrier", booking.booking_agent, ["partyalias", "partyname1","inttra_id"], as_dict=True)
        doc_shipper = frappe.get_doc("Shipper", booking.shipper)

        last_log = frappe.db.sql(
            """
            SELECT * FROM `tabInttra API Logs`
            WHERE inttra_refference_id = %s
            order BY creation DESC LIMIT 1
            """,
            (booking.inttra_reference,), as_dict=True
        )

    

        if booking.get('booking_status') == 'CANCEL':
            frappe.msgprint("Booking already cancelled.")
            return
        
        dct_pay = {}
        dct_payement_enum = {"Additional Charges":"Additional", "Basic Freight":"OceanFreight", "Destination Haulage Charges":"DestinationHaulage", "Destination Port Charges":"DestinationTerminalHandling", "Origin Port Charges":"OriginTerminalHandling", "Origin Haulage Charges":"OriginHaulage"}
        
        for pay in booking.payment if booking.payment else []:
            payment_location = pay.get('payment_location')
            doc_payment_location = {}
            if payment_location:
                doc_payment_location = frappe.get_all(
                    "UNLOCODE Locations",
                    filters={"name": payment_location},
                    fields=["name", "locode", "country_code", "country", "location_name", "sub_division"]
                )
                doc_payment_location = doc_payment_location[0] if doc_payment_location else {}

            lst_payment = [
                {
                    "chargeType": dct_payement_enum.get(pay.get('charge_type')),
                    "paymentTerm":"Prepaid",
                    "chargeLocation": {
                        "identifierType": "UNLOC",
                        "identifierValue": doc_payment_location.get("locode", ""),
                        "city": doc_payment_location.get("location_name", ""),
                        "subdivision": doc_payment_location.get("sub_division", ""),
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": doc_payment_location.get("country_code", "")
                        },
                        "locationType": "PlaceOfPayment"
                    }
                }
            ]

        dct_pay[pay.get('payer')] = lst_payment

        dct_booker = {
                "partyRole": "Booker",
                "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                "partyName1": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                "contacts": [
                    {
                        "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                        "contactType": "InformationContact",
                        "phones": [
                            doc_shipper.phone if doc_shipper.phone else ""
                        ],
                        "emails": [
                            doc_shipper.email if doc_shipper.email else ""
                        ]
                    }
                ]    
            }
        charges = dct_pay.get("Booker")
        if charges:
            dct_booker["charges"] = charges
        
        dct_shipper = {
                        "partyRole": "Shipper",
                        "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                        "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                        "partyName1": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                        "contacts": [
                            {
                                "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                "contactType": "InformationContact",
                                "phones": [
                                    doc_shipper.phone if doc_shipper.phone else ""
                                ],
                                "emails": [
                                    doc_shipper.email if doc_shipper.email else ""
                                ]
                            }
                        ]
                    }
        charges = dct_pay.get("Shipper")
        if charges:
            dct_shipper["charges"] = charges
        
        dct_carrier = {
                        "partyRole": "Carrier",
                        "partyAlias": doc_carrier.partyalias if doc_carrier.partyalias else "",
                        "partyName1": doc_carrier.partyname1 if doc_carrier.partyname1 else "",
                        "partyINTTRACompanyId": doc_carrier.inttra_id if doc_carrier.partyname1 else "",
                        "contacts": [
                            {
                                "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                "contactType": "InformationContact",
                                "phones": [
                                    doc_shipper.phone if doc_shipper.phone else ""
                                ],
                                "emails": [
                                    doc_shipper.email if doc_shipper.email else ""
                                ]
                            }
                        ]
                    }
        charges = dct_pay.get("Carrier")
        if charges:
            dct_carrier["charges"] = charges
        
        dct_notify_party= {}
        if booking.get("notify_parties"):
            notify_party_doc = frappe.get_doc("Notify Party", booking.notify_parties)
            address = notify_party_doc.address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party = {
                "partyRole": "MainNotifyParty",
                "partyName1": notify_party_doc.get("name1", ""),
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_doc.get("country", ""),
                    "postalCode": notify_party_doc.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_consignee = {}
        if booking.get("consignee"):
            consignee_doc = frappe.get_doc("Customer DB", booking.consignee)
            address = consignee_doc.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_consignee = {
                "partyRole": "Consignee",
                "partyName1": consignee_doc.get("customer_name", ""),
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": consignee_doc.get("country", ""),
                    "postalCode": consignee_doc.get("customer_zip", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_contract_party = {}
        if booking.get("contract_party"):
            try:
                contract_party = frappe.get_doc("Contract Party", booking.contract_party)
                address = contract_party.address or ""

                if address:
                    address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                    address_parts = (address_parts + [""] * 3)[:3]
                else:
                    address_parts = ["", "", ""]

                dct_contract_party = {
                    "partyRole": "ContractParty",
                    "partyName1": contract_party.get("name1", ""),
                    "address": {
                        "unstructuredAddress01": address_parts[0],
                        "unstructuredAddress02": address_parts[1],
                        "unstructuredAddress03": address_parts[2],
                        "street01": contract_party.get("country", ""),
                        "postalCode": contract_party.get("postal_code", ""),
                        "country": {
                            "countryCodeType": "ISO",
                            
                        }
                    }
                }
            except Exception as e:
                pass

        
        dct_notification_party = {}
        if booking.get("partner_email_notifications"):           
            try:
                emails_raw = booking.get("partner_email_notifications") or ""
                email_list = [email.strip() for email in re.split(r'[\n,]+', emails_raw) if email.strip()]
                lst_noficn = []
                for email in email_list:
                    name = email.split('@')[0] if '@' in email else email

                    dct_notifn = {                                           
                                    "contactType": "NotificationContact",
                                    "name": name,
                                    "emails": [
                                        email
                                    ]
                                }
                    lst_noficn.append(dct_notifn)
            except Exception as e:
                pass
    


            dct_notification_party = {
                                    "partyRole": "MessageRecipient",
                                    "partyName1": "MRMR and Address 35 charactersfield",
                                    "contacts": lst_noficn
                                }

        

        final_party_list = []

        for party_dict in [dct_booker, dct_shipper, dct_carrier, dct_notify_party,dct_consignee,dct_contract_party,dct_notification_party ]:
            if party_dict:  
                final_party_list.append(party_dict) 

        general_comments = "Carrier CommentsFree-Form"
        
        payload = {
            "bookingState": "CANCEL",
            "messageDate": {
            "dateFormat": "CCYYMMDDHHMM",
            "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
        },
        "creationDate": {
            "dateFormat": "CCYYMMDDHHMM",
            "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
        },
        "shipmentId": booking.customer_shipment_id,
        "transactionContact": {
            "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
            "contactType": "InformationContact",
            "emails": [
                doc_shipper.email if doc_shipper.email else ""
            ],
            "phones": [
                doc_shipper.phone if doc_shipper.phone else ""
            ]
        },
            "generalComments": general_comments,
            "parties": final_party_list,
        }

        
        url = f"{auth_response.get('url')}/booking/{booking.inttra_reference}/cancel"

    
        status_response = requests.post(url, json=payload, headers=headers)
        response_data = status_response.json()

        if status_response.status_code == 200:
            
            booking_status = response_data['bookingDetails'][len(response_data['bookingDetails'])-1]['payload']['bookingState']

            booking.db_set('booking_status', booking_status)
            frappe.db.commit()

            api_log = frappe.get_doc({
                'doctype': 'Inttra API Logs',
                'description': 'Booking Status Check',
                'booking_id': booking_id,
                'inttra_refference_id': booking.inttra_reference,
                'booking_status': booking_status,
                'response': json.dumps(response_data) if response_data else ""
            })
            api_log.insert(ignore_permissions=True)
            frappe.db.commit()
            frappe.local.response["status_code"] = 200
            frappe.local.response["message"] = f"Booking {booking_status} successfully"

            frappe.msgprint(f"Booking Status updated to: {booking_status}")
            return {
                "status_code": status_response.status_code,
                "message": status_response.text
            }
        else:            
            frappe.local.response["status_code"]= status_response.status_code
            frappe.local.response["message"] = response_data.get('message', 'Failed to fetch booking status.')
            return {
                "status_code": status_response.status_code,
                "message": status_response.text
            }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Status Check Error")
        frappe.throw(f"An error occurred: {str(e)}")
        return {
            
        }
    
@frappe.whitelist()
def booking_client_side_scripting(booking_id=None,type=None):   
    """
    Amend booking request on INTTRA by inttraReferenceId.
    """
    try:
        if not booking_id:
            frappe.throw("Booking ID not provided")

        booking = frappe.get_doc("Booking Request", booking_id)

        if type == "New":
            response = send_booking_to_inttra(booking)  
            if response.get("status_code") != 200:
                frappe.throw(f"Failed to amend booking: {response.get('message', 'Unknown error')}")
            
            return {
                "status": "Amendment request sent"
            }

        if type == "Amend":
            response = amend_booking(booking)  
            if response.get("status_code") != 200:
                frappe.throw(f"Failed to amend booking: {response.get('message', 'Unknown error')}")
            
            return {
                "status": "Amendment request sent"
            }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Amend Booking Error")
        frappe.throw(str(e))




@frappe.whitelist()
def amend_booking(booking):   
    """
    Amend booking request on INTTRA by inttraReferenceId.
    """

    try:
       
        if not booking:            
            return {
                "status_code": 400,
                "message": "Booking not found or invalid booking data provided."
            }
        
        auth_response = get_inttra_credentials()

        if auth_response.get("status_code") != 200:
            frappe.throw("Failed to get INTTRA credentials")
        
        token = auth_response.get("access_token") or ""
        url = f"{auth_response.get('url')}/booking/request/"

        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        
        if not booking.inttra_reference:
            booking_response = send_booking_to_inttra(booking)
            if booking_response.get("status_code") != 200:
                return booking_response
            else:
                return booking_response
        doc_carrier = frappe.get_value("Carrier", booking.booking_agent, ["partyalias", "partyname1","inttra_id"], as_dict=True)
        if not doc_carrier:
            return {
                "status_code": 400,
                "message": "Carrier not found. Please check the Carrier...!!"
            }
        doc_shipper = frappe.get_doc("Shipper", booking.shipper)
        if not doc_shipper:
            return {
                "status_code": 400,
                "message": "Shipper not found. Please check the Shipper...!!"
            }

        doc_unlocode = frappe.get_all(
        "UNLOCODE Locations",
        filters={"name": ["in", [booking.place_of_carrier_receipt, booking.place_of_carrier_delivery]]},
        fields=["name", "locode", "country_code","country","location_name"]
        )
        unlocode_map = {item['name']: item for item in doc_unlocode}

        place_of_reciept = unlocode_map.get(booking.place_of_carrier_receipt)
        place_of_delivery = unlocode_map.get(booking.place_of_carrier_delivery)
        

        earliest_departure_date = ""
        latest_delivery_date = ""
        
        lst_transaction_locations = []

        dct_place_of_receipt = {
                        "locationType": "PlaceOfReceipt",
                        "identifierType": "UNLOC",
                        "identifierValue": place_of_reciept.get("locode") if place_of_reciept.get("locode") else "",
                        "city": place_of_reciept.get("location_name") if place_of_reciept.get("location_name") else "",
                        "subdivision":"",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": place_of_reciept.get("country_code") if place_of_reciept.get("country_code") else "",
                            "countryName": place_of_reciept.get("country") if place_of_reciept.get("country") else ""
                        }
                    }
        if booking.earliest_departure_date:
            if isinstance(booking.earliest_departure_date, datetime.date):
                earliest_departure_date = booking.earliest_departure_date.strftime("%Y%m%d")
            else:
                date_obj = datetime.datetime.strptime(booking.earliest_departure_date, "%Y-%m-%d")
                earliest_departure_date = date_obj.strftime("%Y%m%d")
                dct_place_of_receipt["locationDates"] = [
                    {
                        "type": "EarliestDepartureDate",
                        "dateFormat": "CCYYMMDD",
                        "dateValue": earliest_departure_date
                    }
                ]

        
        lst_transaction_locations.append(dct_place_of_receipt)

        dct_place_of_delivery = {
                        "locationType": "PlaceOfDelivery",
                        "identifierType": "UNLOC",
                        "identifierValue": place_of_delivery.get("locode") if place_of_delivery.get("locode") else "",
                        "city": place_of_delivery.get("location_name") if place_of_delivery.get("location_name") else "",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": place_of_delivery.get("country_code") if place_of_delivery.get("country_code") else "",
                            "countryName": place_of_delivery.get("country") if place_of_delivery.get("country") else ""
                        }
                    }
        
        if booking.latest_delivery_date:
            if isinstance(booking.latest_delivery_date, datetime.date):
                latest_delivery_date = booking.latest_delivery_date.strftime("%Y%m%d")
            else:
                date_obj = datetime.datetime.strptime(booking.latest_delivery_date, "%Y-%m-%d")
                latest_delivery_date = date_obj.strftime("%Y%m%d")

                dct_place_of_delivery["locationDates"] = [
                                        {
                                            "type": "LatestDeliveryDate",
                                            "dateFormat": "CCYYMMDD",
                                            "dateValue": latest_delivery_date
                                        }
                                    ]
        

        lst_transaction_locations.append(dct_place_of_delivery)


        main_carriages = frappe.get_all(
        "Booking Main Carriage",
        filters={"parent": booking.name},
        fields=["port_of_load", "port_of_discharge", "eta","etd","vessel","voyage"]
        )
        lst_transportLegs = []
        for data in main_carriages:
            
            doc_unlocode_main = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.port_of_load, data.port_of_discharge]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_unlocode_main}

            port_of_load = unlocode_map_main.get(data.port_of_load)
            port_of_discharge = unlocode_map_main.get(data.port_of_discharge)
            date_eta = ""
            date_etd = ""
            try:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:  
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
            except:
                date_eta = ""
            try:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")
            except:
                date_etd = ""
           
            dct_item = {
                        "stage": "MainCarriage", 
                        "vesselName": data.vessel,
                        "conveyanceNumber":data.voyage,
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        },
                    }
            
            
            lst_transportLegs.append(dct_item)
        


        for data in booking.add_pre_carriage if booking.add_pre_carriage else []:
           
            doc_pre_carriage = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.start, data.end]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_pre_carriage}

            port_of_load = unlocode_map_main.get(data.start)
            port_of_discharge = unlocode_map_main.get(data.end)
            date_eta = ""
            date_etd = ""
            try:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
            except:
                date_eta = ""
            try:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")
            except:
                date_etd = ""
            
            


            dct_item = {
                        "stage": "PreCarriage", 
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        },
                    }
            
            
            lst_transportLegs.append(dct_item)


        for data in booking.add_on_carriage if booking.add_on_carriage else []:
           
            doc_pre_carriage = frappe.get_all(
            "UNLOCODE Locations",
            filters={"name": ["in", [data.start, data.end]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )

            unlocode_map_main = {item['name']: item for item in doc_pre_carriage}

            port_of_load = unlocode_map_main.get(data.start)
            port_of_discharge = unlocode_map_main.get(data.end)
            date_eta = ""
            date_etd = ""
            try:
                if isinstance(data.eta, datetime.date):
                    date_obj_eta = data.eta
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
                else:
                    date_obj_eta = datetime.datetime.strptime(str(data.eta), "%Y-%m-%d")
                    date_eta = date_obj_eta.strftime("%Y%m%d%H%M")
            except:
                date_eta = ""
            try:
                if isinstance(data.etd, datetime.date):
                    date_obj_etd = data.etd
                    date_etd = date_obj_etd.strftime("%Y%m%d")
                else:
                    date_obj_etd = datetime.datetime.strptime(str(data.etd), "%Y-%m-%d")
                    date_etd = date_obj_etd.strftime("%Y%m%d")
            except:
                date_etd = ""

           


            dct_item = {
                        "stage": "OnCarriage", 
                        "startLocation": {
                            "locationType": "PlaceOfLoad",
                            "locationDates": [
                                {
                                    "dateValue":date_etd,
                                    "dateFormat": "CCYYMMDD",
                                    "type": "EstimatedDepartureDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_load.get("locode") if port_of_load.get("locode") else "",
                            "city": port_of_load.get("location_name") if port_of_load.get("location_name") else "",
                            "subdivision": "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_load.get("country_code") if port_of_load.get("country_code") else "",
                                "countryName": port_of_load.get("country") if port_of_load.get("country") else ""
                            }
                        },
                        "endLocation": {
                            "locationType": "PlaceOfDischarge",
                            "locationDates": [
                                {
                                    "dateValue": date_eta,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "type": "EstimatedArrivalDate"
                                }
                            ],
                            "identifierType": "UNLOC",
                            "identifierValue": port_of_discharge.get("locode") if port_of_discharge.get("locode") else "",
                            "city": port_of_discharge.get("location_name") if port_of_discharge.get("location_name") else "",
                            "country": {
                                "countryCodeType": "ISO",
                                "countryCodeValue": port_of_discharge.get("country_code") if port_of_discharge.get("country_code") else "",
                                "countryName": port_of_discharge.get("country") if port_of_discharge.get("country") else ""
                            }
                        },
                    }
            
            
            lst_transportLegs.append(dct_item)
        
        
        lst_packing = []
        lineNumber = 0
        for cargo in booking.cargo:
            lineNumber += 1
            dct_packing = {
                "lineNumber": lineNumber,
                "packageType": "OUTER",
                "goodsDescription": cargo.get("cargo_description", ""),
                "goodsGrossWeight": {
                    "weightType": "KGM",  
                    "weightValue": cargo.get("cargo_gross_weight", "")  
                },
                "goodsNetWeight": {
                    "weightType": "KGM",  
                    "weightValue": cargo.get("net_weight", "")
                },
                "count": cargo.get("package_count", "1"),  
                "typeCode": cargo.get("package_type_code", "EDI"),  
                "typeValue": cargo.get("package_type", "HC"),  
                "typeDescription": "",
                "goodsClassificationType": "WCO",
                "goodsClassificationValue": cargo.get("hs_code", "123333")  
            }
            lst_packing.append(dct_packing)

        lst_equipments = []
        lst_haulge = []
        for data in booking.containers:
            doc_container = frappe.get_value(
                "Container Type",
                {"name": str(data.container_quantitytype)},
                ["name", "typecode", "groupcode", "typecategory", "shortdescription"],
                as_dict=True
            )
            
            lst_haulge = []
            if booking.move_type == 'Port, Ramp, or CY to Port, Ramp, or CY':
                str_move_type = 'PortToPort'
                str_haulage = "MerchantMerchant"
                if data.haulage_detail if data.haulage_detail else []:
                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail
                    
                    RequestedEmptyPickUpDate = ""
                    if haulage_detail.get("emptyPickUp") and haulage_detail.get("emptyPickUp").get("emptyPickupDate") and haulage_detail.get("emptyPickUp").get("emptyPickUpTime"):

                        RequestedEmptyPickUpDate  = haulage_detail.get("emptyPickUp").get("emptyPickupDate") + " " + haulage_detail.get("emptyPickUp").get("emptyPickUpTime")
                        if isinstance(RequestedEmptyPickUpDate, datetime.date):
                            dt_obj = RequestedEmptyPickUpDate
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedEmptyPickUpDate, "%Y-%m-%d %H:%M")
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                        
           
                    

                    lst_haulge = [{
                        "haulageParty": {
                        "partyName1": haulage_detail.get("emptyPickUp").get("companyName"),
                            "address": {
                                "unstructuredAddress01": haulage_detail.get("emptyPickUp").get("address")[0:35],
                                "unstructuredAddress02": haulage_detail.get("emptyPickUp").get("address")[35:70],
                                "unstructuredAddress03": haulage_detail.get("emptyPickUp").get("address")[70:105],
                                "street01": "",
                                "postalCode": "",
                                "country": {
                                    "countryCodeType": "ISO",
                                }
                            },
                            "partyRole": "EmptyPickUp"
                            },
                            "dates": [
                                {
                                    "dateValue": RequestedEmptyPickUpDate,
                                    "dateFormat": "CCYYMMDDHHMM",
                                    "haulageDateType": "RequestedEmptyPickUpDate"
                                }
                            ],
                            "contacts": [
                                {
                                    "name": haulage_detail.get("emptyPickUp").get("contactName"),
                                    "phones": [
                                        haulage_detail.get("emptyPickUp").get("contactNumber")
                                    ]
                                }
                            ] 
                    }]
            elif booking.move_type == 'Door to Port, Ramp, or CY':
                str_move_type = "DoorToPort"
                str_haulage = "CarrierMerchant"

                if data.haulage_detail if data.haulage_detail else []:

                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail
                    
                    empty_positioning_date_time = ""
                    full_pickUp_date_time = ""

                    if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("emptyPositioningDate") and haulage_detail.get("shipFrom").get("emptyPositioningTime"):
                        empty_positioning_date_time  = haulage_detail.get("shipFrom").get("emptyPositioningDate") + " " + haulage_detail.get("shipFrom").get("emptyPositioningTime")
                        if isinstance(empty_positioning_date_time, datetime.date):
                            dt_obj = empty_positioning_date_time
                            empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(empty_positioning_date_time, "%Y-%m-%d %H:%M")
                            empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")

                    if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("fullPickupDate") and haulage_detail.get("shipFrom").get("fullPickupTime"):
                        full_pickUp_date_time = haulage_detail.get("shipFrom").get("fullPickupDate") + " " + haulage_detail.get("shipFrom").get("fullPickupTime")
                        if isinstance(full_pickUp_date_time, datetime.date):
                            dt_obj = full_pickUp_date_time
                            full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(full_pickUp_date_time, "%Y-%m-%d %H:%M")
                            full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M") 

                    
                   
                    lst_haulge =  [{
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipFrom").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipFrom").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipFrom").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipFrom").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipFrom"
                                },
                                "dates": [
                                    {
                                        "dateValue": empty_positioning_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "EmptyPositioningDate"
                                    },
                                    {
                                        "dateValue": full_pickUp_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "FullPickUpDateTime"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipFrom").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipFrom").get("contactNumber")
                                        ]
                                    }
                                ] 
                            }]
                    
                

            elif booking.move_type == 'Door to Door':
                    str_move_type = "DoorToDoor"
                    str_haulage = "CarrierCarrier"
                    if data.haulage_detail if data.haulage_detail else []:

                        if type(data.haulage_detail) == str:
                            haulage_detail = json.loads(data.haulage_detail)
                        else:
                            haulage_detail = data.haulage_detail

                        empty_positioning_date_time = ""
                        full_pickUp_date_time = ""
                        RequestedDoorDeliveryDate = ""
                        if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("emptyPositioningDate") and haulage_detail.get("shipFrom").get("emptyPositioningTime"):
                            empty_positioning_date_time  = haulage_detail.get("shipFrom").get("emptyPositioningDate") + " " + haulage_detail.get("shipFrom").get("emptyPositioningTime")
                            if isinstance(empty_positioning_date_time, datetime.date):
                                dt_obj = empty_positioning_date_time
                                empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(empty_positioning_date_time, "%Y-%m-%d %H:%M")
                                empty_positioning_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        if haulage_detail.get("shipFrom") and haulage_detail.get("shipFrom").get("fullPickupDate") and haulage_detail.get("shipFrom").get("fullPickupTime"):
                            full_pickUp_date_time = haulage_detail.get("shipFrom").get("fullPickupDate") + " " + haulage_detail.get("shipFrom").get("fullPickupTime")
                            if isinstance(full_pickUp_date_time, datetime.date):
                                dt_obj = full_pickUp_date_time
                                full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(full_pickUp_date_time, "%Y-%m-%d %H:%M")
                                full_pickUp_date_time = dt_obj.strftime("%Y%m%d%H%M")
                        if haulage_detail.get("shipTo") and haulage_detail.get("shipTo").get("deliveryDate") and haulage_detail.get("shipTo").get("deliveryTime"):
                            RequestedDoorDeliveryDate = haulage_detail.get("shipTo").get("deliveryDate") +" " + haulage_detail.get("shipTo").get("deliveryTime")
                            if isinstance(RequestedDoorDeliveryDate, datetime.date):
                                dt_obj = RequestedDoorDeliveryDate
                                RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")
                            else:
                                dt_obj = datetime.datetime.strptime(RequestedDoorDeliveryDate, "%Y-%m-%d %H:%M")
                                RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")

                        
                        lst_haulge = [
                            {
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipFrom").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipFrom").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipFrom").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipFrom").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipFrom"
                                },
                                "dates": [
                                    {
                                        "dateValue": empty_positioning_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "EmptyPositioningDate"
                                    },
                                    {
                                        "dateValue": full_pickUp_date_time,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "FullPickUpDateTime"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipFrom").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipFrom").get("contactNumber")
                                        ]
                                    }
                                ] 
                            },
                            {
                                "haulageParty": {
                                "partyName1": haulage_detail.get("shipTo").get("companyName"),
                                "address": {
                                    "unstructuredAddress01": haulage_detail.get("shipTo").get("address")[0:35],
                                    "unstructuredAddress02": haulage_detail.get("shipTo").get("address")[35:70],
                                    "unstructuredAddress03": haulage_detail.get("shipTo").get("address")[70:105],
                                    "street01": "",
                                    "postalCode": "",
                                    "country": {
                                        "countryCodeType": "ISO",
                                    }
                                },
                                "partyRole": "ShipTo"
                                },
                                "dates": [
                                    {
                                        "dateValue": RequestedDoorDeliveryDate,
                                        "dateFormat": "CCYYMMDDHHMM",
                                        "haulageDateType": "RequestedDoorDeliveryDate"
                                    }
                                ],
                                "contacts": [
                                    {
                                        "name": haulage_detail.get("shipTo").get("contactName"),
                                        "phones": [
                                            haulage_detail.get("shipTo").get("contactNumber")
                                        ]
                                    }
                                ] 
                            }
                        ]


            elif booking.move_type == 'Port, Ramp, or CY to Door':
                str_move_type = "PortToDoor"
                str_haulage = "MerchantCarrier"
                
                if data.haulage_detail if data.haulage_detail else []:

                    if type(data.haulage_detail) == str:
                        haulage_detail = json.loads(data.haulage_detail)
                    else:
                        haulage_detail = data.haulage_detail

                    RequestedEmptyPickUpDate = ""
                    RequestedDoorDeliveryDate = ""
                    if haulage_detail.get("emptyPickUp") and haulage_detail.get("emptyPickUp").get("emptyPickupDate") and haulage_detail.get("emptyPickUp").get("emptyPickUpTime"):
                        RequestedEmptyPickUpDate  = haulage_detail.get("emptyPickUp").get("emptyPickupDate") + " " + haulage_detail.get("emptyPickUp").get("emptyPickUpTime")
                        if isinstance(RequestedEmptyPickUpDate, datetime.date):
                            dt_obj = RequestedEmptyPickUpDate
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedEmptyPickUpDate, "%Y-%m-%d %H:%M")
                            RequestedEmptyPickUpDate = dt_obj.strftime("%Y%m%d%H%M")
                    if haulage_detail.get("shipTo") and haulage_detail.get("shipTo").get("deliveryDate") and haulage_detail.get("shipTo").get("deliveryTime"):
                        RequestedDoorDeliveryDate = haulage_detail.get("shipTo").get("deliveryDate") +" " + haulage_detail.get("shipTo").get("deliveryTime")
                        if isinstance(RequestedDoorDeliveryDate, datetime.date):
                            dt_obj = RequestedDoorDeliveryDate
                            RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")
                        else:
                            dt_obj = datetime.datetime.strptime(RequestedDoorDeliveryDate, "%Y-%m-%d %H:%M")
                            RequestedDoorDeliveryDate = dt_obj.strftime("%Y%m%d%H%M")
                    

                lst_haulge = [
                    {
                    "haulageParty": {
                        "partyName1": haulage_detail.get("emptyPickUp").get("companyName"),
                        "address": {
                            "unstructuredAddress01": haulage_detail.get("emptyPickUp").get("address")[0:35],
                            "unstructuredAddress02": haulage_detail.get("emptyPickUp").get("address")[35:70],
                            "unstructuredAddress03": haulage_detail.get("emptyPickUp").get("address")[70:105],
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                            }
                        },
                        "partyRole": "EmptyPickUp"
                        },
                        "dates": [
                            {
                                "dateValue": RequestedEmptyPickUpDate,
                                "dateFormat": "CCYYMMDDHHMM",
                                "haulageDateType": "RequestedEmptyPickUpDate"
                            }
                        ],
                        "contacts": [
                            {
                                "name": haulage_detail.get("emptyPickUp").get("contactName"),
                                "phones": [
                                    haulage_detail.get("emptyPickUp").get("contactNumber")
                                ]
                            }
                        ] 
                    },
                    {
                        "haulageParty": {
                        "partyName1": haulage_detail.get("shipTo").get("companyName"),
                        "address": {
                            "unstructuredAddress01": haulage_detail.get("shipTo").get("address")[0:35],
                            "unstructuredAddress02": haulage_detail.get("shipTo").get("address")[35:70],
                            "unstructuredAddress03": haulage_detail.get("shipTo").get("address")[70:105],
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                            }
                        },
                        "partyRole": "ShipTo"
                        },
                        "dates": [
                            {
                                "dateValue": RequestedDoorDeliveryDate,
                                "dateFormat": "CCYYMMDDHHMM",
                                "haulageDateType": "RequestedDoorDeliveryDate"
                            }
                        ],
                        "contacts": [
                            {
                                "name": haulage_detail.get("shipTo").get("contactName"),
                                "phones": [
                                    haulage_detail.get("shipTo").get("contactNumber")
                                ]
                            }
                        ] 
                    }
                ]

            dct_equipments = {
                            "comments": [data.container_comments],
                            "count": data.number_of_containers,
                            "serviceType": "FCLFCL",
                            "equipmentSizeCode": {
                                "sizeCodeType": "ISO",
                                "sizeCodeValue": doc_container.typecode,
                                "sizeCodeDescription": doc_container.shortdescription
                            },
                            
                            
                            "nonActiveReefer": False
                        }
            
            if str_haulage or lst_haulge:
                dct_equipments["haulage"] = {}
                
                if str_haulage:
                    dct_equipments["haulage"]["arrangement"] = str_haulage

                if lst_haulge:
                    dct_equipments["haulage"]["points"] = lst_haulge

            lst_equipments.append(dct_equipments)
        
        dct_pay = {}
        dct_payement_enum = {"Additional Charges":"Additional", "Basic Freight":"OceanFreight", "Destination Haulage Charges":"DestinationHaulage", "Destination Port Charges":"DestinationTerminalHandling", "Origin Port Charges":"OriginTerminalHandling", "Origin Haulage Charges":"OriginHaulage"}
        dct_pay_terms = {"Pre-paid","Collect","Payable Elsewhere"}
        
        for pay in booking.payment if booking.payment else []:
            payment_location = pay.get('payment_location')
            doc_payment_location = {}
            if payment_location:
                doc_payment_location = frappe.get_all(
                    "UNLOCODE Locations",
                    filters={"name": payment_location},
                    fields=["name", "locode", "country_code", "country", "location_name", "sub_division"]
                )
                doc_payment_location = doc_payment_location[0] if doc_payment_location else {}

            lst_payment = [
                {
                    "chargeType": dct_payement_enum.get(pay.get('charge_type')),
                    "paymentTerm":"Prepaid",
                    "chargeLocation": {
                        "identifierType": "UNLOC",
                        "identifierValue": doc_payment_location.get("locode", ""),
                        "city": doc_payment_location.get("location_name", ""),
                        "subdivision": doc_payment_location.get("sub_division", ""),
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": doc_payment_location.get("country_code", "")
                        },
                        "locationType": "PlaceOfPayment"
                    }
                }
            ]

            dct_pay[pay.get('payer')] = lst_payment

        
            
        dct_booker = {
                    "partyRole": "Booker",
                    "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                    "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                    "partyName1": doc_shipper.shipper_name[:35] if doc_shipper.shipper_name else "",
                    "contacts": [
                        {
                            "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                            "contactType": "InformationContact",
                            "phones": [
                                doc_shipper.phone if doc_shipper.phone else ""
                            ],
                            "emails": [
                                doc_shipper.email if doc_shipper.email else ""
                            ]
                        }
                    ]    
                }
        charges = dct_pay.get("Booker")
        if charges:
            dct_booker["charges"] = charges
        
        dct_shipper = {
                        "partyRole": "Shipper",
                        "partyINTTRACompanyId": doc_shipper.inttra_company_id if doc_shipper.inttra_company_id else "",
                        "partyAlias": doc_shipper.shipper_code if doc_shipper.shipper_code else "",
                        "partyName1": doc_shipper.shipper_name[:35] if doc_shipper.shipper_name else "",
                        "contacts": [
                            {
                                "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                "contactType": "InformationContact",
                                "phones": [
                                    doc_shipper.phone if doc_shipper.phone else ""
                                ],
                                "emails": [
                                    doc_shipper.email if doc_shipper.email else ""
                                ]
                            }
                        ]
                    }
        charges = dct_pay.get("Shipper")
        if charges:
            dct_shipper["charges"] = charges
        
        dct_carrier = {
                        "partyRole": "Carrier",
                        "partyAlias": doc_carrier.partyalias[:35] if doc_carrier.partyalias else "",
                        "partyName1": doc_carrier.partyname1[:35] if doc_carrier.partyname1 else "",
                        "partyINTTRACompanyId": doc_carrier.inttra_id if doc_carrier.partyname1 else "",
                        "contacts": [
                            {
                                "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                                "contactType": "InformationContact",
                                "phones": [
                                    doc_shipper.phone if doc_shipper.phone else ""
                                ],
                                "emails": [
                                    doc_shipper.email if doc_shipper.email else ""
                                ]
                            }
                        ]
                    }
        charges = dct_pay.get("Carrier")
        if charges:
            dct_carrier["charges"] = charges
        
        dct_notify_party= {}
        if booking.get("notify_parties"):
            notify_party_doc = frappe.get_doc("Customer DB", booking.notify_parties)
            address = notify_party_doc.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party = {
                "partyRole": "MainNotifyParty",
                "partyName1": notify_party_doc.get("customer_name", "")[:35] if notify_party_doc.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_doc.get("country", ""),
                    "postalCode": notify_party_doc.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking.get("notify_party_on_booking"):
            notify_party_on_booking = booking.get("notify_party_on_booking")
            dct_notify_party = {
                "partyRole": "MainNotifyParty",
                "partyName1": notify_party_on_booking[:35] if notify_party_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_on_booking[0:35] if notify_party_on_booking else "",
                    "unstructuredAddress02": notify_party_on_booking[35:70] if notify_party_on_booking else "",
                    "unstructuredAddress03": notify_party_on_booking[70:105] if notify_party_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }

        dct_notify_party_one= {}
        if booking.get("notify_party_1"):
            notify_party_1 = frappe.get_doc("Customer DB", booking.notify_party_1)
            address = notify_party_1.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party_one = {
                "partyRole": "FirstAdditionalNotifyParty",
                "partyName1": notify_party_1.get("customer_name", "")[:35] if notify_party_1.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_1.get("country", ""),
                    "postalCode": notify_party_1.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking.get("notify_party_1_on_booking"):
            notify_party_1_on_booking = booking.get("notify_party_1_on_booking")
            dct_notify_party_one = {
                "partyRole": "FirstAdditionalNotifyParty",
                "partyName1": notify_party_1_on_booking[:35] if notify_party_1_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_1_on_booking[0:35] if notify_party_1_on_booking else "",
                    "unstructuredAddress02": notify_party_1_on_booking[35:70] if notify_party_1_on_booking else "",
                    "unstructuredAddress03": notify_party_1_on_booking[70:105] if notify_party_1_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }


        dct_notify_party_two= {}
        if booking.get("notify_party_2"):
            notify_party_2 = frappe.get_doc("Customer DB", booking.notify_party_2)
            address = notify_party_2.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_notify_party_two = {
                "partyRole": "SecondAdditionalNotifyParty",
                "partyName1": notify_party_2.get("customer_name", "")[:35] if notify_party_2.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": notify_party_2.get("country", ""),
                    "postalCode": notify_party_2.get("postal_code", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking.get("notify_party_2_on_booking"):
            notify_party_2_on_booking = booking.get("notify_party_2_on_booking")
            dct_notify_party_two = {
                "partyRole": "SecondAdditionalNotifyParty",
                "partyName1": notify_party_2_on_booking[:35] if notify_party_2_on_booking else "",
                "address": {
                    "unstructuredAddress01": notify_party_2_on_booking[0:35] if notify_party_2_on_booking else "",
                    "unstructuredAddress02": notify_party_2_on_booking[35:70] if notify_party_2_on_booking else "",
                    "unstructuredAddress03": notify_party_2_on_booking[70:105] if notify_party_2_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_consignee = {}
        if booking.get("consignee"):
            consignee_doc = frappe.get_doc("Customer DB", booking.consignee)
            address = consignee_doc.customer_address or ""

            if address:
                address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                address_parts = (address_parts + [""] * 3)[:3]
            else:
                address_parts = ["", "", ""]

            dct_consignee = {
                "partyRole": "Consignee",
                "partyName1": consignee_doc.get("customer_name", "")[:35] if consignee_doc.get("customer_name", "") else "",
                "address": {
                    "unstructuredAddress01": address_parts[0],
                    "unstructuredAddress02": address_parts[1],
                    "unstructuredAddress03": address_parts[2],
                    "street01": consignee_doc.get("country", ""),
                    "postalCode": consignee_doc.get("customer_zip", ""),
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        elif booking.get("consignee_on_booking"):
            consignee_on_booking = booking.get("consignee_on_booking")
            dct_consignee = {
                "partyRole": "Consignee",
                "partyName1": consignee_on_booking[:35] if consignee_on_booking else "",
                "address": {
                    "unstructuredAddress01": consignee_on_booking[0:35] if consignee_on_booking else "",
                    "unstructuredAddress02": consignee_on_booking[35:70] if consignee_on_booking else "",
                    "unstructuredAddress03": consignee_on_booking[70:105] if consignee_on_booking else "",
                    "street01": "",
                    "postalCode": "",
                    "country": {
                        "countryCodeType": "ISO",
                        
                    }
                }
            }
        
        dct_contract_party = {}
        if booking.get("contract_party"):
            try:
                contract_party = frappe.get_doc("Shipper", booking.contract_party)
                if contract_party:
                    
                    dct_contract_party = {
                        "partyRole": "ContractParty",
                        "partyName1": contract_party.shipper_name[:35] if contract_party.shipper_name else "",
                        "address": {
                            "unstructuredAddress01": contract_party.custom_address[0:35] if contract_party.custom_address else "",
                            "unstructuredAddress02": contract_party.custom_address[35:70] if contract_party.custom_address else "",
                            "unstructuredAddress03": contract_party.custom_address[70:105] if contract_party.custom_address else "",
                            "street01": "",
                            "postalCode": "",
                            "country": {
                                "countryCodeType": "ISO",
                                
                            }
                        }
                    } 
                else:
                    contract_party = frappe.get_doc("Customer DB", booking.contract_party)
                    address = contract_party.customer_address or ""
                    if address:
                        address_parts = [address[i:i+35] for i in range(0, len(address), 35)]
                        address_parts = (address_parts + [""] * 3)[:3]
                    else:
                        address_parts = ["", "", ""]

                    dct_contract_party = {
                        "partyRole": "ContractParty",
                        "partyName1": contract_party.get("customer_name", "")[:35] if contract_party.get("customer_name", "") else "",
                        "address": {
                            "unstructuredAddress01": address_parts[0],
                            "unstructuredAddress02": address_parts[1],
                            "unstructuredAddress03": address_parts[2],
                            "street01": contract_party.get("country", ""),
                            "postalCode": contract_party.get("customer_zip", ""),
                            "country": {
                                "countryCodeType": "ISO",
                                
                            }
                        }
                    }
            except Exception as e:
                pass
        elif booking.get("contract_party_on_booking"):
            try:
                contract_party_on_booking = booking.get("contract_party_on_booking")
                dct_contract_party = {
                    "partyRole": "ContractParty",
                    "partyName1": contract_party_on_booking[:35] if contract_party_on_booking else "",
                    "address": {
                        "unstructuredAddress01": contract_party_on_booking[0:35] if contract_party_on_booking else "",
                        "unstructuredAddress02": contract_party_on_booking[35:70] if contract_party_on_booking else "",
                        "unstructuredAddress03": contract_party_on_booking[70:105] if contract_party_on_booking else "",
                        "street01": "",
                        "postalCode": "",
                        "country": {
                            "countryCodeType": "ISO",
                            
                        }
                    }
                }
            except Exception as e:
                pass

        dct_notification_party = {} 
        if booking.get("partner_email_notifications"):           

            emails_raw = booking.get("partner_email_notifications") or ""
            email_list = [email.strip() for email in re.split(r'[\n,]+', emails_raw) if email.strip()]
            lst_noficn = []
            for email in email_list:
                name = email.split('@')[0] if '@' in email else email

                dct_notifn = {                                           
                                "contactType": "NotificationContact",
                                "name": name,
                                "emails": [
                                    email
                                ]
                            }
                lst_noficn.append(dct_notifn)
    


            dct_notification_party = {
                                    "partyRole": "MessageRecipient",
                                    "partyName1": "Westside Exports LLC",
                                    "contacts": lst_noficn
                                }
       

        final_party_list = []

        for party_dict in [dct_booker, dct_shipper, dct_carrier, dct_notify_party,dct_consignee,dct_contract_party,dct_notification_party,dct_notify_party_one,dct_notify_party_two ]:
            if party_dict:  
                final_party_list.append(party_dict)  

        if booking.booking_office:
            booking_office =  frappe.get_all(
                            "UNLOCODE Locations",
                            filters={"name": ["in", [booking.booking_office]]},
                            fields=["name", "locode", "country_code","country","location_name"]
                        )
            if booking_office:
                booking_office = booking_office[0]
                dct_booking_office = {
                        "locationType": "BookingOffice",
                        "identifierType": "UNLOC",
                        "identifierValue": booking_office.get("locode") if booking_office.get("locode") else "",
                        "city": booking_office.get("location_name") if booking_office.get("location_name") else "",
                        "subdivision": booking_office.get("sub_division") if booking_office.get("sub_division") else "",
                        "country": {
                            "countryCodeType": "ISO",
                            "countryCodeValue": booking_office.get("country_code") if booking_office.get("country_code") else "",
                            "countryName": booking_office.get("country") if booking_office.get("country") else ""
                        }
                    }     
                lst_transaction_locations.append(dct_booking_office)     
            
        
        lst_reference = []
        if booking.carrier_booking_number:
            dct_ref = {
            "referenceType": "BookingNumber",
            "referenceValue": booking.carrier_booking_number
            }
            lst_reference.append(dct_ref)
        if booking.contract_number:
            dct_ref = {
            "referenceType": "ContractNumber",
            "referenceValue": booking.contract_number
            }
            lst_reference.append(dct_ref)
        if booking.tariff_number:
            dct_ref =    {
                "referenceType": "FreightTariffNumber",
                "referenceValue": booking.tariff_number
                }
            lst_reference.append(dct_ref)  

        if booking.shippers_reference_numbers:
            dct_ref =    {
                "referenceType": "ShipperReferenceNumber",
                "referenceValue": booking.shippers_reference_numbers
                }
            lst_reference.append(dct_ref)    
        if booking.forwarders_reference_numbers:
            dct_ref =    {
                "referenceType": "FreightForwarderRefNumber",
                "referenceValue": booking.forwarders_reference_numbers
                }
            lst_reference.append(dct_ref)    
        if booking.bl_reference_numbers:
            dct_ref =    {
                "referenceType": "BillOfLadingNumber",
                "referenceValue": booking.bl_reference_numbers
                }
            lst_reference.append(dct_ref)      

        if booking.purchase_order_numbers:
            dct_ref =    {
                "referenceType": "PurchaseorderNumber",
                "referenceValue": booking.purchase_order_numbers
                }
            lst_reference.append(dct_ref)    
        
        
        if booking.contract_party_reference_numbers:
            dct_ref =    {
                "referenceType": "ContractPartyReferenceNumber",
                "referenceValue": booking.contract_party_reference_numbers
                }
            lst_reference.append(dct_ref) 

        if booking.consignees_reference_numbers:
            dct_ref =    {
                "referenceType": "ConsigneeReferenceNumber",
                "referenceValue": booking.consignees_reference_numbers
                }
            lst_reference.append(dct_ref) 

        

        payload = {                    
                "bookingState": "AMEND",
                "moveType": str_move_type,
                "parties": final_party_list,
                "references": lst_reference,
                "messageDate": {
                    "dateFormat": "CCYYMMDDHHMM",
                    "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
                },
                "creationDate": {
                    "dateFormat": "CCYYMMDDHHMM",
                    "dateValue": datetime.datetime.now().strftime("%Y%m%d%H%M")
                },
                "shipmentId": booking.customer_shipment_id,
                "carrierReferenceNumber": booking.carrier_booking_number,
                "transactionContact": {
                    "name": doc_shipper.shipper_name if doc_shipper.shipper_name else "",
                    "contactType": "InformationContact",
                    "emails": [
                        doc_shipper.email if doc_shipper.email else ""
                    ],
                    "phones": [
                        doc_shipper.phone if doc_shipper.phone else ""
                    ]
                },
                "customerChangeSummary": [
                   booking.customer_comments
                ],
                "cargoHazardousIndicator": False,
                "cargoEnvironmentalPollutantIndicator": False,
                "cargoOutofGaugeIndicator": False,
                "notifyMe": True,
                
                "transactionLocations": lst_transaction_locations,
                "transportLegs": lst_transportLegs,
                "packageDetails": lst_packing,

                "equipments": lst_equipments,
                
                "perContainerReleaseFlag": False
            }
        

        print(payload)


        url = f"{auth_response.get('url')}/booking/{booking.inttra_reference}/amend"
    
        status_response = requests.post(url, json=payload, headers=headers)
        response_data = status_response.json()

        if status_response.status_code == 200:
            
            booking_status = response_data['bookingDetails'][len(response_data['bookingDetails'])-1]['payload']['bookingState']

            booking.db_set('booking_status', booking_status)
            frappe.db.commit()

            frappe.get_doc({
                'doctype': 'Inttra API Logs',
                'description': 'Booking Amendment Request',
                'booking_id': booking.name,
                'inttra_refference_id': booking.inttra_reference,
                'booking_status': booking_status,
                'response': json.dumps(response_data) if response_data else "",
                'requested_json': json.dumps(payload) or None
            }).insert(ignore_permissions=True)
            frappe.db.commit()
            frappe.local.response["status_code"] = 200
            frappe.local.response["message"] = f"Booking Status updated to: {booking_status}"
            frappe.msgprint(f"Booking Status updated to: {booking_status}")
            return{
                "status_code": 200,
                "message": f"Booking Status updated to: {booking_status}"
            }
        else:
            frappe.get_doc({
                'doctype': 'Inttra API Logs',
                'description': 'Booking Amendment Request',
                'booking_id': booking.name,
                'inttra_refference_id': booking.inttra_reference,
                'booking_status': "AMEND_FAILED",
                'response': json.dumps(response_data) if response_data else "",
                'requested_json': json.dumps(payload) or None
            }).insert(ignore_permissions=True)
            frappe.db.commit()
            
            frappe.local.response["status_code"]= status_response.status_code
            frappe.local.response["message"] = response_data[0].get('message')
            frappe.msgprint(response_data[0].get('message'))
            return{
                "status_code": status_response.status_code,
                "message": response_data[0].get('message')
            }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Booking Status Check Error")
        frappe.throw(f"An error occurred: {str(e)}")
        return{
            "status_code": 500,
            "message": str(e)
        }





def generate_unique_id():
    now = datetime.datetime.now()
    year = now.strftime('%y')  
    timestamp = now.strftime('%m%d%H%M%S')  
    microseconds = str(now.microsecond)[:3] 
    return f"SHMT{year}{timestamp}{microseconds}"


party_role_to_field = {
    "Consignee": "consignee_on_booking",
    "Forwarder": "forwarder_on_booking",
    "ContractParty": "contract_party_on_booking",
    "NotifyParty": "notify_party_on_booking",
    "CustomsBroker": "customs_broker_on_booking",
    "NotifyParty1": "notify_party_1_on_booking",
    "NotifyParty2": "notify_party_2_on_booking",
    "Shipper": "shipper_on_booking",  
    "FreightPayer": "freight_payer_on_booking", 
}



# Helper function to format party info
def format_party_info(party):
    try:
        lines = [party.get("partyName", "") if isinstance(party, dict) else ""]

        address = party.get("address") if isinstance(party, dict) else {}
        if not isinstance(address, dict):
            address = {}

        for i in range(1, 5):
            line = address.get(f"unstructuredAddress0{i}")
            if line:
                lines.append(line)

        city = address.get("city")
        if city:
            lines.append(city)

        state = address.get("state")
        postal = address.get("postalCode")

        country = address.get("country", {})
        if not isinstance(country, dict):
            country = {}

        country_name = country.get("countryName")

        if state or postal or country_name:
            lines.append(" ".join(filter(None, [state, postal, country_name])))

        return "\n".join(filter(None, lines))

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Error in format_party_info")
        return ""



@frappe.whitelist()
def create_new_booking(response_data,booking_id=None):
    """
    Create split booking based on the response data.
    """
    try:
        temp_data = None
        doc_booking = frappe.new_doc("Booking Request")
        result = {}
        for party in response_data.get('parties',[]):
            print(party)
            fieldname = party.get("partyRole")
            if fieldname:
                result[fieldname] = format_party_info(party)
            
            if party.get('partyRole') == "Shipper":
                shipper = frappe.get_value("Shipper",{"inttra_company_id": party.get('partyINTTRACompanyId')},"name")
                result["shipper"] = shipper

            elif party.get('partyRole') == "Carrier":

                carrier_name = frappe.get_value("Carrier", {"inttra_id": party.get('partyINTTRACompanyId')},"name")
                if not carrier_name:
                    carrier_name = frappe.get_value("Carrier", {"partyalias": party.get('partyAlias')},"name")
                result["carrier_name"] = carrier_name

            elif party.get('partyRole') == "Consignee":
                consignee = frappe.get_value("Customer DB",{"customer_name": party.get('partyName')},"name")
                result["consignee"] = consignee

            elif party.get('partyRole') == "MainNotifyParty":
                main_notify_party = frappe.get_value("Customer DB",{"customer_name": party.get('partyName')},"name")
                result["main_notify_party"] = main_notify_party

            elif party.get('partyRole') == "FirstAdditionalNotifyParty":
                first_notify_party = frappe.get_value("Customer DB",{"customer_name": party.get('partyName')},"name")
                result["first_notify_party"] = first_notify_party

            elif party.get('partyRole') == "SecondAdditionalNotifyParty":
                second_notify_party = frappe.get_value("Customer DB",{"customer_name": party.get('partyName')},"name")
                result["second_notify_party"] = second_notify_party

            elif party.get('partyRole') == "ContractParty": 
                contract_party = frappe.get_value("Contract Party",{"name1": party.get('partyName')},"name")
                result["contract_party"] = contract_party

            elif party.get('partyRole') == "BookingOffice":
                part_address = party.get("address") or {}
                str_address = None
                if isinstance(part_address, dict):
                    str_address = ", ".join(
                        value for key, value in part_address.items() if value
                    )
                contact_info = party.get("contacts") or {}
                str_telephone = ", ".join(contact_info.get("phones", [])) if contact_info.get("phones") else None
                str_email = ", ".join(contact_info.get("emails", [])) if contact_info.get("emails") else None

                doc_booking.append("additional_contacts", {
                    "partner_role": "BookingOffice",
                    "partner_name": party.get("partyName1"),
                    "contact_name": contact_info.get("name"),
                    "telephone": str_telephone[:140] if str_telephone else None,
                    "email": str_email[:140] if str_email else None,
                    "address_line": str_address
                })


        doc_booking.booking_agent = result.get("carrier_name",None)  
        doc_booking.shipper = result.get("shipper",None)
        doc_booking.shipper_on_booking = result.get("Shipper",None)
        doc_booking.consignee = result.get("consignee",None)
        doc_booking.consignee_on_booking = result.get("Consignee",None)
        doc_booking.forwarder = result.get("forwarder",None)
        doc_booking.forwarder_on_booking = result.get("Forwarder",None)
        doc_booking.contract_party = result.get("contract_party",None)
        doc_booking.contract_party_on_booking = result.get("ContractParty",None)
        doc_booking.notify_parties = result.get("main_notify_party",None)
        doc_booking.notify_party_on_booking = result.get("MainNotifyParty",None)
        doc_booking.customs_broker = result.get("customs_broker",None)
        doc_booking.customs_broker_on_booking = result.get("CustomsBroker",None)
        doc_booking.notify_party_1 = result.get("first_notify_party",None)
        doc_booking.notify_party_1_on_booking = result.get("FirstAdditionalNotifyParty",None)
        doc_booking.notify_party_2 = result.get("second_notify_party",None)
        doc_booking.notify_party_2_on_booking = result.get("SecondAdditionalNotifyParty",None)

        doc_booking.carrier_booking_number = response_data.get("carrierReferenceNumber", None)
        
        doc_booking.inttra_reference = response_data.get("inttraReference", None)
        doc_booking.booking_status = response_data.get("bookingState", None)
        
        doc_booking.booking_response_type = response_data.get("bookingResponseType", None)
        doc_booking.cargo_hazardous_indicator = response_data.get("cargoHazardousIndicator", None)
        doc_booking.cargo_controlled_environment_indicator = response_data.get("cargoControlledEnvironmentIndicator", None)
        doc_booking.cargo_environmental_pollutant_indicator = response_data.get("cargoEnvironmentalPollutantIndicator", None)
        doc_booking.cargo_out_of_gauge_indicator = response_data.get("cargoOutofGaugeIndicator", None)
        doc_booking.out_side_booking = True
        for ref in response_data.get("references", []):
            doc_booking.append ("additional_reference",{
                "reference_type": ref.get("referenceType"),
                "text":ref.get("referenceValue")
            }
            )

            if ref.get("referenceType") == "ShipmentID":
                doc_booking.customer_shipment_id = ref.get("referenceValue")

            if ref.get("referenceType") == "ContractPartyReferenceNumber":
                doc_booking.contract_party_reference_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "ConsigneeReferenceNumber":
                doc_booking.consignees_reference_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "ShipmentID":
                doc_booking.additional_refences = ref.get("referenceValue")

            if ref.get("referenceType") == "BillOfLadingNumber":
                doc_booking.bl_reference_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "PurchaseOrderNumber":
                doc_booking.purchase_order_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "FreightForwarderRefNumber":
                doc_booking.forwarders_reference_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "ShipperReferenceNumber":
                doc_booking.shippers_reference_numbers = ref.get("referenceValue")

            if ref.get("referenceType") == "CarrierSourceBookingNumber":
                doc_booking.carrier_source_booking_number = ref.get("referenceValue")
                doc_booking.parent_carrier_booking_number = ref.get("referenceValue")
            
            if ref.get("referenceType") == "ContractNumber":
                doc_booking.contract_number = ref.get("referenceValue")
        

        for package in response_data.get("packageDetails") or []:
            doc_booking.append("cargo", {
                "container_number": "",  
                "package_counttype_outermost": frappe.get_value("Package Type", {"edi_factcode": package.get("typeValue")}, "name") ,
                "package_count": package.get("count"),
                "hs_code": package.get("goodsClassificationValue") if package.get("goodsClassificationValue") else 400400,
                "origin_of_goods": "",  
                "cargo_description": package.get("goodsDescription"),
                "package_type_description": package.get("typeDescription"),
                "schedule_b_number": package.get("goodsClassificationSchedBValue"),
                "cus_code": package.get("goodsClassificationValue") or 400400,
                "cargo_gross_weight": (package.get("goodsGrossWeight") or {}).get("weightValue"),
                "net_weight": (package.get("goodsNetWeight") or {}).get("weightValue") if package.get("goodsNetWeight") else None,
                "net_weight_unit": (package.get("goodsNetWeight") or {}).get("weightType") if package.get("goodsNetWeight") else None,
                "gross_volume": (package.get("goodsGrossVolume") or {}).get("volumeValue") if package.get("goodsGrossVolume") else None,
                "gross_volume_unit": (package.get("goodsGrossVolume") or {}).get("volumeType") if package.get("goodsGrossVolume") else None,
                "print_on_bl_as": "",  
                "ncm_codes": "",        
                "marks_and_numbers": ", ".join(package.get("marksAndNumbers", [])) if package.get("marksAndNumbers") else None
            })
        
        if (response_data.get("transactionContact") or {}):
            str_telephone = ", ".join((response_data.get("transactionContact") or {}).get("phones")) if (response_data.get("transactionContact") or {}).get("phones") else None
            str_email = ", ".join((response_data.get("transactionContact") or {}).get("emails")) if (response_data.get("transactionContact") or {}).get("emails") else None

            doc_booking.append("additional_contacts", {
                "partner_role" : "Transaction Contact",
                "partner_name" : (response_data.get("transactionContact") or {}).get("name"),
                "contact_name" : (response_data.get("transactionContact") or {}).get("name"),
                "telephone": str_telephone[0:140] if str_telephone else None,
                "email" : str_email[0:140] if str_email else None,
                "address_line": None
            })
        
        doc_booking.insert(ignore_permissions=True)
        frappe.db.commit()
       
        booking_id = doc_booking.name
        temp_data = {}
        
        if isinstance (response_data.get("siDueDate"),dict):
            try: 
                si_date = parse_date_field(response_data.get("siDueDate")) if response_data.get("siDueDate") else None
            except:
                si_date = None
        else:
            si_date = None
            
        if isinstance(response_data.get("vgmDueDate"),dict):
            try:
                vgm_date = parse_date_field(response_data.get("vgmDueDate")) if response_data.get("vgmDueDate") else None
            except:
                vgm_date = None
        else:
            vgm_date = None

        if si_date:
            temp_data["si_due_date"] = si_date

        if vgm_date:
            temp_data["vgm_due_date"] = vgm_date

        channel = response_data.get('channel')
        if channel:
            temp_data["product_channel"] = channel

        booking_state = response_data.get('bookingState')
        if booking_state:
            temp_data["booking_status"] = booking_state

        carrier_booking_number = response_data.get('carrierReferenceNumber')
        if booking_state:
            temp_data["carrier_booking_number"] = carrier_booking_number

        general_comments = response_data.get('generalComments')
        if general_comments:
            temp_data["general_comments"] = ("\n".join(general_comments) if isinstance(general_comments, list) else general_comments)

        if response_data.get('split'):
            temp_data["split"]  = True
        temp_data["inttra_response_status"]  = 200 

        if booking_id:
            bkr_update_doc = frappe.get_doc("Booking Request", booking_id)
            bkr_update_doc.update(temp_data)
            bkr_update_doc.save(ignore_permissions=True)
            frappe.db.commit()

        if response_data.get("transportLegs"):

            update_booking_carriage = update_booking_request((response_data.get("transportLegs") or []), booking_id,(response_data.get("transactionLocations") or []))
            if not update_booking_carriage:
                return "Error updating booking carriages."
        
        container_summary_map = {}  # key = sizeCodeValue, value = count
        doc_container_type_map = {}
        cy_date_map = {}
        if response_data.get("equipments"):
            for container in response_data["equipments"]:
                size_code_value = container.get("equipmentSizeCode", {}).get("sizeCodeValue")
                size_code_desc = container.get("equipmentSizeCode", {}).get("sizeCodeDescription")
                count = int(container.get("count", 0))

                if size_code_value:
                    container_summary_map.setdefault(size_code_value, {"count": 0, "desc": size_code_desc})
                    container_summary_map[size_code_value]["count"] += count

                try:
                    if container.get('haulage', {}).get("points"):
                        for point in container['haulage']['points']:
                            if point.get("haulageParty", {}).get("partyRole") == "FullDropOFF":
                                for date in point.get("dates", []):
                                    if date.get("haulageDateType") == "ClosingDate":
                                        cy_date_map[size_code_value] = parse_date_field(date)
                except:
                    pass

        for typecode, summary in container_summary_map.items():
            doc_container_type = frappe.get_value(
                "Container Type", {"typecode": typecode},
                ["name", "shortdescription", "typecode"], as_dict=True
            )
            doc_container_type_map[typecode] = doc_container_type or {}

            doc_container_type_name = doc_container_type_map[typecode].get("name", "")
            doc_container_type_description = doc_container_type_map[typecode].get("shortdescription", "")

            booking_container_name = frappe.get_value("Booking Container", {
                "parent": booking_id,
                "container_descp": doc_container_type_description
            }, "name")

            if booking_container_name:
                frappe.db.set_value("Booking Container", booking_container_name, {
                    "container_descp": summary["desc"],
                    "number_of_containers": summary["count"],
                    "container_quantitytype": doc_container_type_name,
                })
            else:
                doc = frappe.get_doc("Booking Request", booking_id)
                doc.append("containers", {
                    "container_descp": summary["desc"],
                    "number_of_containers": summary["count"],
                    "container_quantitytype": doc_container_type_name,
                })
                doc.save()
            frappe.db.commit()
        for container in response_data.get("equipments") or []:
            size_code = container.get("equipmentSizeCode", {})
            net_weight = container.get("netWeight", {})
            size_code_value = size_code.get("sizeCodeValue") or None
            container_name = container.get('identifierValue')
            haulage = container.get('haulage')
            haulage_json = json.dumps(haulage) if haulage else None
            container_comments = container.get('comments')[0] if isinstance(container.get('comments'), list) and container.get('comments') else None

            doc_equipments = None
            if container_name:
                doc_equipments = frappe.get_value("Equipments", {
                    "booking_request": booking_id,
                    "equipment_name": container_name
                }, "name")
                if not doc_equipments:
                    empty_eqs = frappe.get_all("Equipments", filters={
                        "booking_request": booking_id,
                        "equipment_name": ""
                    }, fields=["name"])
                    doc_equipments = empty_eqs[0]["name"] if empty_eqs else None
            else:
                existing_eqs = frappe.get_all("Equipments", filters={"booking_request": booking_id}, fields=["name"])
                total_existing = len(existing_eqs)
                total_required = container.get("count", 1)
                if total_existing < int(total_required):
                    doc_equipments = None

            if doc_equipments:
                frappe.db.set_value("Equipments", doc_equipments, {
                    "count": container.get("count"),
                    "equipment_name": container_name,
                    "inttra_booking_number": response_data.get("inttraReference") or None,
                    "carrier_booking_number": response_data.get("carrierReferenceNumber") or None,
                    "supplier_type": container.get("supplierType") or None,
                    "service_type": container.get("serviceType") or None,
                    "code_value": size_code_value ,
                    "description": size_code.get("sizeCodeDescription") if size_code else None,
                    "weight_value": net_weight.get("weightValue") if net_weight else None,
                    "weight_type": net_weight.get("weightType") if net_weight else None,
                    "comment": container_comments or None,
                    "is_active": 1,
                    "response_haulage_details": haulage_json or None
                })
            elif not container_name and int(container.get('count', 1)) > 1:
                for _ in range(0, int(container.get("count"))):
                    equipment = frappe.new_doc("Equipments")
                    equipment.booking_request = booking_id
                    equipment.count = 1
                    equipment.inttra_booking_number = response_data.get("inttraReference") or None
                    equipment.carrier_booking_number = response_data.get("carrierReferenceNumber") or None
                    equipment.equipment_name = container_name
                    equipment.supplier_type = container.get("supplierType") or None
                    equipment.service_type = container.get("serviceType") or None
                    equipment.code_value = size_code_value
                    equipment.description = size_code.get("sizeCodeDescription") if size_code else None
                    equipment.weight_value = net_weight.get("weightValue") if net_weight else None
                    equipment.weight_type = net_weight.get("weightType") if net_weight else None
                    equipment.comment = container_comments or None
                    equipment.is_active = 1
                    equipment.response_haulage_details = haulage_json or None
                    equipment.insert(ignore_permissions=True)
            else:
                equipment = frappe.new_doc("Equipments")
                equipment.booking_request = booking_id
                equipment.count = container.get("count") or None
                equipment.inttra_booking_number = response_data.get("inttraReference") or None
                equipment.carrier_booking_number = response_data.get("carrierReferenceNumber") or None
                equipment.equipment_name = container_name
                equipment.supplier_type = container.get("supplierType") or None
                equipment.service_type = container.get("serviceType") or None
                equipment.code_value = size_code_value
                equipment.description = size_code.get("sizeCodeDescription") if size_code else None
                equipment.weight_value = net_weight.get("weightValue") if net_weight else None
                equipment.weight_type = net_weight.get("weightType") if net_weight else None
                equipment.comment = container_comments or None
                equipment.is_active = 1
                equipment.response_haulage_details = haulage_json or None
                equipment.insert(ignore_permissions=True)
            frappe.db.commit()

        str_pending_reasons = safe_text_formate((response_data.get("pendingReason") or None))
        str_carrier_terms_and_conditions = safe_text_formate((response_data.get("carrierTermsAndConditions") or None))
        str_split_reason = safe_text_formate((response_data.get("splitDetails") or {}).get("splitReason"))
        str_split_comment = safe_text_formate((response_data.get("splitDetails") or {}).get("comments"))
        str_split_description = safe_text_formate((response_data.get("splitDetails") or {}).get("description"))
        str_pending_description = safe_text_formate(response_data.get("pendingDescription") or None)
        str_hazardous_summary = safe_text_formate(response_data.get("carrierHazardousSummary") or None)
        str_vessel_rate_of_exchange = safe_text_formate(response_data.get("vesselRateOfExchange") or None)
        str_carrier_reason_for_change = safe_text_formate(response_data.get("carrierReasonForChange") or None)
        frappe.db.set_value("Booking Request", booking_id, {
                "cy_date": cy_date_map.get(typecode) if cy_date_map and typecode else None,
                "pending_reasons": str_pending_reasons,
                "pending_description": str_pending_description,
                "carrier_hazardous_summary": str_hazardous_summary,
                "carrier_terms_and_conditions": str_carrier_terms_and_conditions,
                "split_reason": str_split_reason,
                "comment": str_split_comment,
                "description" : str_split_description,
                "vessel_rate_of_exchange" : str_vessel_rate_of_exchange,
                "carrier_reason_for_change" : str_carrier_reason_for_change
            })
        
        frappe.db.commit()
        return True

    except Exception as e:
        
        tb = traceback.format_exc()
        frappe.log_error(tb, title="Error creating split booking")
        return {
            "status_code": 500,
            "message": f"Error creating split booking: {str(e)} on line {tb.splitlines()[-1].split(':')[-1].strip()}"
        }
    

def safe_text_formate(rst_terms):
    try:
        str_formated_data = None
        if rst_terms:
            if isinstance(rst_terms, list):
                str_formated_data = ", ".join(rst_terms)
            elif isinstance(rst_terms, dict):
                str_formated_data = frappe.as_json(rst_terms)
            elif isinstance(rst_terms, str):
                str_formated_data = rst_terms
                
        return str_formated_data
    except Exception as e:
        frappe.log_error(f"Error parsing carrierTermsAndConditions: {e}")
        str_formated_data = None
        return str_formated_data


def location_data(location):
    """
    Get location data based on the location name.
    """
    try:
        location_data = frappe.get_all(
            "UNLOCODE Locations",
            filters={"locode": ["in", [location]]},
            fields=["name", "locode", "country_code","country","location_name"]
            )
        return location_data
    except Exception as e:
        frappe.log_error(f"Error fetching location data: {str(e)}")
        return None



def update_booking_request(legs, booking_id, transaction_locations):
    try:
        if legs:
            int_order_main = 0
            int_order_on = 0
            int_order_pre = 0
            lst_place_of_receipt = []
            lst_place_of_delivary = []
            lst_eta = []
            lst_etd = []

            lst_place_of_receipt_pre = []
            lst_place_of_delivary_pre = []
            lst_eta_pre = []
            lst_etd_pre = []
            lst_eta_on = []
            frappe.db.delete("Booking Main Carriage", {"parent": booking_id,"parentfield": "main_carriage"})
            frappe.db.delete("Booking Carriage", {"parent": booking_id,"parentfield": "add_pre_carriage"})
            frappe.db.delete("Booking Carriage", {"parent": booking_id,"parentfield": "add_on_carriage"})

            for leg in legs:

                start_location = (leg.get('startLocation') or {}).get('identifierValue')
                if start_location:
                    port_of_load = location_data(start_location)
                
                end_location = (leg.get('endLocation') or {}).get('identifierValue')
                if end_location:
                    port_of_discharge = location_data(end_location) 

                
                dct_start_date = leg.get('startLocation') or {}
                start_date = dct_start_date.get('locationDates')
                if isinstance(start_date, list) and start_date:
                    start_date = start_date[0]
                else:
                    start_date = None
                ETD = parse_date_field(start_date) if start_date else None

                dct_end_date = leg.get('endLocation') or {}
                end_date = dct_end_date.get('locationDates')

                if isinstance(end_date, list) and end_date:
                    end_date = end_date[0]
                else:
                    end_date = None
                ETA = parse_date_field(end_date) if end_date else None

                
                if leg.get("stage") == "MainCarriage":

                    booking_main_carriages = frappe.get_all(
                        "Booking Main Carriage",
                        filters={"parent": booking_id,"port_of_load": port_of_load[0].get('name')},
                        pluck="name"
                    )

                    
                    if booking_main_carriages:
                        for data in booking_main_carriages:
                            int_order_main += 1
                            frappe.db.set_value("Booking Main Carriage", data, {
                                "parentfield": "main_carriage",
                                "lloyds_code": leg.get('lloydsCode'),
                                "port_of_load": port_of_load[0].get('name') if port_of_load else None,
                                "port_of_discharge": port_of_discharge[0].get('name') if port_of_discharge else None,
                                "etd": ETD,
                                "eta": ETA,
                                "carrier": leg.get('carrierScac'),
                                "vessel": leg.get('vesselName'),
                                "voyage": leg.get('conveyanceNumber'),
                                "mean": leg.get('means') if leg.get('means') else None,
                                "mode": MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None,
                                "country": leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None,
                                "order": int_order_main
                            })
                            lst_place_of_receipt.append(port_of_load[0].get('name')) 
                            lst_place_of_delivary.append(port_of_discharge[0].get('name'))
                            lst_eta.append(ETA)
                            lst_etd.append(ETD)
                    else:                        
                        booking_main_carriage = frappe.new_doc("Booking Main Carriage")
                        booking_main_carriage.parent = booking_id
                        booking_main_carriage.parenttype = "Booking Request"
                        booking_main_carriage.parentfield = "main_carriage"
                        booking_main_carriage.port_of_load = port_of_load[0].get('name') if port_of_load else None
                        booking_main_carriage.port_of_discharge = port_of_discharge[0].get('name') if port_of_discharge else None
                        booking_main_carriage.etd = ETD 
                        booking_main_carriage.eta = ETA
                        booking_main_carriage.lloyds_code = leg.get('lloydsCode')
                        booking_main_carriage.carrier = leg.get('carrierScac')
                        booking_main_carriage.vessel = leg.get('vesselName')
                        booking_main_carriage.voyage = leg.get('conveyanceNumber')
                        booking_main_carriage.mean = leg.get('means') if leg.get('means') else None
                        booking_main_carriage.mode = MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None
                        booking_main_carriage.country = leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None
                        booking_main_carriage.order = int_order_main
                        booking_main_carriage.insert(ignore_permissions=True)
                        frappe.db.commit()
                        lst_place_of_receipt.append(port_of_load[0].get('name')) 
                        lst_place_of_delivary.append(port_of_discharge[0].get('name'))
                        lst_eta.append(ETA)
                        lst_etd.append(ETD)
                elif leg.get("stage") == "PreCarriage":
                    booking_pre_carriages = frappe.get_all(
                        "Booking Carriage",
                        filters={"parent": booking_id,"start": port_of_load[0].get('name'),'parentfield': "add_pre_carriage"},
                        pluck="name"
                    )
                    if booking_pre_carriages:
                        for data in booking_pre_carriages:
                            int_order_pre += 1
                            frappe.db.set_value("Booking Carriage", data, {
                                'parentfield': "add_pre_carriage",
                                "start" : port_of_load[0].get('name') if port_of_load else None,
                                "end" : port_of_discharge[0].get('name') if port_of_discharge else None,
                                "etd": ETD,
                                "eta": ETA,
                                "mode" : MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None,
                                "mean": leg.get('means') if leg.get('means') else None,
                                "vessal_name": leg.get('vesselName'),
                                "carrier_code": leg.get('carrierScac'),
                                "voyage": leg.get('conveyanceNumber'),
                                "country": leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None,
                                "order": int_order_pre
                            })
                        lst_place_of_receipt_pre.append(port_of_load[0].get('name'))
                        lst_place_of_delivary_pre.append(port_of_discharge[0].get('name'))
                        lst_eta_pre.append(ETA)
                        lst_etd_pre.append(ETD)
                    else:
                        
                        booking_pre_carriage = frappe.new_doc("Booking Carriage")
                        booking_pre_carriage.parent = booking_id
                        booking_pre_carriage.parenttype = "Booking Request"
                        booking_pre_carriage.parentfield = "add_pre_carriage"
                        booking_pre_carriage.start = port_of_load[0].get('name') if port_of_load else None
                        booking_pre_carriage.end = port_of_discharge[0].get('name') if port_of_discharge else None
                        booking_pre_carriage.etd = ETD 
                        booking_pre_carriage.eta = ETA 
                        booking_pre_carriage.mode = MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None
                        booking_pre_carriage.mean = leg.get('means') if leg.get('means') else None
                        booking_pre_carriage.vessal_name = leg.get('vesselName')
                        booking_pre_carriage.carrier_code = leg.get('carrierScac')
                        booking_pre_carriage.voyage = leg.get('conveyanceNumber'),
                        booking_pre_carriage.country = leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None
                        booking_pre_carriage.order = int_order_pre
                        booking_pre_carriage.insert(ignore_permissions=True)
                        frappe.db.commit()
                        lst_place_of_receipt_pre.append(port_of_load[0].get('name'))
                        lst_place_of_delivary_pre.append(port_of_discharge[0].get('name'))
                        lst_eta_pre.append(ETA)
                        lst_etd_pre.append(ETD)
                elif leg.get("stage") == "OnCarriage":
                    booking_on_carriages = frappe.get_all(
                        "Booking Carriage",
                        filters={"parent": booking_id,"start": port_of_load[0].get('name'),'parentfield': "add_on_carriage"},
                        pluck="name"
                    )
                    if booking_on_carriages:
                        for data in booking_on_carriages:
                            int_order_on += 1
                            frappe.db.set_value("Booking Carriage", data, {
                                'parentfield': "add_on_carriage",
                                "start" : port_of_load[0].get('name') if port_of_load else None,
                                "end" : port_of_discharge[0].get('name') if port_of_discharge else None,
                                "etd": ETD,
                                "eta": ETA,
                                "mode" : MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None,
                                "mean": leg.get('means') if leg.get('means') else None,
                                "vessal_name": leg.get('vesselName'),
                                "carrier_code": leg.get('carrierScac'),
                                "voyage": leg.get('conveyanceNumber'),
                                "country": leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None,
                                "order": int_order_on
                            })
                        lst_eta_on.append(ETA)
                    else:
                        
                        booking_on_carriage = frappe.new_doc("Booking Carriage")
                        booking_on_carriage.parent = booking_id
                        booking_on_carriage.parenttype = "Booking Request"
                        booking_on_carriage.parentfield = "add_on_carriage"
                        booking_on_carriage.start = port_of_load[0].get('name') if port_of_load else None
                        booking_on_carriage.end = port_of_discharge[0].get('name') if port_of_discharge else None
                        booking_on_carriage.etd = ETD 
                        booking_on_carriage.eta = ETA 
                        booking_on_carriage.mode = MODE_MAPPING.get(leg.get('mode')) if leg.get('mode') else None
                        booking_on_carriage.mean = leg.get('means') if leg.get('means') else None
                        booking_on_carriage.vessal_name = leg.get('vesselName')
                        booking_on_carriage.carrier_code = leg.get('carrierScac')
                        booking_on_carriage.voyage = leg.get('conveyanceNumber'),
                        booking_on_carriage.country = leg.get('vesselFlagCountry').get('countryCodeValue') if leg.get('vesselFlagCountry') else None
                        booking_on_carriage.order = int_order_on
                        booking_on_carriage.insert(ignore_permissions=True)
                        frappe.db.commit()
                        lst_eta_on.append(ETA)
            
        if lst_place_of_receipt:
            doc = frappe.get_doc("Booking Request", booking_id)
            if lst_place_of_receipt_pre:
                doc.place_of_carrier_receipt = lst_place_of_receipt_pre[0]
                doc.earliest_departure_date = lst_etd_pre[0]
            else:
                doc.place_of_carrier_receipt = lst_place_of_receipt[0]
                doc.earliest_departure_date = lst_etd[0]
            doc.place_of_carrier_delivery = lst_place_of_delivary[-1]
            doc.latest_delivery_date = lst_eta[-1]
            doc.save()
            frappe.db.commit()

        if transaction_locations:
            for location in transaction_locations:
                if location.get("locationType") == "FirstForeignPortOfAcceptance":
                    if location.get("identifierValue"):
                        lst_location = frappe.get_all(
                                "UNLOCODE Locations",
                                filters = {"locode": location.get("identifierValue")}, 
                                fields=["name", "locode", "country_code","country","location_name"]
                            )                        
                        if lst_location:
                            dct_lcoation = lst_location[0]
                            frappe.db.set_value("Booking Request", booking_id, {
                                "first_foreign_port_of_acceptance": f"{dct_lcoation.get('locode')},{dct_lcoation.get('location_name')},{dct_lcoation.get('country')},({dct_lcoation.get('country_code')})"
                            })
                elif location.get("locationType") == "LastNonUSPort":
                    if location.get("identifierValue"):
                        lst_location = frappe.get_all(
                                "UNLOCODE Locations",
                                filters = {"locode": location.get("identifierValue")}, 
                                fields=["name", "locode", "country_code","country","location_name"]
                            )                        
                        if lst_location:
                            dct_lcoation = lst_location[0]
                            frappe.db.set_value("Booking Request", booking_id, {
                                "last_non_us_port": f"{dct_lcoation.get('locode')},{dct_lcoation.get('location_name')},{dct_lcoation.get('country')},({dct_lcoation.get('country_code')})"
                            })
                elif location.get("locationType") == "FinalPortForAMSDocumentation":
                    
                    if location.get("identifierValue"):
                        ams_date = location.get('locationDates')[0] if location.get('locationDates')  else None
                        if ams_date:
                            str_date = parse_date_field(ams_date) 
                            
                        lst_location = frappe.get_all(
                                "UNLOCODE Locations",
                                filters = {"locode": location.get("identifierValue")}, 
                                fields=["name", "locode", "country_code","country","location_name"]
                            )                        
                        if lst_location:
                            dct_lcoation = lst_location[0]
                            frappe.db.set_value("Booking Request", booking_id, {
                                "final_port_for_ams_documentation": f"{dct_lcoation.get('locode')},{dct_lcoation.get('location_name')},{dct_lcoation.get('country')},({dct_lcoation.get('country_code')})",
                                "ams_filing_due_date": str_date
                            })
                elif location.get("locationType") == "FirstUSPort":
                    arrival_date = location.get('locationDates')[0] if location.get('locationDates')  else None
                    if arrival_date:
                        str_date = parse_date_field(arrival_date) 
                    if location.get("identifierValue"):
                        lst_location = frappe.get_all(
                                "UNLOCODE Locations",
                                filters = {"locode": location.get("identifierValue")}, 
                                fields=["name", "locode", "country_code","country","location_name"]
                            )                        
                        if lst_location:
                            dct_lcoation = lst_location[0]
                            frappe.db.set_value("Booking Request", booking_id, {
                                "first_us_port": f"{dct_lcoation.get('locode')},{dct_lcoation.get('location_name')},{dct_lcoation.get('country')},({dct_lcoation.get('country_code')})",
                                "estimated_arrival_date": str_date
                            })
        return True

    except Exception as e:
        frappe.log_error(f"Error updating booking carrages: {str(e)}")
        return False
    

def parse_date_field(date_obj):
    try:
        if not date_obj or not isinstance(date_obj, dict):
            return None

        date_value = date_obj.get("dateValue")
        date_format = date_obj.get("dateFormat")
        
        if not date_value:
            date_value = date_obj.get("text")
    
        if not date_value:
            return None

        format_map = {
            "CCYYMMDD": "%Y%m%d",
            "YYMMDD": "%y%m%d",
            "CCYYMMDDHHMM": "%Y%m%d%H%M",
            "YYMMDDHHMM": "%y%m%d%H%M",
            "CCYYMMDDHHMMSS": "%Y%m%d%H%M%S",
        }

        try:
            dt = None
            if date_format:
                strptime_format = format_map.get(date_format)        
                dt = datetime.datetime.strptime(date_value, strptime_format)        
            if not date_format:            
                for key,value in format_map.items():
                    try:
                        strptime_format = value
                        date_format = key
                        dt = datetime.datetime.strptime(date_value, strptime_format)
                        break
                    except:
                        continue       
            
            if "H" in date_format:
                if dt:
                    return dt.strftime("%Y-%m-%d %H:%M")
                else:
                    return None
            else:
                if dt:
                    return dt.strftime("%Y-%m-%d")
                else:
                    return None
            
        except Exception:
            return {
                "message":"error while doing in the date formating"
            }
    except Exception:
            return {
                "message":"error in date formating function"
            }