from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
import io
import os
import json
import datetime
import frappe
from westside.api.invoice_ocr import process_invoice_ocr

@frappe.whitelist(allow_guest=True)
def access_google_drive(hours=1):
    try:
        # === CONFIG ===
        SCOPES = ['https://www.googleapis.com/auth/drive.readonly']
        FOLDER_ID = '1g7JnNAzQmGGxlKQcQv5bbefYTLqdFusC'
        DOWNLOAD_DIR = './pdfs'
        DOWNLOADED_LOG = './downloaded_files.json'
        CREDENTIALS_PATH = frappe.get_site_path('private', 'client_secret.json')
        TOKEN_PATH = frappe.get_site_path('private', 'token.json')  

        # === AUTH ===
        creds = None
        if os.path.exists(TOKEN_PATH):
            creds = Credentials.from_authorized_user_file(TOKEN_PATH, SCOPES)
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(CREDENTIALS_PATH, SCOPES)
                creds = flow.run_local_server(host="0.0.0.0", port=8888, access_type='offline', prompt='consent')
            # Save the credentials for future runs
            with open(TOKEN_PATH, 'w') as token_file:
                token_file.write(creds.to_json())

        drive_service = build('drive', 'v3', credentials=creds)

        # === Ensure download directory exists ===
        if not os.path.exists(DOWNLOAD_DIR):
            os.makedirs(DOWNLOAD_DIR)

        # === Load downloaded file IDs log ===
        if os.path.exists(DOWNLOADED_LOG):
            with open(DOWNLOADED_LOG, 'r') as f:
                downloaded_files = set(json.load(f))
        else:
            downloaded_files = set()

        # === Get time 1 hour ago in RFC3339 format ===
        print(hours, "hours")
        now = datetime.datetime.utcnow()
        one_hour_ago = now - datetime.timedelta(hours=int(hours))
        one_hour_ago_iso = one_hour_ago.isoformat("T") + "Z"

        # === Query Drive for PDFs uploaded in last 1 hour ===
        query = (
            f"'{FOLDER_ID}' in parents and "
            f"mimeType='application/pdf' and "
            f"modifiedTime >= '{one_hour_ago_iso}'"
        )

        results = drive_service.files().list(
            q=query,
            fields="files(id, name, modifiedTime)",
            orderBy="modifiedTime desc"
        ).execute()

        files = results.get('files', [])
        print(files, "files")
        if not files:
            frappe.logger().info("No new PDF files found in last 1 hour.")
            return "✅ No new PDF files found."

        new_files_downloaded = 0

        for file in files:
            print(file["name"], "file")
            file_id = file['id']
            file_name = file['name']
            file_path = os.path.join(DOWNLOAD_DIR, file_name)

            if frappe.db.exists("Google Drive Invoice File", {"file_id": file_id}):
                frappe.logger().info(f"Skipping already downloaded file: {file_name}")
                continue

            frappe.logger().info(f"Downloading new file: {file_name}")
            request = drive_service.files().get_media(fileId=file_id)
            fh = io.FileIO(file_path, 'wb')
            downloader = MediaIoBaseDownload(fh, request)

            done = False
            while not done:
                status, done = downloader.next_chunk()
                frappe.logger().info(f"  Progress: {int(status.progress() * 100)}%")

            
            with open(file_path, 'rb') as f:
                file_doc = frappe.get_doc({
                    "doctype": "File",
                    "file_name": file_name,
                    "attached_to_doctype": "Google Drive Invoice File",
                    "attached_to_name": file_id, 
                    "content": f.read(),
                    "is_private": 0,
                }).insert(ignore_permissions=True)

            
            invoice_doc = frappe.get_doc({
                "doctype": "Google Drive Invoice File",
                "file_id": file_id,
                "file_name": file_name,
                "file_path": file_path,
                "downloaded_on": frappe.utils.now_datetime(),
                "file_link": file_doc.name  
            }).insert(ignore_permissions=True)

            
            file_doc.attached_to_name = invoice_doc.name
            file_doc.save(ignore_permissions=True)

            frappe.db.commit()
            new_files_downloaded += 1
            try:
                process_invoice_ocr(file_doc.name,invoice_doc.name)
            except Exception as e:
                frappe.log_error(f"Error in process_invoice_ocr: {str(e)}=={invoice_doc.name}")
                pass

        with open(DOWNLOADED_LOG, 'w') as f:
            json.dump(list(downloaded_files), f)

        msg = f"✅ Downloaded {new_files_downloaded} new PDF file(s)."
        frappe.logger().info(msg)
        return msg

    except Exception as e:
        error_message = f"❌ Error downloading files: {str(e)}"
        frappe.log_error("Google Drive Sync Error", error_message)
        return error_message
