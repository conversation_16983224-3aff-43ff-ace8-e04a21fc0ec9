import os
import re
import json
import fitz
import base64
import frappe
from openai import OpenAI
from datetime import datetime
from difflib import Sequence<PERSON>atcher
from frappe.utils.file_manager import save_file
from westside.westside.doctype.bill.bill import create_bill_from_ocr_data
from westside.www.Authentication.auth_decorators import role_required
from westside.www.OCR.utils import normalize_weight_unit, normalize_volume_unit



def fuzzy_match(text1, text2, threshold=0.6):
    """Helper function for fuzzy matching"""
    if not text1 or not text2:
        return 0
    return SequenceMatcher(None, text1.lower().strip(), text2.lower().strip()).ratio()


def clean_text(text):
    """Helper function to clean text for better matching"""
    if not text:
        return ""
    # Remove extra whitespace, newlines, and special characters
    text = re.sub(r'\s+', ' ', str(text).strip())
    text = re.sub(r'[^\w\s-]', '', text)
    return text.upper()


def verify_bol_data(extracted_data, reference_data):
    """
    Verify and match extracted BOL data against reference tables
    Returns matched objects or empty objects if no match found
    """
    # Initialize result with empty objects
    result = {
        "port_of_load": {"name": "", "locode": "", "country_code": "", "location_name": ""},
        "port_of_discharge": {"name": "", "locode": "", "country_code": "", "location_name": ""},
        "confidence_scores": {}
    }
    
    # Match Port of Load
    if extracted_data.get("origin_port"):
        port_name = clean_text(extracted_data["origin_port"])
        best_match = None
        best_score = 0
        
        for port in reference_data["port_of_load"]:
            # Check against location_name and locode
            location_score = fuzzy_match(port_name, clean_text(port.get("location_name", "")))
            locode_score = fuzzy_match(port_name, clean_text(port.get("locode", "")))
            
            score = max(location_score, locode_score)
            
            if score > best_score and score >= 0.7:
                best_score = score
                best_match = port
        
        if best_match:
            result["port_of_load"] = best_match
            result["confidence_scores"]["port_of_load"] = best_score
    
    # Match Port of Discharge
    if extracted_data.get("destination_port"):
        port_name = clean_text(extracted_data["destination_port"])
        best_match = None
        best_score = 0
        
        for port in reference_data["port_of_discharge"]:
            location_score = fuzzy_match(port_name, clean_text(port.get("location_name", "")))
            locode_score = fuzzy_match(port_name, clean_text(port.get("locode", "")))
            
            score = max(location_score, locode_score)
            
            if score > best_score and score >= 0.7:
                best_score = score
                best_match = port
        
        if best_match:
            result["port_of_discharge"] = best_match
            result["confidence_scores"]["port_of_discharge"] = best_score
    
    return result



def get_reference_data():
    """Get reference data from cache or database"""
    cache = frappe.cache()
    reference_data = cache.get_value("reference_data")

    if not reference_data:
        reference_data = {
            "port_of_load": frappe.get_all("UNLOCODE Locations", fields=['name', 'locode', 'country_code', 'location_name']),
            "port_of_discharge": frappe.get_all("UNLOCODE Locations", fields=['name', 'locode', 'country_code', 'location_name'])
        }
        cache.set_value("reference_data", reference_data, expires_in_sec=3600)  # 1 hour cache

    return reference_data



def extract_from_pdf_as_images(uploaded_file):
    """Extract data from PDF by converting to images first"""
    
    try:
        relative_path = uploaded_file.file_url.replace("/files/", "")
        # Build correct full path
        file_path = frappe.get_site_path("public", "files", relative_path)
        
        # Convert PDF to images
        doc = fitz.open(file_path)
        page_images = []
        
        for page_num in range(min(5, len(doc))):  # Limit to first 5 pages to avoid token limits
            page = doc[page_num]
            # Convert to high-quality PNG
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), alpha=False)  # 2x zoom, no alpha
            img_data = pix.tobytes("png")
            base64_img = base64.b64encode(img_data).decode('utf-8')
            page_images.append(base64_img)
        
        doc.close()
         # Clean up
        
        if not page_images:
            return {"status_code": 400, "message": "Could not extract images from PDF"}
        
        return call_openai_vision(page_images)

    except Exception as e:
        frappe.log_error(f"Error in extract_from_pdf_as_images: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error processing PDF: {str(e)}"
        }




def call_openai_vision(page_images):
    """Call OpenAI Vision API with images"""
    try:
        client = OpenAI(api_key=frappe.conf.get("openai_api_key"), timeout=120)
        
        # Create message content with text prompt and all page images
        message_content = [{"type": "text", "text": get_extraction_prompt()}]
        
        for img_base64 in page_images:
            message_content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{img_base64}",
                    "detail": "high"
                }
            })
        
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[{"role": "user", "content": message_content}],
            response_format={"type": "json_object"},
            max_tokens=4096,
            temperature=0
        )
        
        return json.loads(response.choices[0].message.content)
        
    except Exception as e:
        frappe.log_error(f"Error in call_openai_vision: {str(e)}")
        raise e



def get_extraction_prompt():
    """Get the standardized extraction prompt"""
    return  """ 
    You are an advanced OCR data extractor specialized in international shipping invoices (including Hapag-Lloyd and CMA CGM).
    Read the extracted OCR text and return only valid JSON containing all the fields listed below.
    Use exactly the same key names as mentioned. Do not modify, rename, or invent any key.
    If a value is not present, Return None.

    Extract and return data using only the following keys:
    invoice_number, 
    booking_id,
    bill_date, 
    due_date, 
    bol_number, 
    shipping_date,
    shipment_number, 
    customer_number, 
    service_contract_number, 
    document_type, 
    issuer_company, 
    issuer_address, 
    customer_company, 
    customer_address, 
    shipper, 
    consignee, 
    notify_party, 
    freight_forwarder, 
    contact_person, 
    contact_email, 
    contact_phone, 
    origin_port, 
    destination_port, 
    place_of_receipt, 
    place_of_delivery, 
    vessel_name, 
    voyage_number, 
    container_numbers, 
    cargo_description, 
    commodity_code, 
    quantity, 
    hs_code,
    package_type,
    line_items,
    total_amount,
    currency

    Each key must follow these conditions:

    Output only JSON — no extra text or commentary.

    Combine multi-line text (e.g., addresses).

    Dates in "YYYY-MM-DD" format.

    Currency values must be numeric strings with "currency" stored separately.

    Numbers like totals or unit prices should be kept as strings.

    Multi-value fields such as container numbers or URLs must be lists.

   
    When extracting:

    Detect and normalize all party names and addresses (issuer, customer, shipper, consignee, notify party).

    Identify and extract all port and vessel details (origin_port, destination_port, place_of_receipt, place_of_delivery, vessel_name, voyage_number).

    Extract all container IDs into a list under container_numbers.

    Capture all itemized cost lines like Seafreight, Marine Fuel Recover, Basic Ocean Freight, Documentation Fee, etc.
    For line_items, return an array of objects with:
    {
        "item_code": "string or null",
        "description": "string",
        "quantity": "string or null",
        "unit": "string or null (e.g., KG, PCS, UNITS)",
        "unit_price": "string or null",
        "currency": "string (e.g., USD, EUR)",
        "amount": "string",
        "tax_rate": "string or null",
        "tax_amount": "string or null"
    }

    Your final output must be a single JSON object that cleanly represents all extracted fields according to these rules.
    """




@frappe.whitelist(methods=["POST"])
def process_invoice_ocr(file_doc_name = None,docname = None):
    """
    Process the input pdf file and extract bill information using ocr.

    Returns:
        Document: Json of extracted information.
    """

    try:
        # Check if file exists in Frappe
        if not file_doc_name and not docname:
            return {"status": "error", "message": "No file document name provided"}
        if file_doc_name:
            file_doc = frappe.get_doc("File", file_doc_name)
        elif docname:
            file_doc = frappe.get_doc("File", {"attached_to_name": docname})
        print(file_doc,"file_doc")
        if not file_doc:
            return {"status": "error", "message": "File not found in Frappe storage"}

        # Extract data from PDF
        structured_data = extract_from_pdf_as_images(file_doc)

        # If extraction failed, return empty
        if not structured_data:
            return {"status": "error", "message": "OCR extraction failed", "data": {}}

        # Get reference data for verification
        reference_data = get_reference_data()

        try:
            verified_matches = verify_bol_data(structured_data, reference_data)

            if verified_matches["port_of_load"].get("locode") and verified_matches["confidence_scores"].get("port_of_load", 0) >= 0.8:
                # Replace port of load data with verified match
                structured_data["origin_port"] = verified_matches["port_of_load"]["name"] or None
                structured_data["origin_port_location"] = verified_matches["port_of_load"]["location_name"] or structured_data["port_of_load_location"]
                
            if verified_matches["port_of_discharge"].get("locode") and verified_matches["confidence_scores"].get("port_of_discharge", 0) >= 0.8:
                # Replace port of discharge data with verified match
                structured_data["destination_port"] = verified_matches["port_of_discharge"]["name"] or None
                structured_data["destination_port_location"] = verified_matches["port_of_discharge"]["location_name"] or structured_data["port_of_discharge_location"]
        
        except Exception as e:
            frappe.logger().error(f"Error in BOL data verification: {str(e)}")
            # Don't fail the entire process if verification fails
            structured_data["verification_error"] = str(e)
        
        file_data = {
            "file_url" : file_doc.file_url,
            "file_name" : file_doc.file_name,
            "attached_to_name": file_doc.attached_to_name
        }

        if structured_data:
            structured_data.update({"file_data": file_data})
            dct_data = structured_data
            resp = create_bill_from_ocr_data(dct_data)
            if resp.get("status_code") == 200:
                return {
                    "status": "success",
                    "message": "Bill OCR processed successfully",
                    "extracted_data": structured_data,
                    "file_data": file_data,
                }


        # print(structured_data)
        # return {
        #         "status": "success",
        #         "message": "Bill OCR processed successfully",
        #         "extracted_data": structured_data,
        #         "file_data": file_data

        #     }

    except Exception as e:
        frappe.log_error(f"Error in process_invoice_ocr: {str(e)}")
        return {"status": "error", "message": str(e)}




