<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>BILL</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }

        body {
            font-family: "Helvetica Neue", sans-serif;
            font-size: 11pt;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .invoice-container {
            width: 100%;
            padding: 0;
        }

        .header {
            border-bottom: 2px solid #178bcf;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        .header h2 {
            margin: 0;
            font-size: 24pt;
            color: #178bcf;
        }

        .company-info {
            font-size: 11pt;
            margin-top: 5px;
        }

        .section {
            margin-bottom: 30px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th {
            background-color: #e6f0f8;
            color: #178bcf;
            padding: 8px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #ccc;
        }

        td {
            padding: 8px;
            border-bottom: 1px dashed #ccc;
        }

        .right {
            text-align: right;
        }

        .summary td {
            border: none;
        }

        .summary tr td:first-child {
            color: gray;
        }

        .footer {
            font-size: 9pt;
            text-align: center;
            position: fixed;
            bottom: 20px;
            width: 100%;
            color: gray;
        }

        .watermark {
            position: fixed;
            top: 45%;
            left: -25%;
            width: 150%;
            text-align: center;
            font-size: 75px;
            color: #178bcf;
            opacity: 0.06;
            transform: rotate(-45deg) !important;
            z-index: 0;
            pointer-events: none;
            white-space: nowrap;
        }
    </style>
</head>

<body>

    <div class="invoice-container">
        <div class="header">
            <h2>BILL</h2>
            <div class="company-info">
                <strong>{{ doc.shipper_name or "Westside Exports LLC" }}</strong><br>
                {{ doc.shipper_address.replace('\n', '<br>') | safe }}<br>
                {{ doc.shipper_phone }}<br>
                {{ doc.shipper_email }}
            </div>
        </div>

        <div class="section">
            <table>
                <tr>
                    <td>
                        <strong>Vendor:</strong><br>
                        {{ doc.quickbooks_vendor_display_name or "" }}<br>
                        {% if doc.get('BillAddr') %}
                            {{ doc.BillAddr.get('Line1', '') }}<br>
                            {% if doc.BillAddr.get('Line2') %}{{ doc.BillAddr.Line2 }}<br>{% endif %}
                            {% if doc.BillAddr.get('Line3') %}{{ doc.BillAddr.Line3 }}<br>{% endif %}
                            {% if doc.BillAddr.get('Line4') %}{{ doc.BillAddr.Line4 }}<br>{% endif %}
                            {% if doc.BillAddr.get('City') %}{{ doc.BillAddr.City }}, {% endif %}
                            {% if doc.BillAddr.get('CountrySubDivisionCode') %}{{ doc.BillAddr.CountrySubDivisionCode }} {% endif %}
                            {% if doc.BillAddr.get('PostalCode') %}{{ doc.BillAddr.PostalCode }}{% endif %}
                            {% if doc.BillAddr.get('City') or doc.BillAddr.get('CountrySubDivisionCode') or doc.BillAddr.get('PostalCode') %}<br>{% endif %}
                            {{ doc.BillAddr.get('Country', '') }}
                        {% endif %}<br>
                        <!-- {{ doc.qb_vendor_email or "" }}<br>
                        {{ doc.qb_vendor_contact or "" }} -->
                    </td>
                    <td class="right">
                        <table>
                            <tr>
                                <td><strong>Bill Number:</strong></td>
                                <td>{{ doc.document_number or "" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Vendor Bill No:</strong></td>
                                <td>{{ doc.vendor_bill_no or "" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Bill Date:</strong></td>
                                <td>{{ doc.bill_date or "" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Due Date:</strong></td>
                                <td>{{ doc.due_date or "" }}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        {% if doc.category_details and doc.category_details|length > 0 and
        doc.category_details[0].get("quickbooks_category_id") %}
        <div class="section">
            <h4 style="color: #178bcf;">Category Details</h4>
            <table>
                <thead>
                    <tr>
                        <th>Category</th>
                        <th>Description</th>
                        <th class="right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in doc.category_details %}
                    <tr>
                        <td>{{ item.category or "" }}</td>
                        <td>{{ item.description or "" }}</td>
                        <td class="right">{{ item.amount or "" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        {% if doc.item_details and doc.item_details|length > 0 and doc.item_details[0].get("quickbooks_item_id") %}
        <div class="section">
            <h4 style="color: #178bcf;">Item Details</h4>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Qty</th>
                        <th>UOM</th>
                        <th>Rate</th>
                        <th class="right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in doc.item_details %}
                    <tr>
                        <td>{{ item.product_service or "" }}</td>
                        <td>{{ item.description or "" }}</td>
                        <td>{{ item.quantity or "" }}</td>
                        <td>{{ item.uom or "" }}</td>
                        <td>{{ item.rate or "" }}</td>
                        <td class="right">{{ item.amount or "" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <div class="section">
            <table class="summary">
                <tr>
                    <td>Total</td>
                    <td class="right">${{ doc.total_amount or "" }}</td>
                </tr>
                <tr>
                    <td>Paid</td>
                    <td class="right">${{ doc.paid_amount or 0 }}</td>
                </tr>
                <tr>
                    <td>Balance Due</td>
                    <td class="right">${{ doc.balance_amount }}</td>
                </tr>
            </table>
        </div>

        <div class="footer">
            Page 1 of 1
        </div>
        <!-- <div class="watermark">
            Westside Exports LLC
        </div> -->
    </div>
</body>

</html>